package com.ctrip.dcs.dsp.delay.carconfig;

import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
public enum CarConfigCode {

    INVENTORY_VALIDATOR_CONFIG("car.dsp.inventory_validator_config", "1.1.0"),
    DELAY_DSP_MATCH_TASK_CONFIG("car.dsp.match_task_config", "1.1.0"),
    ORDER_DIS_LIMIT("car.dsp.order_dis_limit_num", "1.1.0"),
    VIRTUAL_DSP_TAKEN_FAIL_LIMIT("car.dsp.delay_dsp_taken_fail_limit_conf", "2.2.0"),
    DELAY_DSP_TASK("car.dsp.delay_dsp_task", "1.1.0"),
    ORDER_MILEAGE_VALUE("car.dsp.order_mileage_value", "1.1.0"),
    DRIVER_PROFIT_DAY_BASELINE("car.dsp.driver_profit_day_baseline", "2.2.0"),
    LBS_BUFFER_CONFIG("car.dsp.lbs_buffer_config", "2.2.0"),
    DELAY_DSP_SUB_SKU("car.dsp.delay_dsp_sub_sku", "1.1.0"),
    HEAD_TAIL_LIMIT("car.dsp.head_tail_limit", "1.1.0"),
    ;

    private String code;

    private String version;

    CarConfigCode(String code, String version) {
        this.code = code;
        this.version = version;
    }
}
