package com.ctrip.dcs.dsp.delay.infrastructure.util;

import com.ctrip.dcs.dsp.delay.util.DateUtil;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;


public class ObjectUtil {

    public static Map<String, String> objectToMap(Object obj) throws IllegalAccessException {
        if (obj == null) {
            return Collections.emptyMap();
        }
        Map<String, String> map = new HashMap<>();
        Class<?> aClass = obj.getClass();
        while (aClass != null) {
            Field[] declaredFields = aClass.getDeclaredFields();
            for (Field field : declaredFields) {
                field.setAccessible(true);
                Class<?> type = field.getType();
                Object o = field.get(obj);
                if (o == null) {
                    map.put(field.getName(), null);
                    continue;
                }
                if (type == String.class) {
                    map.put(field.getName(), (String)field.get(obj));
                } else if (type == Integer.class || type == Long.class) {
                    map.put(field.getName(), String.valueOf(field.get(obj)));
                } else if (type == LocalDateTime.class) {
                    map.put(field.getName(), DateUtil.format((LocalDateTime)field.get(obj)));
                } else if (type == BigDecimal.class) {
                    map.put(field.getName(), ((BigDecimal)field.get(obj)).toString());
                }else if (type == Boolean.class){
                    map.put(field.getName(), ((Boolean)field.get(obj)).toString());
                } else if (type == List.class) {
                    StringJoiner stringJoiner = new StringJoiner(",");
                    ((List<String>)field.get(obj)).forEach(stringJoiner::add);
                    map.put(field.getName(), stringJoiner.toString());
                }
            }
            aClass = aClass.getSuperclass();
        }
        return map;
    }
}
