package com.ctrip.dcs.dsp.delay.service.impl

import com.ctrip.dcs.dsp.delay.carconfig.DelayTaskConfig
import com.ctrip.dcs.dsp.delay.carconfig.value.DelayDspTaskValue
import com.ctrip.dcs.dsp.delay.gateway.DriverGateway
import com.ctrip.dcs.dsp.delay.lock.DistributedLock
import com.ctrip.dcs.dsp.delay.model.DelayDspConfigInfoDTO
import com.ctrip.dcs.dsp.delay.model.DelayDspConfigResInfoDTO
import com.ctrip.dcs.dsp.delay.model.DelayDspTask
import com.ctrip.dcs.dsp.delay.model.Driver
import com.ctrip.dcs.dsp.delay.model.SupplyOrder
import com.ctrip.dcs.dsp.delay.qconfig.DSPScheduleMatchValueConfig
import com.ctrip.dcs.dsp.delay.qconfig.value.DSPScheduleMatchValueVO
import com.ctrip.dcs.dsp.delay.repository.DelayDspTaskRepository
import com.ctrip.dcs.dsp.delay.service.DispatchConfigService
import com.ctrip.dcs.dsp.delay.util.DateUtil
import org.assertj.core.util.Lists
import org.junit.Assert
import org.springframework.beans.factory.annotation.Autowired
import spock.lang.Specification
import spock.lang.Unroll

import static org.mockito.ArgumentMatchers.any
import static org.mockito.ArgumentMatchers.anyInt
import static org.mockito.ArgumentMatchers.anyInt
import static org.mockito.ArgumentMatchers.anyString
import static org.mockito.Mockito.when

/**
 * <AUTHOR>
 */
class DelayTaskServiceImplSpec extends Specification {

    def dispatchConfigService = Mock(DispatchConfigService)

    def delayDspTaskRepository = Mock(DelayDspTaskRepository)

    def dspScheduleMatchValueConfig = Mock(DSPScheduleMatchValueConfig)


    def driverGateway = Mock(DriverGateway)

    def delayTaskConfig = Mock(DelayTaskConfig)

    def lock = Mock(DistributedLock)

    def delayTaskService = new DelayTaskServiceImpl("dspScheduleMatchValueConfig": dspScheduleMatchValueConfig, "dispatchConfigService": dispatchConfigService, "delayDspTaskRepository": delayDspTaskRepository, "driverGateway": driverGateway, delayTaskConfig: delayTaskConfig, distributedLock: lock)

    @Unroll
    def "query and create task"() {
        given: "Mock数据"
        driverGateway.query(order.driverId) >> new Driver(cityId: 1, cityCode: "beijing_city", carTypeId: 117)
        delayDspTaskRepository.query(_) >> dbTask
        delayTaskConfig.get(_, _, _,_) >> config
        delayDspTaskRepository.save(_) >> 1
        lock.tryAcquire(_, _, _) >> true

        dspScheduleMatchValueConfig.getValue(_, _) >> null
        when: "执行校验方法"
        def res = delayTaskService.queryOrCreate(order)

        then: "验证校验结果"
        if (result == null) {
            res == null
        } else {
            res.getTaskId() == result.getTaskId()
        }

        where:
        order            | dbTask                      | config                  || result
        getSupplyOrder() | null                        | null                    || null
        getSupplyOrder() | new DelayDspTask()          | null                    || null
        getSupplyOrder() | null                        | getDelayDspTaskValue(1) || null
        getSupplyOrder() | new DelayDspTask(taskId: 1) | getDelayDspTaskValue(1) || dbTask
        getSupplyOrder() | null                        | getDelayDspTaskValue(1) || null
    }

    def "test query Delay Dsp Config List"() {
        given:
        DelayDspTaskValue value = new DelayDspTaskValue();
        value.setType("DP");
        value.setBegin("20:00");
        value.setEnd("08:59");
        value.setHour(10);
        value.setDeadline(7)
        delayTaskConfig.get(_, _, _, _) >> value

        when:
        List<DelayDspConfigInfoDTO> delayDspConfigInfoList = Lists.newArrayList();
        DelayDspConfigInfoDTO delayDspConfigInfoDTO = new DelayDspConfigInfoDTO();
        delayDspConfigInfoDTO.setCityId(1L);
        delayDspConfigInfoDTO.setCarTypeId(117);
        delayDspConfigInfoDTO.setSysExpectBookTime(new Date());
        delayDspConfigInfoList.add(delayDspConfigInfoDTO);
        List<DelayDspConfigResInfoDTO> result = delayTaskService.queryDelayDspConfigList(delayDspConfigInfoList)

        then:
        result != null
    }


    def getSupplyOrder() {
        return new SupplyOrder(cityId: 1, cityCode: "beijing_city", carTypeId: 117, sysExpectBookTime: new Date(), orderStatus: 3, driverId: "1")
    }

    def getDelayDspTaskValue(int hour) {
        def time = DateUtil.formatDate(new Date(), DateUtil.HOUR_MIN_FMT)
        return new DelayDspTaskValue(begin: time, end: time, hour: hour, deadline: hour - 3, type: "DP")
    }


    @Unroll
    def "query getMatchTaskTime"() {
        given: "Mock数据"

        dspScheduleMatchValueConfig.getValue(_, _) >> config
        when: "执行校验方法"
        def res = delayTaskService.getMatchTaskTime(delayDspTask)

        then: "验证校验结果"
        resultCode == res

        where:
        delayDspTask                                                                                                                                                                                            | config                                           || resultCode
        new DelayDspTask(executeTime: null)                                                                                                                                                                     | null                                             || 0
        new DelayDspTask(executeTime: DateUtil.parseDate("2024-01-01 12:00:00", "yyyy-MM-dd HH:mm:ss"), cityId: 1, carTypeId: 117)                                                                              | null                                             || 0
        new DelayDspTask(executeTime: DateUtil.parseDate("2024-01-01 12:00:00", "yyyy-MM-dd HH:mm:ss"), matchTime: DateUtil.parseDate("2024-02-01 12:00:00", "yyyy-MM-dd HH:mm:ss"), cityId: 1, carTypeId: 117) | new DSPScheduleMatchValueVO(21, 57, 303, "1", 8) || 0
        new DelayDspTask(executeTime: DateUtil.parseDate("2024-03-01 12:00:00", "yyyy-MM-dd HH:mm:ss"), matchTime: DateUtil.parseDate("2024-02-01 12:00:00", "yyyy-MM-dd HH:mm:ss"), cityId: 1, carTypeId: 117) | new DSPScheduleMatchValueVO(21, 57, 303, "1", 8) || 303
        new DelayDspTask(executeTime: DateUtil.parseDate("2024-03-01 16:00:00", "yyyy-MM-dd HH:mm:ss"), matchTime: DateUtil.parseDate("2024-03-01 01:00:00", "yyyy-MM-dd HH:mm:ss"), cityId: 1, carTypeId: 117) | new DSPScheduleMatchValueVO(10, 57, 303, "1", 8) || 342
        new DelayDspTask(executeTime: DateUtil.parseDate("2024-03-01 16:00:00", "yyyy-MM-dd HH:mm:ss"), matchTime: DateUtil.parseDate("2024-03-01 07:00:00", "yyyy-MM-dd HH:mm:ss"), cityId: 1, carTypeId: 117) | new DSPScheduleMatchValueVO(10, 57, 303, "1", 8) || 57
        new DelayDspTask(executeTime: DateUtil.parseDate("2024-03-01 16:00:00", "yyyy-MM-dd HH:mm:ss"), matchTime: DateUtil.parseDate("2024-03-01 15:20:00", "yyyy-MM-dd HH:mm:ss"), cityId: 1, carTypeId: 117) | new DSPScheduleMatchValueVO(10, 57, 303, "1", 8) || 0

    }


    @Unroll
    def "query buildTaskTemp"() {
        given: "Mock数据"
        driverGateway.query(order.driverId) >> new Driver(cityId: 1, cityCode: "beijing_city", carTypeId: 117)
        delayDspTaskRepository.query(_) >> fromDbTask
        delayTaskConfig.get(_, _, _,_) >> config
        delayDspTaskRepository.save(_) >> 1
        lock.tryAcquire(_, _, _) >> true

        dspScheduleMatchValueConfig.getValue(_, _) >> null
        when: "执行校验方法"
        def res = delayTaskService.buildTaskTemp(order)

        then: "验证校验结果"
        resultRes == Objects.isNull(res)

        where:
        order                           | fromDbTask         | config                            || resultRes
        new SupplyOrder(orderStatus: 2) | null               | null                              || true
        new SupplyOrder(orderStatus: 2) | new DelayDspTask() | new DelayDspTaskValue(type: "KM") || false
        new SupplyOrder(orderStatus: 2) | new DelayDspTask() | new DelayDspTaskValue(type: "DP") || false
        getSupplyOrder()                | new DelayDspTask() | new DelayDspTaskValue(type: "DP") || false

    }

    @Unroll
    def "query buildTaskTemp1"() {
        given: "Mock数据"
        driverGateway.query(order.driverId) >> null
        delayDspTaskRepository.query(_) >> fromDbTask
        delayTaskConfig.get(_, _, _,_) >> config
        delayDspTaskRepository.save(_) >> 1
        lock.tryAcquire(_, _, _) >> true

        dspScheduleMatchValueConfig.getValue(_, _) >> null
        when: "执行校验方法"
        def res = delayTaskService.buildTaskTemp(order)

        then: "验证校验结果"
        resultRes == Objects.isNull(res)

        where:
        order                           | fromDbTask         | config                            || resultRes
        getSupplyOrder()                | new DelayDspTask() | new DelayDspTaskValue(type: "DP") || true

    }


    @Unroll
    def "query updateTaskInfo"() {
        given:

        when:
        def updateRes = delayTaskService.updateTaskInfo(new DelayDspTask())
        then:
        Assert.assertTrue(Objects.isNull(updateRes));
    }
}
