package com.ctrip.dcs.dsp.delay.filter.impl;

import com.ctrip.dcs.dsp.delay.model.DelayDspTask;
import com.ctrip.dcs.dsp.delay.model.Driver;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 */
@Component("taskCarTypeFilter")
public class TaskCarTypeFilter extends CommonFilter {

    @Override
    public boolean filter(Driver driver, DelayDspTask task) {
        return Objects.equals(task.getCarTypeId(), driver.getCarTypeId());
    }

    @Override
    public String reason() {
        return "not equals car type";
    }
}
