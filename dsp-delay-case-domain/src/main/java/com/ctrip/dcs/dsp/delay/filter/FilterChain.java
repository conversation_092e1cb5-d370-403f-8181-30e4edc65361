package com.ctrip.dcs.dsp.delay.filter;

import com.ctrip.dcs.dsp.delay.model.DelayDspTask;
import com.ctrip.dcs.dsp.delay.model.Driver;
import com.ctrip.dcs.dsp.delay.qconfig.DelayDspCommonQConfig;
import com.ctrip.dcs.dsp.delay.util.JsonUtil;
import com.ctrip.dcs.dsp.delay.util.SpringApplicationContextUtil;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 */
@Component
public class FilterChain {

    private static final Logger logger = LoggerFactory.getLogger(FilterChain.class);

    @Autowired
    private DelayDspCommonQConfig delayDspCommonQConfig;

    public List<Driver> filter(List<Driver> drivers, DelayDspTask task) {
        List<Filter> filters = buildFilter();
        for (Filter filter : filters) {
            if (CollectionUtils.isEmpty(drivers)) {
                break;
            }
            try {
                // 过滤不能服务的司机
                drivers = filter.doFilter(drivers, task);
            } catch (Exception e) {
                logger.error("driver filter error!", e);
            }
        }
        return drivers;
    }

    private List<Filter> buildFilter() {
        List<String> filterNames = delayDspCommonQConfig.getDriverFilterList();
        logger.info("FilterChain_buildFuilter", JsonUtil.toJson(filterNames));
        List<Filter> filters = Lists.newArrayList();
        for (String name : filterNames) {
            try {
                Filter filter = SpringApplicationContextUtil.getBean(name, Filter.class);
                filters.add(filter);
            } catch (Exception e) {
                logger.error("FilterChain.buildFuilter", "there is no filter. name: {}, error: {}", name, e);
            }
        }
        return filters;
    }
}
