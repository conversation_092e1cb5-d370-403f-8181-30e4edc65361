package com.ctrip.dcs.dsp.delay.carconfig;

import com.ctrip.dcs.dsp.delay.util.JsonUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.codehaus.jackson.JsonNode;
import org.codehaus.jackson.type.TypeReference;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class CarConfig {

    private static final String VALUES_KEY = "values";

    private String uniqCode;

    private String configType;

    private String labType;

    private String version;

    private Schema schema;

    private String getRowValues(String key) {

        Map<String, SchemaData> map = Optional.ofNullable(schema)
                .map(Schema::getData)
                .orElse(Maps.newHashMap());
        SchemaData data = map.get(key);
        return Optional.ofNullable(data)
                .map(SchemaData::getRowValue)
                .orElse(StringUtils.EMPTY);
    }

    private <K extends Key, V> List<V> parseRowValues(K key, TypeReference type) {

        List<V> values = Lists.newArrayList();
        String rowValues = getRowValues(key.toKey());
        if (StringUtils.isBlank(rowValues)) {
            return values;
        }
        JsonNode node = JsonUtil.path(rowValues, VALUES_KEY);
        Object o = JsonUtil.fromJson(node, type);
        if (o instanceof List) {
            List list = (List) o;
            values.addAll(list);
        } else {
            V v = (V) o;
            values.add(v);
        }
        return values;
    }

    public <K extends Key, V> List<V> values(TypeReference type, K... keys) {

        List<V> values = Lists.newArrayList();
        for (K key : keys) {
            List<V> list = parseRowValues(key, type);
            values.addAll(list);
        }
        return values;
    }
}
