package com.ctrip.dcs.dsp.delay.infrastructure.util;

import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import org.apache.commons.lang3.StringUtils;

import java.security.MessageDigest;

/**
 * 生成token类
 */
public class TokenUtil {

    private static final Logger logger = LoggerFactory.getLogger(TokenUtil.class);

    /**
     * 盐
     */
    private static final String SALT = "7QE5mROd3g";

    /**
     * 空字符串
     */
    public static final String EMPTY = "";

    /**
     * 生成token方法
     *
     * @param originalKey
     * @return
     */
    public static String generateToken(String originalKey) {
        if (StringUtils.isEmpty(originalKey)) {
            logger.error("GenerateToken_Error", "originalKey is null");
            return EMPTY;
        }
        try {
            String userStr = originalKey + "_" + SALT;
            MessageDigest md5 = MessageDigest.getInstance("MD5");
            byte[] result = md5.digest(userStr.getBytes());
            String returnVal = byteArray2HexStr(result);
            logger.info("GenerateToken_" + originalKey, "MD5=" + returnVal);
            return returnVal;
        } catch (Exception e) {
            logger.error("GenerateToken_Error", e);
        }
        logger.warn("GenerateToken_" + originalKey, "generateToken,MD5=empty");
        return EMPTY;
    }

    /**
     * 处理字节数组得到MD5密码的方法
     */
    private static String byteArray2HexStr(byte[] bs) {
        StringBuffer sb = new StringBuffer();
        for (byte b : bs) {
            sb.append(byte2HexStr(b));
        }
        return sb.toString();
    }

    /**
     * 字节标准移位转十六进制方法
     */
    private static String byte2HexStr(byte b) {
        String hexStr = null;
        int n = b;
        if (n < 0) {
            // 若需要自定义加密,请修改这个移位算法即可
            n = b & 0x7F + 128;
        }
        hexStr = Integer.toHexString(n / 16) + Integer.toHexString(n % 16);
        return hexStr.toLowerCase();
    }

    /**
     * 提供一个测试的主函数
     */
    public static void main(String[] args) {
        System.out.println("123:" + generateToken("123"));
        System.out.println("123456789:" + generateToken("123456789"));
        System.out.println("123456789:" + generateToken(""));
    }
}
