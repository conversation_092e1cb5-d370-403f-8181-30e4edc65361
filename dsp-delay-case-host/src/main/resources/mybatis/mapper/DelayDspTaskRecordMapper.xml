<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ctrip.dcs.dsp.delay.infrastructure.mapper.DelayDspTaskRecordMapper">

    <resultMap id="BaseResultMap" type="com.ctrip.dcs.dsp.delay.model.DelayDspTaskRecord">
        <result column="id" property="id" />
        <result column="task_id" property="taskId" />
        <result column="order_id" property="orderId" />
        <result column="driver_id" property="driverId" />
        <result column="driver_score" property="driverScore" />
        <result column="order_seq" property="orderSeq" />
        <result column="taken_code" property="takenCode" />
        <result column="empty_distance" property="emptyDistance" />
        <result column="empty_duration" property="emptyDuration" />
        <result column="match_success" property="matchSuccess" />
        <result column="is_out" property="isOut" />
        <result column="pre_out" property="preOut" />
        <result column="match_record_id" property="matchRecordId" />
        <result column="driver_order_id" property="driverOrderId" />
        <result column="order_source" property="orderSource" />
    </resultMap>

    <sql id="Base_Column_List">
        id,
        task_id,
        order_id,
        driver_id,
        driver_score,
        order_seq,
        taken_code,
        empty_distance,
        empty_duration,
        match_success,
        is_out,
        pre_out,
        driver_order_id,
        order_source,
        match_record_id
    </sql>

    <insert id="insert">
        INSERT INTO delay_dsp_task_record (
            task_id,
            order_id,
            driver_id,
            driver_score,
            order_seq,
            taken_code,
            empty_distance,
            empty_duration,
            match_success,
            is_out,
            pre_out,
            driver_order_id,
            order_source,
            match_record_id,
            datachange_lasttime,
            datachange_createtime
        )
        VALUES
        <foreach collection="items" item="item" separator=",">
            (
                #{item.taskId},
                #{item.orderId},
                #{item.driverId},
                #{item.driverScore},
                #{item.orderSeq},
                #{item.takenCode},
                #{item.emptyDistance},
                #{item.emptyDuration},
                #{item.matchSuccess},
                #{item.isOut},
                #{item.preOut},
                #{item.driverOrderId},
                #{item.orderSource},
                #{item.matchRecordId},
                NOW(),
                NOW()
            )
        </foreach>
    </insert>

    <update id="update" parameterType="com.ctrip.dcs.dsp.delay.model.DelayDspTaskRecord">
        UPDATE delay_dsp_task_record
        <set>
            <if test="takenCode != null">
                taken_code = #{takenCode},
            </if>
            <if test="isOut != null">
                is_out = #{isOut},
            </if>
            datachange_lasttime = NOW()
        </set>
        WHERE
        id = #{id}
    </update>



    <update id="updateMatchStatus">
        UPDATE delay_dsp_task_record
        <set>
            match_success = #{matchSuccess},
            is_out = #{isOut}
        </set>
        WHERE
        id = #{recordId}
    </update>

    <select id="getDistinctTaskIdNotRedispatch" resultType="Long">
        SELECT distinct(task_id) FROM delay_dsp_task_record
        WHERE is_out = 0  AND match_success = 0
    </select>

    <select id="queryNotOut" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM delay_dsp_task_record
        WHERE
        is_out = 0
        AND match_success = #{matchSuccess}
        limit #{pageSize}
    </select>


    <select id="queryNotOutByTask" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM delay_dsp_task_record
        WHERE
        task_id in
        <foreach collection="redispatchTaskIds" item="taskId" open="(" separator="," close=")">
            #{taskId}
        </foreach>
        and
        is_out = 0
        AND match_success = 0
        limit #{pageSize}
    </select>


    <select id="queryCountNotOut" resultType="int">
        SELECT count(id) FROM delay_dsp_task_record
        WHERE is_out = 0 AND match_success = #{matchSuccess}
    </select>


    <select id="query" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM delay_dsp_task_record
        WHERE
        id = #{id}
    </select>

    <select id="queryByTaskId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM delay_dsp_task_record
        WHERE
        task_id = #{taskId}
        AND pre_out = #{preOut}
    </select>


    <select id="queryByDspOrderId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM delay_dsp_task_record
        WHERE
        order_id = #{dspOrderId}
    </select>

</mapper>