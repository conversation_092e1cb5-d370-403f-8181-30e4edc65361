package com.ctrip.dcs.dsp.delay.validator.impl;

import com.ctrip.dcs.dsp.delay.carconfig.SpecialDateConfig;
import com.ctrip.dcs.dsp.delay.carconfig.value.SpecialDateValue;
import com.ctrip.dcs.dsp.delay.model.SupplyOrder;
import com.ctrip.dcs.dsp.delay.service.DelayDspOrderService;
import com.ctrip.dcs.dsp.delay.util.DateUtil;
import com.ctrip.dcs.dsp.delay.util.JsonUtil;
import com.ctrip.dcs.dsp.delay.validator.Validator;
import com.ctrip.dcs.dsp.delay.validator.ValidatorCode;
import com.ctrip.dcs.dsp.delay.validator.ValidatorDTO;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024/8/12 11:33
 */
@Component("specialDateValidator")
public class SpecialDateValidator implements Validator {
    static Logger logger = LoggerFactory.getLogger(SpecialDateValidator.class);
    
    @Resource
    DelayDspOrderService delayDspOrderService;
    
    
    
    @Override
    public ValidatorDTO validate(SupplyOrder order) {
        String sysExpectBookTime = DateUtil.formatDate(order.getSysExpectBookTime(),DateUtil.DATE_FMT);
        Boolean isHit = delayDspOrderService.checkSpecialDate(order.getCityId(), order.getCarTypeId(), sysExpectBookTime);
        if (Objects.equals(isHit,Boolean.TRUE)) {
            return new ValidatorDTO(ValidatorCode.SPECIAL_DATE);
        }
        return new ValidatorDTO(ValidatorCode.OK);
    }
}

