package com.ctrip.dcs.dsp.delay.infrastructure.http

import com.ctrip.dcs.dsp.delay.infrastructure.soa.RhbDriverPointProxy
import com.ctrip.dcs.dsp.delay.model.DriverScore
import com.ctrip.dcs.dsp.driver.level.api.DriverPointDetailResp
import com.ctrip.dcs.dsp.driver.level.api.DspDriverPointDetail
import com.ctrip.igt.framework.common.clogging.Logger
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

/**
 * <AUTHOR>
 * @date 2023/4/7 14:57
 * @version V1.0
 */
class DriverScoreServiceProxyTest extends Specification {
    @Mock
    Logger logger
    @Mock
    RhbDriverPointProxy proxy
    @InjectMocks
    DriverScoreServiceProxy driverScoreServiceProxy

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test query Driver Score"() {
        given:
        when(proxy.queryDspDriverPointDetails(any())).thenReturn(resp)
        expect:
        driverScoreServiceProxy.queryDriverScore(["String"] as Set<String>).size() == size
        where:
        resp                                                                          || size
        null                                                                          || 0
        new DriverPointDetailResp(pointDetails: [new DspDriverPointDetail(drvId: 1,driverBaseScore: 1,carBaseScore: 1,orderServiceScore: 1,activeScore: 1,totalScore: 1)]) || 1


    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme