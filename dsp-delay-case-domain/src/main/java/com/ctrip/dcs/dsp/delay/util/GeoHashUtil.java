package com.ctrip.dcs.dsp.delay.util;

/**
 * <AUTHOR>
 */
public class GeoHashUtil {

    private static final int PRECISION_BIT = 28;

    private static char[] codeMap = new char[]{'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'j', 'k', 'm', 'n', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z'};

    public static String buildGeoHash(double longitude, double latitude) {
        return buildGeoHash(longitude, latitude, PRECISION_BIT);
    }

    public static String buildGeoHash(double longitude, double latitude, int precisionBit) {
        String mixCode = mixCode(buildLongitudeGeoHash(longitude, precisionBit), buildLatitudeGeoHash(latitude, precisionBit));
        return encodeGeoHashStr(mixCode);
    }

    private static String sandWich(double floor, double ceiling, double val, int precisionBit) {
        StringBuilder sb = new StringBuilder();

        for(int curPrecisionBit = 0; curPrecisionBit < precisionBit; ++curPrecisionBit) {
            double mid = (floor + ceiling) / 2.0D;
            if (val < mid) {
                sb.append("0");
                ceiling = mid;
            } else {
                sb.append("1");
                floor = mid;
            }
        }

        return sb.toString();
    }

    private static String buildLongitudeGeoHash(double longitude, int precisionBit) {
        return sandWich(-180.0D, 180.0D, longitude, precisionBit);
    }

    private static String buildLatitudeGeoHash(double latitude, int precisionBit) {
        return sandWich(-90.0D, 90.0D, latitude, precisionBit);
    }

    private static String mixCode(String longtitudeCode, String latitudeCode) {
        StringBuilder result = new StringBuilder();

        for(int i = 0; i < longtitudeCode.length(); ++i) {
            result.append(longtitudeCode.charAt(i));
            result.append(latitudeCode.charAt(i));
        }

        return result.toString();
    }

    private static String encodeGeoHashStr(String code) {
        int round = 0;
        StringBuilder builder = new StringBuilder();
        StringBuilder result = new StringBuilder();

        for(int i = code.length() - 1; i >= 0; --i) {
            char select = code.charAt(i);
            builder.insert(0, select);
            ++round;
            if (round == 5) {
                result.insert(0, codeMap[Integer.parseInt(builder.toString(), 2)]);
                builder = new StringBuilder();
                round = 0;
            }
        }

        if (round > 0) {
            result.insert(0, codeMap[Integer.parseInt(builder.toString(), 2)]);
        }

        return result.toString();
    }

}
