package com.ctrip.dcs.dsp.delay.model;

public class PreOutPoolDTO {

    /**
     * 用户单号，必传
     */
    public String userOrderId;

    /**
     * 催派来源1：用户 2、客服，必传
     */
    public Integer pressedDispatchSource;

    /**
     * 客服工号,pressedDispatchSource为2必传
     */
    public String customerServiceUid;

    public String getUserOrderId() {
        return userOrderId;
    }

    public void setUserOrderId(String userOrderId) {
        this.userOrderId = userOrderId;
    }

    public Integer getPressedDispatchSource() {
        return pressedDispatchSource;
    }

    public void setPressedDispatchSource(Integer pressedDispatchSource) {
        this.pressedDispatchSource = pressedDispatchSource;
    }

    public String getCustomerServiceUid() {
        return customerServiceUid;
    }

    public void setCustomerServiceUid(String customerServiceUid) {
        this.customerServiceUid = customerServiceUid;
    }
}
