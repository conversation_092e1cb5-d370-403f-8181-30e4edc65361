package com.ctrip.dcs.dsp.delay.validator.impl

import com.ctrip.dcs.dsp.delay.gateway.SelfDispatcherOrderGateway
import com.ctrip.dcs.dsp.delay.model.SupplyOrder
import com.ctrip.dcs.dsp.delay.validator.ValidatorCode
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 */
class OrderRedispatchValidatorTest extends Specification {

    SelfDispatcherOrderGateway selfDispatcherOrderGateway = Mock(SelfDispatcherOrderGateway)

    OrderRedispatchValidator validator = new OrderRedispatchValidator(selfDispatcherOrderGateway: selfDispatcherOrderGateway)

    @Unroll
    def "validate"() {

        given: "Mock数据"
        SupplyOrder order = new SupplyOrder(sourceOrderId: "1")
        selfDispatcherOrderGateway.isRedispatch("1") >> isRedispatch

        when: "执行校验方法"
        def res = validator.validate(order)

        then: "验证校验结果"
        res.getCode() == code

        where:
        isRedispatch || code
        false        || ValidatorCode.OK
        true         || ValidatorCode.REDISPATCH
    }
}
