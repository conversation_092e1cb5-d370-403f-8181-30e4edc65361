package com.ctrip.dcs.dsp.delay.interfaces.schedule;

import com.ctrip.dcs.dsp.delay.consts.CommonConstant;
import com.ctrip.dcs.dsp.delay.lock.DistributedLock;
import com.ctrip.dcs.dsp.delay.model.DelayDspOrder;
import com.ctrip.dcs.dsp.delay.model.DelayDspTask;
import com.ctrip.dcs.dsp.delay.mq.MessageProducer;
import com.ctrip.dcs.dsp.delay.repository.DelayDspOrderRepository;
import com.ctrip.dcs.dsp.delay.repository.DelayDspTaskRepository;
import com.ctrip.dcs.dsp.delay.util.DateUtil;
import com.ctrip.dcs.dsp.delay.util.JsonUtil;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerContext;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qschedule.config.QSchedule;
import qunar.tc.schedule.Parameter;

import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * 提前计算lbs到缓存中，任务匹配时增加性能
 * <AUTHOR>
 */
@Component
public class AdjacencyRouteSchedule {

    private static final Logger logger = LoggerFactory.getLogger(AdjacencyRouteSchedule.class);

    @Autowired
    private DelayDspTaskRepository delayDspTaskRepository;

    @Autowired
    private DelayDspOrderRepository delayDspOrderRepository;

    @Autowired
    private MessageProducer messageProducer;

    @Autowired
    private DistributedLock distributedLock;

    @QSchedule("dcs.dsp.delay.task.adjacency.route.task")
    public void execute(Parameter parameter) {
        LoggerContext.newContext().withGlobalTraceId(UUID.randomUUID().toString()).init();
        // 查询30分钟内待执行的任务
        List<DelayDspTask> tasks = delayDspTaskRepository.queryByTime(DateUtil.addMinutes(new Date(), 60));
        logger.info("AdjacencyRouteSchedule_execute", JsonUtil.toJson(tasks));
        tasks.sort(Comparator.comparing(DelayDspTask::getExecuteTime));
        for (DelayDspTask task : tasks) {
            String key = CommonConstant.APP_ID + CommonConstant.DCS_DSP_DELAY_TASK_ADJACENCY_ROUTE_SUBJECT + CommonConstant.PLACEHOLDER + task.getTaskId();
            boolean ok = distributedLock.tryAcquire(key, "ok", 60 * 60);
            if (!ok) {
                // 之前的定时任务已经执行过了
                continue;
            }
            List<DelayDspOrder> orders = delayDspOrderRepository.queryByTaskId(task.getTaskId());
            long delay = 0;
            for (DelayDspOrder order : orders) {
                Map<String, Object> map = Maps.newHashMap();
                map.put("taskId", task.getTaskId());
                map.put("orderId", order.getOrderId());
                messageProducer.sendDelayMessage(CommonConstant.DCS_DSP_DELAY_TASK_ADJACENCY_ROUTE_SUBJECT, map, delay);
                delay += 100;
            }
        }
    }
}
