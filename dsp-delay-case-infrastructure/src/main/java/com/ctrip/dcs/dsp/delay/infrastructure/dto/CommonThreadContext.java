package com.ctrip.dcs.dsp.delay.infrastructure.dto;

import com.ctrip.dcs.scm.sdk.common.ParentCategoryEnum;
import lombok.Builder;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2025/4/8 14:33
 */
@Builder
public class CommonThreadContext {
    
    private String threadPoolName;
    
    private Set<Long> driverIds;
    
    private Map<String, List<Long>> drvUdlGroup;
    
    private ParentCategoryEnum parentCategoryEnum;
    
    private Long supplierId;
    
    public String getThreadPoolName() {
        return threadPoolName;
    }
    
    public void setThreadPoolName(String threadPoolName) {
        this.threadPoolName = threadPoolName;
    }
    
    public Set<Long> getDriverIds() {
        return driverIds;
    }
    
    public void setDriverIds(Set<Long> driverIds) {
        this.driverIds = driverIds;
    }
    
    public Map<String, List<Long>> getDrvUdlGroup() {
        return drvUdlGroup;
    }
    
    public void setDrvUdlGroup(Map<String, List<Long>> drvUdlGroup) {
        this.drvUdlGroup = drvUdlGroup;
    }
    
    public ParentCategoryEnum getParentCategoryEnum() {
        return parentCategoryEnum;
    }
    
    public void setParentCategoryEnum(ParentCategoryEnum parentCategoryEnum) {
        this.parentCategoryEnum = parentCategoryEnum;
    }
}
