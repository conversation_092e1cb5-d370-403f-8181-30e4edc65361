package com.ctrip.dcs.dsp.delay.filter.impl;

import com.ctrip.dcs.dsp.delay.model.DelayDspTask;
import com.ctrip.dcs.dsp.delay.model.Driver;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 */
@Component("addressFilter")
public class AddressFilter extends CommonFilter {

    @Override
    public boolean filter(Driver driver, DelayDspTask task) {
        return checkPOI(driver.getAddressLongitude()) && checkPOI(driver.getAddressLatitude());
    }

    private boolean checkPOI(Double poi) {
        return Objects.nonNull(poi) && poi > 0D;
    }

    @Override
    public String reason() {
        return "no address";
    }
}
