package com.ctrip.dcs.dsp.delay.validator;

import com.ctrip.dcs.dsp.delay.model.SupplyOrder;
import com.ctrip.dcs.dsp.delay.validator.impl.HighGradeOrderValidator;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.modules.junit4.PowerMockRunner;

/**
 * <AUTHOR>
 */
@RunWith(PowerMockRunner.class)
public class HighGradeOrderValidatorTest {

    @InjectMocks
    private HighGradeOrderValidator validator;

    @Mock
    private SupplyOrder order;

    @Test
    public void test() {
        PowerMockito.when(order.getHighGradeOrder()).thenReturn(null);
        ValidatorDTO validate = validator.validate(order);
        Assert.assertEquals(ValidatorCode.OK, validate.getCode());
        PowerMockito.when(order.getHighGradeOrder()).thenReturn(1);
        ValidatorDTO validate1 = validator.validate(order);
        Assert.assertEquals(ValidatorCode.HIGH_LEVEL_ORDER_NOT_GOLD_DRIVER_LIMIT, validate1.getCode());
        PowerMockito.when(order.getHighGradeOrder()).thenReturn(0);
        ValidatorDTO validate2 = validator.validate(order);
        Assert.assertEquals(ValidatorCode.OK, validate2.getCode());
    }
}
