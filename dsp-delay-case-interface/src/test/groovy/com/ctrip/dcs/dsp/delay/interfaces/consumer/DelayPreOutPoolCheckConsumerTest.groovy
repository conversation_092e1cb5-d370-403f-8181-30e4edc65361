package com.ctrip.dcs.dsp.delay.interfaces.consumer

import com.ctrip.dcs.dsp.delay.infrastructure.mapper.DelayDspTaskRecordMapper
import com.ctrip.dcs.dsp.delay.model.DelayDspTaskRecord
import com.ctrip.dcs.dsp.delay.service.PreOutPoolService
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import qunar.tc.qmq.Message
import spock.lang.Specification

import static org.mockito.ArgumentMatchers.anyString
import static org.mockito.Mockito.when

/**
 * <AUTHOR>
 */
class DelayPreOutPoolCheckConsumerTest extends Specification {
    @Mock
    PreOutPoolService preOutPoolService
    @Mock
    DelayDspTaskRecordMapper delayDspTaskRecordMapper
    @Mock
    Message message
    @InjectMocks
    DelayPreOutPoolCheckConsumer delayPreOutPoolCheckConsumer

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test on Message"() {
        given:
        when(delayDspTaskRecordMapper.queryByDspOrderId(anyString())).thenReturn(null);
        def a = 1

        when:
        def result =delayPreOutPoolCheckConsumer.onMessage(message);
        a = a + 1

        then:
        a == 2

    }


    def "test on Message1"() {
        given:
        def a = 1
        when(delayDspTaskRecordMapper.queryByDspOrderId(anyString())).thenReturn(new DelayDspTaskRecord());
        when(message.getStringProperty(anyString())).thenReturn("1");
        when(message.getIntProperty(anyString())).thenReturn(0)
        when:
        def result =delayPreOutPoolCheckConsumer.onMessage(message);
        a = a + 1

        then:
        a == 2

    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme