package com.ctrip.dcs.dsp.delay.factory;

import com.ctrip.dcs.dsp.delay.carconfig.value.DriverProfitDayBaselineValue;
import com.ctrip.dcs.dsp.delay.carconfig.value.HeadTailLimitValue;
import com.ctrip.dcs.dsp.delay.carconfig.value.LBSBufferValue;
import com.ctrip.dcs.dsp.delay.carconfig.value.OrderMileageValue;
import com.ctrip.dcs.dsp.delay.context.DelayDspContext;
import com.ctrip.dcs.dsp.delay.model.DelayDspOrder;
import com.ctrip.dcs.dsp.delay.model.DelayDspTask;
import com.ctrip.dcs.dsp.delay.model.DispatcherConfig;
import com.ctrip.dcs.dsp.delay.model.DriverAggregation;
import com.ctrip.dcs.dsp.delay.qconfig.DriverProfitBaselineConfig;
import com.ctrip.dcs.dsp.delay.qconfig.HeadTailLimitConfig;
import com.ctrip.dcs.dsp.delay.qconfig.LbsBufferConfig;
import com.ctrip.dcs.dsp.delay.qconfig.OrderMileageValueConfig;
import com.ctrip.dcs.dsp.delay.qconfig.value.HeadTailLimitValueVO;
import com.ctrip.dcs.dsp.delay.qconfig.value.LbsBufferValueVO;
import com.ctrip.dcs.dsp.delay.qconfig.value.OrderMileageConfigValueVO;
import com.ctrip.dcs.dsp.delay.service.DispatchConfigService;
import com.ctrip.dcs.dsp.delay.service.DriverService;
import com.ctrip.dcs.dsp.delay.util.JsonUtil;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Component
public class DelayDspContextFactory {

    private static final Logger logger = LoggerFactory.getLogger(DelayDspContextFactory.class);

    @Autowired
    private DriverService driverService;

    @Autowired
    private DispatchConfigService dispatchConfigService;

    @Autowired
    private DriverProfitBaselineConfig driverProfitBaselineConfig;

    @Autowired
    private OrderMileageValueConfig orderMileageValueConfig;

    @Autowired
    private LbsBufferConfig lbsBufferConfig;

    @Autowired
    private HeadTailLimitConfig headTailLimitConfig;

    public DelayDspContext createDelayDspContextForMatch(DelayDspTask task, List<DelayDspOrder> orders) {

        DispatcherConfig config = buildDispatcherConfig(task);

        List<DriverAggregation> driverAggregations = driverService.queryDriverAggregationWithProfitFilter(task, config.getDriverProfitDayBaselineValue());

        return new DelayDspContext(task, orders, driverAggregations, config);
    }

    public DelayDspContext createDelayDspContext(DelayDspTask task, List<DelayDspOrder> orders) {

        DispatcherConfig config = buildDispatcherConfig(task);

        List<DriverAggregation> driverAggregations = driverService.queryDriverAggregation(config.getDriverProfitDayBaselineValue(), task);

        return new DelayDspContext(task, orders, driverAggregations, config);
    }

    public DelayDspContext createSDDelayDspContext(DelayDspTask task, List<DelayDspOrder> orders) {

        return new DelayDspContext(task, orders, null, null);
    }

    private DispatcherConfig buildDispatcherConfig(DelayDspTask task) {
        // 收益基线配置
        DriverProfitDayBaselineValue driverProfitDayBaselineValue = queryDriverProfitDayBaselineValue(task);
        // 里程价值配置
        List<OrderMileageValue> orderMileageValues = queryOrderMileageValue(task);
        // lbs buffer配置
        List<LBSBufferValue> lbsBufferValues = queryLbsBufferValue(task);
        // 首尾配置
        HeadTailLimitValue headTailLimitValue = queryHeadTailLimitConfig(task);

        return new DispatcherConfig(orderMileageValues, driverProfitDayBaselineValue, lbsBufferValues, headTailLimitValue);
    }

    public List<String> queryPreOutPoolDrivers(DelayDspTask delayDspTask, Map<String, List<String>> driverOrderIdsMapping) {
        DriverProfitDayBaselineValue profitDayBaselineValue = getProfitDayBaselineValue(delayDspTask);
        logger.info("DelayDspContextFactory_queryPreOutPoolDrivers_profitDayBaselineValue", JsonUtil.toJson(profitDayBaselineValue));
        if(profitDayBaselineValue.getStandardProfit() == 0){
            return Lists.newArrayList();
        }
        List<String> list = driverService.queryPreOutPoolDrivers(delayDspTask, profitDayBaselineValue, driverOrderIdsMapping);
        logger.info("DelayDspContextFactory_queryPreOutPoolDrivers_queryPreOutPoolDrivers", JsonUtil.toJson(list));
        return list;
    }


    public DriverProfitDayBaselineValue getProfitDayBaselineValue(DelayDspTask task) {
        // 收益基线配置
        return queryDriverProfitDayBaselineValue(task);
    }

    private HeadTailLimitValue queryHeadTailLimitConfig(DelayDspTask task) {
        HeadTailLimitValueVO config = headTailLimitConfig.getHeadTailLimitVO(task.getCityId(), task.getCarTypeId());
        return new HeadTailLimitValue(config.getHeadLimit(), config.getTailLimit());
    }

    private List<LBSBufferValue> queryLbsBufferValue(DelayDspTask task) {
        LbsBufferValueVO config = lbsBufferConfig.getValue(task.getCityId());
        LBSBufferValue value = new LBSBufferValue();
        value.setStartTime(config.getStartTime());
        value.setEndTime(config.getEndTime());
        value.setValueType(config.getType());
        value.setValue(config.getValue());

        return Lists.newArrayList(value);
    }

    private List<OrderMileageValue> queryOrderMileageValue(DelayDspTask task) {
        OrderMileageConfigValueVO config = orderMileageValueConfig.getValue(task.getCityId(), task.getCarTypeId());
        OrderMileageValue value = new OrderMileageValue();
        value.setStartKilo(Integer.valueOf(config.getStartKilo()));
        value.setEndKilo(Integer.valueOf(config.getEndKilo()));
        value.setStartTime(config.getStartTime());
        value.setEndTime(config.getEndTime());
        value.setValue(Double.valueOf(config.getValue()));
        return Lists.newArrayList(value);
    }

    private DriverProfitDayBaselineValue queryDriverProfitDayBaselineValue(DelayDspTask task) {
        Integer highProfitLineDay = driverProfitBaselineConfig.getHighProfitLineDay(task.getCityId(), task.getCarTypeId());
        Integer standardProfitLineDay = driverProfitBaselineConfig.getStandardProfitLineDay(task.getCityId(), task.getCarTypeId());
        return new DriverProfitDayBaselineValue(standardProfitLineDay, highProfitLineDay);
    }
}
