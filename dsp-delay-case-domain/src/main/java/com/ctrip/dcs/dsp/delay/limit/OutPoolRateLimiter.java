package com.ctrip.dcs.dsp.delay.limit;

import com.ctrip.dcs.dsp.delay.model.DelayDspTask;
import com.google.common.util.concurrent.RateLimiter;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 出池限流器
 * <AUTHOR>
 */
@Component
public class OutPoolRateLimiter {

    /**
     * 默认限流:1QPS
     */
    private static final double DEFAULT_LIMIT = 1D;

    private static final String CONFIRM_KEY = "confirm_rate_limiter";

    private static final String REDISPATCH_KEY = "redispatch_rate_limiter";

    private static final String DEFAULT_KEY = "default_rate_limiter";

    /**
     * 应单限流
     */
    private static RateLimiter confirmRateLimiter;

    /**
     * 改派限流
     */
    private static RateLimiter redispatchRateLimiter;

    /**
     * 存量任务的限流
     */
    private static RateLimiter defaultRateLimiter;

    public RateLimiter getConfirmRateLimiter(DelayDspTask task) {
        return task.isNearDeadline() ? defaultRateLimiter : confirmRateLimiter;
    }

    public RateLimiter getRedispatchRateLimiter(DelayDspTask task) {
        return task.isNearDeadline() ? defaultRateLimiter : redispatchRateLimiter;
    }

    public static void createLimiter(Map<String, String> config) {
        confirmRateLimiter = RateLimiter.create(MapUtils.getDoubleValue(config, CONFIRM_KEY, DEFAULT_LIMIT));
        redispatchRateLimiter = RateLimiter.create(MapUtils.getDoubleValue(config, REDISPATCH_KEY, DEFAULT_LIMIT));
        defaultRateLimiter = RateLimiter.create(MapUtils.getDoubleValue(config, DEFAULT_KEY, DEFAULT_LIMIT));
    }

    public static void main(String[] args) {
        RateLimiter limiter = RateLimiter.create(0.5);
        Thread t1 = new Thread(() -> {
            for (int i = 0; i < 10; i++) {
                long l = System.currentTimeMillis();
                double acquire = limiter.acquire();
                l = System.currentTimeMillis() - l;
                System.out.println("t1:" + acquire +", l:" + l);
            }
        });
        Thread t2 = new Thread(() -> {
            for (int i = 0; i < 10; i++) {
                long l = System.currentTimeMillis();
                double acquire = limiter.acquire();
                l = System.currentTimeMillis() - l;
                System.out.println("t1:" + acquire +", l:" + l);
            }
        });
        t1.start();
        t2.start();
    }
}
