package com.ctrip.dcs.dsp.delay.service.impl

import com.ctrip.dcs.dsp.delay.carconfig.value.DriverProfitDayBaselineValue
import com.ctrip.dcs.dsp.delay.carconfig.value.HeadTailLimitValue
import com.ctrip.dcs.dsp.delay.conflict.Check
import com.ctrip.dcs.dsp.delay.conflict.CheckChain
import com.ctrip.dcs.dsp.delay.conflict.CheckCode
import com.ctrip.dcs.dsp.delay.conflict.impl.*
import com.ctrip.dcs.dsp.delay.context.DelayDspContext
import com.ctrip.dcs.dsp.delay.factory.DPFactory
import com.ctrip.dcs.dsp.delay.gateway.GeoGateway
import com.ctrip.dcs.dsp.delay.model.*
import com.ctrip.dcs.dsp.delay.qconfig.DelayDspChecksQConfig
import com.ctrip.dcs.dsp.delay.qconfig.DelayDspCommonQConfig
import com.ctrip.dcs.dsp.delay.qconfig.value.DelayDspCheckValueVO
import com.ctrip.dcs.dsp.delay.service.ConflictService
import com.ctrip.dcs.dsp.delay.util.DateUtil
import com.ctrip.dcs.dsp.delay.util.JsonUtil
import com.ctrip.dcs.dsp.delay.util.MetricsUtil
import com.ctrip.dcs.dsp.delay.util.SpringApplicationContextUtil
import com.ctrip.igt.framework.common.metrics.MetricsFactory
import com.google.common.collect.Lists
import io.dropwizard.metrics5.MetricRegistry
import org.codehaus.jackson.type.TypeReference
import org.junit.runner.RunWith
import org.mockito.Mock
import org.powermock.api.mockito.PowerMockito
import org.powermock.core.classloader.annotations.PrepareForTest
import org.powermock.core.classloader.annotations.SuppressStaticInitializationFor
import org.powermock.modules.junit4.PowerMockRunner
import org.powermock.modules.junit4.PowerMockRunnerDelegate
import org.spockframework.runtime.Sputnik
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 */
@RunWith(PowerMockRunner.class)
@PowerMockRunnerDelegate(Sputnik.class)
@PrepareForTest([SpringApplicationContextUtil.class, MetricsUtil.class, MetricsFactory.class])
@SuppressStaticInitializationFor(["com.ctrip.dcs.dsp.delay.util.SpringApplicationContextUtil", "com.ctrip.dcs.dsp.delay.util.MetricsUtil", "com.ctrip.igt.framework.common.metrics.MetricsFactory"])
class DPDispatchTaskServiceImplSpec extends Specification {

    def delayDspCommonQConfig = Mock(DelayDspCommonQConfig)
    def delayDspChecksQConfig = Mock(DelayDspChecksQConfig)
    def geoGateway = Mock(GeoGateway)
    def checkChain = new CheckChain(delayDspChecksQConfig: delayDspChecksQConfig)
    def conflictService = new ConflictServiceImpl(checkChain: checkChain)
    def mockConflictService = Mock(ConflictService)
    def dpFactory = new DPFactory(conflictService: conflictService, geoGateway: geoGateway, delayDspCommonQConfig: delayDspCommonQConfig)
    def service = new DPDispatchTaskServiceImpl(conflictService: conflictService, geoGateway: geoGateway, dpFactory : dpFactory)
    @Mock
    private MetricRegistry metricRegistry;

    void setup() {
        PowerMockito.mockStatic(SpringApplicationContextUtil.class)
        PowerMockito.mockStatic(MetricsFactory.class)
        PowerMockito.mockStatic(MetricsUtil.class)
        PowerMockito.when(MetricsFactory.getMetricRegistry()).thenReturn(metricRegistry);
    }

    @Unroll
    def "dispatch"() {
        given: "Mock数据"
        def task = new DelayDspTask(taskId: 1L, beginTime: DateUtil.parseTime("08:00"), endTime: DateUtil.parseTime("19:59"), cityId: 1)
        def config = new DispatcherConfig(lbsBufferValues: Lists.newArrayList(), orderMileageValues: Lists.newArrayList(), driverProfitDayBaselineValue: new DriverProfitDayBaselineValue(highProfit: 200), headTailLimitValue: HeadTailLimitValue.DEFAULT)
        def context = new DelayDspContext(delayDspTask: task, config: config, driverAggregations: driver, orders: order)
        geoGateway.queryRoute(_, _) >> route
        delayDspChecksQConfig.getCheckValueVO(_)>> new DelayDspCheckValueVO("headOrderCheck,prevMileageCheck",
                "serviceTimeCheck,emptyDurationCheck,prevMileageCheck",
                "tailOrderCheck");
        PowerMockito.when(SpringApplicationContextUtil.getBean("serviceTimeCheck", Check.class)).thenReturn(new ServiceTimeCheck())
        PowerMockito.when(SpringApplicationContextUtil.getBean("prevMileageCheck", Check.class)).thenReturn(new PrevMileageCheck(geoGateway: geoGateway))
        PowerMockito.when(SpringApplicationContextUtil.getBean("emptyDurationCheck", Check.class)).thenReturn(new EmptyDurationCheck(geoGateway: geoGateway))
        PowerMockito.when(SpringApplicationContextUtil.getBean("tailOrderCheck", Check.class)).thenReturn(new TailOrderCheck(geoGateway: geoGateway))
        PowerMockito.when(SpringApplicationContextUtil.getBean("headOrderCheck", Check.class)).thenReturn(new HeadOrderCheck(geoGateway: geoGateway))
        delayDspCommonQConfig.isOrderProfitCity(_) >> true


        when: "执行校验方法"
        def res = service.dispatch(context)
        int count = 0
        for (DispatchResultDetail detail : res.getDetails()) {
            if (detail.getMatchSuccess() == 1) {
                count++
            }
        }

        then: "验证校验结果"
        count == result

        where:
        driver                | order       | route                                  || result
        null                  | getOrders() | new Route(duration: 0D, distance: 0D)  || 0
        getDrivers()          | getOrders() | new Route(duration: 0D, distance: 0D)  || 4    // (2,4),(1,5)
        getDrivers()          | getOrders() | new Route(duration: 5D, distance: 10D) || 3    // (1,4),(5)
        getDriversHaveOrder() | getOrders() | new Route(duration: 0D, distance: 0D)  || 4    // (1,0),(2,4)
        getDriversHaveOrder() | getOrders() | new Route(duration: 5D, distance: 10D) || 3    // (0),(1,4)
    }

    @Unroll
    def "o2首单不达标，但是可以和o1、o3匹配"() {
        given: "Mock数据"
        def task = new DelayDspTask(taskId: 1L, beginTime: DateUtil.parseTime("08:00"), endTime: DateUtil.parseTime("19:59"), cityId: 1)
        def config = new DispatcherConfig(lbsBufferValues: Lists.newArrayList(), orderMileageValues: Lists.newArrayList(), driverProfitDayBaselineValue: new DriverProfitDayBaselineValue(highProfit: 200), headTailLimitValue: new HeadTailLimitValue(5D, 5D))
        def context = new DelayDspContext(delayDspTask: task, config: config, driverAggregations: driver, orders: order)
        def headOrderCheck = Mock(HeadOrderCheck)
        headOrderCheck.check(_) >> CheckCode.OK >> CheckCode.HEAD_LIMIT_CONFLICT >> CheckCode.OK
        geoGateway.queryRoute(_, _) >> new Route(duration: 0D, distance: 0D)
        delayDspChecksQConfig.getCheckValueVO(_)>> new DelayDspCheckValueVO("headOrderCheck,prevMileageCheck",
                "serviceTimeCheck,emptyDurationCheck,prevMileageCheck",
                "tailOrderCheck");
        PowerMockito.when(SpringApplicationContextUtil.getBean("serviceTimeCheck", Check.class)).thenReturn(new ServiceTimeCheck())
        PowerMockito.when(SpringApplicationContextUtil.getBean("prevMileageCheck", Check.class)).thenReturn(new PrevMileageCheck(geoGateway: geoGateway))
        PowerMockito.when(SpringApplicationContextUtil.getBean("emptyDurationCheck", Check.class)).thenReturn(new EmptyDurationCheck(geoGateway: geoGateway))
        PowerMockito.when(SpringApplicationContextUtil.getBean("tailOrderCheck", Check.class)).thenReturn(new TailOrderCheck(geoGateway: geoGateway))
        PowerMockito.when(SpringApplicationContextUtil.getBean("headOrderCheck", Check.class)).thenReturn(headOrderCheck)
        delayDspCommonQConfig.isOrderProfitCity(_) >> true


        when: "执行校验方法"
        def res = service.dispatch(context)
        int count = 0
        for (DispatchResultDetail detail : res.getDetails()) {
            if (detail.getMatchSuccess() == 1) {
                count++
            }
        }

        then: "验证校验结果"
        count == result

        where:
        driver        | order        || result
        getDrivers1() | getOrders3() || 3    // (1,2,3)
    }

    @Unroll
    def "o1、o2首单不达标，o3独自接单"() {
        given: "Mock数据"

        def task = new DelayDspTask(taskId: 1L, beginTime: DateUtil.parseTime("08:00"), endTime: DateUtil.parseTime("19:59"), cityId: 1)
        def config = new DispatcherConfig(lbsBufferValues: Lists.newArrayList(), orderMileageValues: Lists.newArrayList(), driverProfitDayBaselineValue: new DriverProfitDayBaselineValue(highProfit: 200), headTailLimitValue: new HeadTailLimitValue(5D, 5D))
        def context = new DelayDspContext(delayDspTask: task, config: config, driverAggregations: driver, orders: order)

        geoGateway.queryRoute(_, _) >> new Route(duration: 20D, distance: 5D)
        delayDspChecksQConfig.getCheckValueVO(_)>> new DelayDspCheckValueVO("headOrderCheck,prevMileageCheck",
                "serviceTimeCheck,emptyDurationCheck,prevMileageCheck",
                "tailOrderCheck");
        PowerMockito.when(SpringApplicationContextUtil.getBean("serviceTimeCheck", Check.class)).thenReturn(new ServiceTimeCheck())
        PowerMockito.when(SpringApplicationContextUtil.getBean("prevMileageCheck", Check.class)).thenReturn(new PrevMileageCheck(geoGateway: geoGateway))
        PowerMockito.when(SpringApplicationContextUtil.getBean("emptyDurationCheck", Check.class)).thenReturn(new EmptyDurationCheck(geoGateway: geoGateway))
        PowerMockito.when(SpringApplicationContextUtil.getBean("tailOrderCheck", Check.class)).thenReturn(new TailOrderCheck(geoGateway: geoGateway))
        PowerMockito.when(SpringApplicationContextUtil.getBean("headOrderCheck", Check.class)).thenReturn(new HeadOrderCheck(geoGateway: geoGateway))
        delayDspCommonQConfig.isOrderProfitCity(_) >> true



        when: "执行校验方法"
        def res = service.dispatch(context)
        int count = 0
        for (DispatchResultDetail detail : res.getDetails()) {
            if (detail.getMatchSuccess() == 1) {
                count++
            }
        }

        then: "验证校验结果"
        count == result

        where:
        driver        | order        || result
        getDrivers() | getOrders1()  || 1    // (3)
    }

    @Unroll
    def "o1、o2首单不达标，且互相冲突但o1、o2是已接订单，o3普通延后派订单"() {
        given: "Mock数据"
        def task = new DelayDspTask(taskId: 1L, beginTime: DateUtil.parseTime("08:00"), endTime: DateUtil.parseTime("19:59"), cityId: 1)
        def config = new DispatcherConfig(lbsBufferValues: Lists.newArrayList(), orderMileageValues: Lists.newArrayList(), driverProfitDayBaselineValue: new DriverProfitDayBaselineValue(highProfit: 200), headTailLimitValue: new HeadTailLimitValue(5D, 5D))
        def context = new DelayDspContext(delayDspTask: task, config: config, driverAggregations: driver, orders: order)

        geoGateway.queryRoute(_, _) >> new Route(duration: 20D, distance: 5D)
        delayDspChecksQConfig.getCheckValueVO(_)>> new DelayDspCheckValueVO("headOrderCheck,prevMileageCheck",
                "serviceTimeCheck,emptyDurationCheck,prevMileageCheck",
                "tailOrderCheck");
        PowerMockito.when(SpringApplicationContextUtil.getBean("serviceTimeCheck", Check.class)).thenReturn(new ServiceTimeCheck())
        PowerMockito.when(SpringApplicationContextUtil.getBean("prevMileageCheck", Check.class)).thenReturn(new PrevMileageCheck(geoGateway: geoGateway))
        PowerMockito.when(SpringApplicationContextUtil.getBean("emptyDurationCheck", Check.class)).thenReturn(new EmptyDurationCheck(geoGateway: geoGateway))
        PowerMockito.when(SpringApplicationContextUtil.getBean("tailOrderCheck", Check.class)).thenReturn(new TailOrderCheck(geoGateway: geoGateway))
        PowerMockito.when(SpringApplicationContextUtil.getBean("headOrderCheck", Check.class)).thenReturn(new HeadOrderCheck(geoGateway: geoGateway))
        delayDspCommonQConfig.isOrderProfitCity(_) >> true

        when: "执行校验方法"
        def res = service.dispatch(context)
        int count = 0
        for (DispatchResultDetail detail : res.getDetails()) {
            if (detail.getMatchSuccess() == 1) {
                count++
            }
        }

        then: "验证校验结果"
        count == result

        where:
        driver        | order        || result
        getDrivers2() | getOrders2() || 3    // (1,2,3)
    }

    @Unroll
    def "o1、o2、o3匹配，但o3不能作为尾单"() {
        given: "Mock数据"
        def task = new DelayDspTask(taskId: 1L, beginTime: DateUtil.parseTime("08:00"), endTime: DateUtil.parseTime("19:59"), cityId: 1)
        def config = new DispatcherConfig(lbsBufferValues: Lists.newArrayList(), orderMileageValues: Lists.newArrayList(), driverProfitDayBaselineValue: new DriverProfitDayBaselineValue(highProfit: 200), headTailLimitValue: new HeadTailLimitValue(5D, 5D))
        def context = new DelayDspContext(delayDspTask: task, config: config, driverAggregations: driver, orders: order)
        def tailOrderCheck = Mock(TailOrderCheck)
        tailOrderCheck.check(_) >> CheckCode.TAIL_LIMIT_CONFLICT >> CheckCode.OK >> CheckCode.OK
        geoGateway.queryRoute(_, _) >> new Route(duration: 0D, distance: 0D)
        delayDspChecksQConfig.getCheckValueVO(_)>> new DelayDspCheckValueVO("headOrderCheck,prevMileageCheck",
                "serviceTimeCheck,emptyDurationCheck,prevMileageCheck",
                "tailOrderCheck");
        PowerMockito.when(SpringApplicationContextUtil.getBean("serviceTimeCheck", Check.class)).thenReturn(new ServiceTimeCheck())
        PowerMockito.when(SpringApplicationContextUtil.getBean("prevMileageCheck", Check.class)).thenReturn(new PrevMileageCheck(geoGateway: geoGateway))
        PowerMockito.when(SpringApplicationContextUtil.getBean("emptyDurationCheck", Check.class)).thenReturn(new EmptyDurationCheck(geoGateway: geoGateway))
        PowerMockito.when(SpringApplicationContextUtil.getBean("tailOrderCheck", Check.class)).thenReturn(tailOrderCheck)
        PowerMockito.when(SpringApplicationContextUtil.getBean("headOrderCheck", Check.class)).thenReturn(new HeadOrderCheck(geoGateway: geoGateway))
        delayDspCommonQConfig.isOrderProfitCity(_) >> true


        when: "执行校验方法"
        def res = service.dispatch(context)
        int count = 0
        for (DispatchResultDetail detail : res.getDetails()) {
            if (detail.getMatchSuccess() == 1) {
                count++
            }
        }

        then: "验证校验结果"
        count == result

        where:
        driver        | order        || result
        getDrivers1() | getOrders3() || 2    // (1,2)
    }

    @Unroll
    def "高收益组合，不包含司机已接订单，选择低收益的结果"() {
        given: "Mock数据"
        def service = new DPDispatchTaskServiceImpl(conflictService: mockConflictService, geoGateway: geoGateway, dpFactory : dpFactory)
        String s = "{\"delayDspTask\":{\"taskId\":3942,\"cityCode\":\"beijing_city\",\"cityId\":1,\"carTypeId\":117,\"taskStatus\":1,\"taskSeq\":0,\"beginTime\":\"2024-09-28 06:00:00\",\"endTime\":\"2024-09-28 17:59:59\",\"executeTime\":\"2024-08-22 18:20:00\",\"executeTimeDeadline\":\"2024-09-27 23:00:00\",\"taskType\":\"DP\",\"taskVersion\":21,\"matchTime\":\"2024-08-22 01:24:24\",\"matchStatus\":1,\"configBeginTime\":\"06:00\",\"configEndTime\":\"17:59\"},\"orders\":[{\"orderId\":\"91254199591436302\",\"mainOrderId\":\"70125247804\",\"taskId\":3942,\"carTypeId\":117,\"cityCode\":\"beijing_city\",\"cityId\":1,\"skuId\":49748,\"channelId\":235390,\"sysExpectBookTime\":\"2024-09-28 09:15:00\",\"predictServiceStopTime\":\"2024-09-28 10:28:00\",\"categoryCode\":\"airport_pickup\",\"isOta\":0,\"kiloLength\":43.0,\"fromLongitude\":116.593409,\"fromLatitude\":40.079174,\"fromCoordsys\":\"GCJ02\",\"fromHash\":\"1tu9p211tbfs\",\"toLongitude\":116.27356,\"toLatitude\":39.907612,\"toCoordsys\":\"GCJ02\",\"toHash\":\"1tu8v2nzwtd8\",\"driverId\":\"\",\"driverOrderFee\":129.39,\"isCancel\":0,\"isDelay\":1,\"duid\":\"91254199591436302-v2.0-0-8889-1:1:1-************-2-5-0-0\",\"orderSource\":\"QUNAR\"}],\"driverAggregations\":[{\"driver\":{\"driverId\":\"3452468\",\"cityId\":1,\"cityCode\":\"beijing_city\",\"status\":2,\"carTypeId\":117,\"coopMode\":5,\"carLicense\":\"京A21011\",\"addressLongitude\":116.489407,\"addressLatitude\":39.617582,\"isEnergy\":1,\"workTimes\":[\"00:00~11:59\"],\"transportGroups\":[{\"transportGroupId\":2539,\"transportGroupName\":\"是是是\",\"transportGroupMode\":1006},{\"transportGroupId\":2723,\"transportGroupName\":\"测试2024\",\"transportGroupMode\":1003},{\"transportGroupId\":1476,\"transportGroupName\":\"回归报名制\",\"transportGroupMode\":1001},{\"transportGroupId\":253,\"transportGroupName\":\"测试境外进单配置01运力组\",\"transportGroupMode\":1003},{\"transportGroupId\":1699,\"transportGroupName\":\"测试备用调度电话\",\"transportGroupMode\":1003},{\"transportGroupId\":3795,\"transportGroupName\":\"北京-携程专车1002000\",\"transportGroupMode\":1006},{\"transportGroupId\":224,\"transportGroupName\":\"测试01运力组01\",\"transportGroupMode\":1001},{\"transportGroupId\":3998,\"transportGroupName\":\"香港到深圳\",\"transportGroupMode\":1003},{\"transportGroupId\":134,\"transportGroupName\":\"新增优先人工调度\",\"transportGroupMode\":1003},{\"transportGroupId\":239,\"transportGroupName\":\"何海龙测试兼职勿动\",\"transportGroupMode\":1002},{\"transportGroupId\":468,\"transportGroupName\":\"测试升级\",\"transportGroupMode\":1003},{\"transportGroupId\":249,\"transportGroupName\":\"运力组进单测试1\",\"transportGroupMode\":1003}],\"registerTransportGroupId\":2539},\"score\":{\"drivId\":\"3452468\",\"totalScore\":296.2,\"driverBaseScore\":300.0,\"carBaseScore\":0.0,\"activeScore\":1.4,\"orderServiceScore\":-8.2,\"extraBonusScore\":0.0,\"studyScore\":0.0},\"orders\":[{\"orderId\":\"881833283240602455\",\"mainOrderId\":\"36555519365\",\"taskId\":3942,\"carTypeId\":118,\"cityCode\":\"shanghai_city\",\"cityId\":2,\"skuId\":49767,\"channelId\":235390,\"sysExpectBookTime\":\"2024-09-28 10:15:00\",\"predictServiceStopTime\":\"2024-09-28 10:35:00\",\"categoryCode\":\"airport_pickup\",\"isOta\":0,\"kiloLength\":21.0,\"fromLongitude\":121.327447,\"fromLatitude\":31.192568,\"fromCoordsys\":\"GCJ02\",\"fromHash\":\"1tms65wgrx52\",\"toLongitude\":121.474556,\"toLatitude\":31.23277,\"toCoordsys\":\"GCJ02\",\"toHash\":\"1tms7j3y08d6\",\"driverId\":\"3452468\",\"driverOrderFee\":499.0,\"isCancel\":0,\"isDelay\":0,\"duid\":\"\",\"orderSource\":\"QUNAR\"}],\"outTimeOrders\":[]}],\"config\":{\"orderMileageValues\":[{\"startKilo\":0,\"endKilo\":99,\"startTime\":\"00:00\",\"endTime\":\"23:59\",\"value\":1.0}],\"driverProfitDayBaselineValue\":{\"standardProfit\":190,\"highProfit\":1000},\"lbsBufferValues\":[{\"startTime\":\"00:00\",\"endTime\":\"23:59\",\"valueType\":0,\"value\":0.0}],\"headTailLimitValue\":{\"headLimit\":-1.0,\"tailLimit\":-1.0},\"incomeStandardLine\":1000,\"headLimit\":-1.0,\"tailLimit\":-1.0}}";
        DelayDspContext delayDspContext = JsonUtil.fromJson(s, new TypeReference<DelayDspContext>() {
        });
        List<DelayDspOrder> list = Lists.newArrayList();
        list.addAll(delayDspContext.getOrders());
        list.addAll(delayDspContext.getDriverAggregations().get(0).getOrders())
        list.sort { a, b -> (a.getPredictServiceStopTime() <=> b.getPredictServiceStopTime()) }
        int[][] matrix = [[1, 0], [0, 1]]
        mockConflictService.checkConflict(_ as DelayDspContext, _ as DriverAggregation, _ as ArrayList) >> CheckCode.OK

        when: "执行校验方法"
        List<DelayDspOrder> result = service.select(delayDspContext, delayDspContext.getDriverAggregations().get(0), list, matrix);

        then: "验证校验结果"
        result.size() == 1
        result.get(0).getOrderId() == "881833283240602455"
    }


    def getDrivers1() {
        def driver1 = new Driver(driverId: "1", addressLongitude: 1.1, addressLatitude: 1.1)
        def score1 = new DriverScore(totalScore: 100D)
        def aggregation1 = new DriverAggregation(driver: driver1, score: score1, orders: Lists.newArrayList())
        return Lists.newArrayList(aggregation1)
    }

    def getDrivers2() {
        def o1 = new DelayDspOrder(orderId: "1", sysExpectBookTime: DateUtil.parseTime("08:10"), predictServiceStopTime: DateUtil.parseTime("08:28"), kiloLength: 0, driverOrderFee: 75.54, driverId: "1", isDelay: 0)
        def o2 = new DelayDspOrder(orderId: "2", sysExpectBookTime: DateUtil.parseTime("09:02"), predictServiceStopTime: DateUtil.parseTime("09:47"), kiloLength: 0, driverOrderFee: 96.24, driverId: "1", isDelay: 0)
        def driver1 = new Driver(driverId: "1", addressLongitude: 1.1, addressLatitude: 1.1)
        def score1 = new DriverScore(totalScore: 100D)
        def aggregation1 = new DriverAggregation(driver: driver1, score: score1, orders: Lists.newArrayList(o1, o2))
        return Lists.newArrayList(aggregation1)
    }

    def getDrivers() {
        def driver1 = new Driver(driverId: "1", addressLongitude: 1.1, addressLatitude: 1.1)
        def driver2 = new Driver(driverId: "2", addressLongitude: 1.1, addressLatitude: 1.1)
        def score1 = new DriverScore(totalScore: 100D)
        def score2 = new DriverScore(totalScore: 10D)
        def aggregation1 = new DriverAggregation(driver: driver1, score: score1, orders: Lists.newArrayList())
        def aggregation2 = new DriverAggregation(driver: driver2, score: score2, orders: Lists.newArrayList())

        return Lists.newArrayList(aggregation1, aggregation2)
    }

    def getDriversHaveOrder() {
        def order = new DelayDspOrder(orderId: "0", sysExpectBookTime: DateUtil.parseTime("08:43"), predictServiceStopTime: DateUtil.parseTime("09:48"), kiloLength: 0, driverOrderFee: 75.54, driverId: "1", isDelay: 0)
        def driver1 = new Driver(driverId: "1", addressLongitude: 1.1, addressLatitude: 1.1)
        def driver2 = new Driver(driverId: "2", addressLongitude: 1.1, addressLatitude: 1.1)
        def score1 = new DriverScore(totalScore: 100D)
        def score2 = new DriverScore(totalScore: 10D)
        def aggregation1 = new DriverAggregation(driver: driver1, score: score1, orders: Lists.newArrayList(order))
        def aggregation2 = new DriverAggregation(driver: driver2, score: score2, orders: Lists.newArrayList())

        return Lists.newArrayList(aggregation1, aggregation2)
    }

    def getOrders() {
        def o1 = new DelayDspOrder(orderId: "1", sysExpectBookTime: DateUtil.parseTime("08:10"), predictServiceStopTime: DateUtil.parseTime("08:28"), kiloLength: 0, driverOrderFee: 75.54, cityId: 1)
        def o2 = new DelayDspOrder(orderId: "2", sysExpectBookTime: DateUtil.parseTime("08:02"), predictServiceStopTime: DateUtil.parseTime("08:47"), kiloLength: 0, driverOrderFee: 96.24, cityId: 1)
        def o3 = new DelayDspOrder(orderId: "3", sysExpectBookTime: DateUtil.parseTime("08:35"), predictServiceStopTime: DateUtil.parseTime("09:30"), kiloLength: 0, driverOrderFee: 120, cityId: 1)
        def o4 = new DelayDspOrder(orderId: "4", sysExpectBookTime: DateUtil.parseTime("09:01"), predictServiceStopTime: DateUtil.parseTime("09:32"), kiloLength: 0, driverOrderFee: 132.57, cityId: 1)
        def o5 = new DelayDspOrder(orderId: "5", sysExpectBookTime: DateUtil.parseTime("08:42"), predictServiceStopTime: DateUtil.parseTime("09:48"), kiloLength: 0, driverOrderFee: 162, cityId: 1)
        def o6 = new DelayDspOrder(orderId: "6", sysExpectBookTime: DateUtil.parseTime("08:23"), predictServiceStopTime: DateUtil.parseTime("10:51"), kiloLength: 0, driverOrderFee: 160.05, cityId: 1)
        def o7 = new DelayDspOrder(orderId: "7", sysExpectBookTime: DateUtil.parseTime("07:23"), predictServiceStopTime: DateUtil.parseTime("08:51"), kiloLength: 0, driverOrderFee: 160.05, cityId: 1)
        def o8 = new DelayDspOrder(orderId: "8", sysExpectBookTime: DateUtil.parseTime("21:23"), predictServiceStopTime: DateUtil.parseTime("22:51"), kiloLength: 0, driverOrderFee: 160.05, cityId: 1)
        return Lists.newArrayList(o1, o2, o3, o4, o5, o6, o7, o8)
    }

    def getOrders1() {
        def o1 = new DelayDspOrder(orderId: "1", sysExpectBookTime: DateUtil.parseTime("08:10"), predictServiceStopTime: DateUtil.parseTime("08:28"), kiloLength: 0, driverOrderFee: 75.54, cityId: 1)
        def o2 = new DelayDspOrder(orderId: "2", sysExpectBookTime: DateUtil.parseTime("08:02"), predictServiceStopTime: DateUtil.parseTime("08:47"), kiloLength: 0, driverOrderFee: 96.24, cityId: 1)
        def o3 = new DelayDspOrder(orderId: "3", sysExpectBookTime: DateUtil.parseTime("08:35"), predictServiceStopTime: DateUtil.parseTime("09:30"), kiloLength: 0, driverOrderFee: 120)
        return Lists.newArrayList(o1, o2, o3)
    }


    def getOrders2() {
        def o3 = new DelayDspOrder(orderId: "3", sysExpectBookTime: DateUtil.parseTime("10:35"), predictServiceStopTime: DateUtil.parseTime("10:30"), kiloLength: 0, driverOrderFee: 120, cityId: 1)
        return Lists.newArrayList(o3)
    }

    def getOrders3() {
        def o1 = new DelayDspOrder(orderId: "1", sysExpectBookTime: DateUtil.parseTime("08:10"), predictServiceStopTime: DateUtil.parseTime("08:18"), kiloLength: 0, driverOrderFee: 75.54, cityId: 1)
        def o2 = new DelayDspOrder(orderId: "2", sysExpectBookTime: DateUtil.parseTime("08:32"), predictServiceStopTime: DateUtil.parseTime("08:47"), kiloLength: 0, driverOrderFee: 96.24, cityId: 1)
        def o3 = new DelayDspOrder(orderId: "3", sysExpectBookTime: DateUtil.parseTime("09:05"), predictServiceStopTime: DateUtil.parseTime("09:30"), kiloLength: 0, driverOrderFee: 120, cityId: 1)
        return Lists.newArrayList(o1, o2, o3)
    }

}
