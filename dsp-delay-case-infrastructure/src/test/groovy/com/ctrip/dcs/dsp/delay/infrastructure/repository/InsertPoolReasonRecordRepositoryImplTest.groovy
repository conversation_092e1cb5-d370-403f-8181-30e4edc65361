package com.ctrip.dcs.dsp.delay.infrastructure.repository

import com.ctrip.dcs.dsp.delay.infrastructure.mapper.ck.ClickHouseAdapter
import com.ctrip.dcs.dsp.delay.model.InsertPoolReasonRecord
import org.junit.Assert
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 * @since 2024/8/19 21:11
 */
class InsertPoolReasonRecordRepositoryImplTest extends Specification {
    def testObj = new InsertPoolReasonRecordRepositoryImpl()
    def clickHouseAdapter = Mock(ClickHouseAdapter)

    def setup() {
        testObj.clickHouseAdapter = clickHouseAdapter
    }

    @Unroll
    def "test record"() {
        given:
        InsertPoolReasonRecord record = buildInsertPoolReasonRecord();

        when:
        testObj.record(record)

        then:
        Assert.assertTrue(Objects.nonNull(record))
    }

    def InsertPoolReasonRecord buildInsertPoolReasonRecord() {
        InsertPoolReasonRecord record = new InsertPoolReasonRecord();
        record.setUserOrderId("userOrderId");
        record.setDspOrderId("dspOrderId");
        record.setReasonCode("OK");
        record.setStartTime("2024-05-11 11:11:11");
        record.setEndTime("2024-05-12 12:12:12");
        return record;
    }
}
