package com.ctrip.dcs.dsp.delay.enums;

/**
 * @Description 灰度开关枚举
 * <AUTHOR>
 **/
public enum FlowSwitchScenesEnum {
    ORDER_BOOK(1, "book"),
    OTHER(2, "other"),

    ;


    private final int code;
    private final String des;

    FlowSwitchScenesEnum(int code, String des) {
        this.code = code;
        this.des = des;
    }

    public int getCode() {
        return code;
    }

    public String getDes() {
        return des;
    }

}
