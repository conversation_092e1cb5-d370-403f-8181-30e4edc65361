package com.ctrip.dcs.dsp.delay.service;

import com.ctrip.dcs.dsp.delay.conflict.CheckCode;
import com.ctrip.dcs.dsp.delay.context.DelayDspContext;
import com.ctrip.dcs.dsp.delay.model.DelayDspOrder;
import com.ctrip.dcs.dsp.delay.model.DriverAggregation;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface ConflictService {

    /**
     * 司机是否能接了prev单之后，再接next单
     * @param context
     * @param driverAggregation
     * @param prev
     * @param next
     * @return
     */
    CheckCode checkConflict(DelayDspContext context, DriverAggregation driverAggregation, DelayDspOrder prev, DelayDspOrder next);

    /**
     * 司机是否已首单接next订单
     * @param context
     * @param driverAggregation
     * @param next
     * @return
     */
    CheckCode checkConflict(DelayDspContext context, DriverAggregation driverAggregation, DelayDspOrder next);

    /**
     * 司机是否能接起订单集合
     * @param context
     * @param driverAggregation
     * @param list
     * @return
     */
    CheckCode checkConflict(DelayDspContext context, DriverAggregation driverAggregation, List<DelayDspOrder> list);

}
