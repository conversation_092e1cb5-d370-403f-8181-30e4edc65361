package com.ctrip.dcs.dsp.delay.fliter;

import com.ctrip.dcs.dsp.delay.filter.impl.LeaveFilter;
import com.ctrip.dcs.dsp.delay.gateway.DriverGateway;
import com.ctrip.dcs.dsp.delay.model.DelayDspTask;
import com.ctrip.dcs.dsp.delay.model.Driver;
import com.ctrip.dcs.dsp.delay.model.DriverLeave;
import com.ctrip.dcs.dsp.delay.util.DateUtil;
import com.google.common.collect.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@RunWith(PowerMockRunner.class)
public class LeaveFilterTest {

    @InjectMocks
    private LeaveFilter filter;

    @Mock
    private DriverGateway driverGateway;

    @Test
    public void test() {
        Driver driver1 = new Driver();
        driver1.setDriverId("1");
        Driver driver2 = new Driver();
        driver2.setDriverId("2");
        Driver driver3 = new Driver();
        driver3.setDriverId("3");

        DelayDspTask task = new DelayDspTask();
        task.setBeginTime(DateUtil.parseDate("2022-04-28 08:00:00"));
        task.setEndTime(DateUtil.parseDate("2022-04-28 19:59:59"));

        DriverLeave leave1 = new DriverLeave();
        leave1.setDrvId(1L);
        leave1.setLeaveBeginTime("2022-04-28 08:00:00");
        leave1.setLeaveEndTime("2022-04-28 19:59:59");

        DriverLeave leave3 = new DriverLeave();
        leave3.setDrvId(3L);
        leave3.setLeaveBeginTime("2022-04-29 08:00:00");
        leave3.setLeaveEndTime("2022-04-29 19:59:59");

        PowerMockito.when(driverGateway.queryLeave(Mockito.any())).thenReturn(Lists.newArrayList(leave1, leave3));
        List<Driver> drivers = this.filter.doFilter(Lists.newArrayList(driver1, driver2, driver3), task);
        Assert.assertNotNull(drivers);
    }
}
