package com.ctrip.dcs.dsp.delay.qconfig

import com.ctrip.dcs.dsp.delay.model.DelayDspTask
import com.ctrip.dcs.dsp.delay.model.Driver
import com.ctrip.dcs.dsp.delay.qconfig.key.DriverHighProfitLineDayKeyVO
import com.ctrip.dcs.dsp.delay.qconfig.value.DriverHighProfitLineDayValueVO
import com.google.common.collect.Lists
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 */
class DriverProfitBaselineConfigSpec extends Specification{

    def map = new HashMap();

    def config = new DriverProfitBaselineConfig(configMap: map)

    @Unroll
    def "test"() {
        given: "Mock数据"
        DriverHighProfitLineDayKeyVO keyVO = new DriverHighProfitLineDayKeyVO(cityId, carTypeId)
        DriverHighProfitLineDayValueVO valueVO = new DriverHighProfitLineDayValueVO(standard, high)
        map.put(key, new BaseCarConfig.Config(keyVO, Lists.newArrayList(valueVO)))

        when: "执行校验方法"
        def v1 = config.getHighProfitLineDay(1, 1)
        def v2 = config.getStandardProfitLineDay(1, 1)

        then: "验证校验结果"
        v1 == high
        v2 == standard

        where:
        key   | cityId | carTypeId || high | standard
        "1#1" | 1      | 1         || 100  | 10
        "1#2" | 1      | 2         || 0    | 0
        "0#0" | 0      | 0         || 100  | 10
        "1#0" | 1      | 0         || 100  | 10
    }
}
