package com.ctrip.dcs.dsp.delay.infrastructure.geteway;

import com.ctrip.dcs.dsp.delay.gateway.ChannelGateway;
import com.ctrip.dcs.dsp.delay.infrastructure.converter.CommonConverter;
import com.ctrip.dcs.dsp.delay.infrastructure.soa.ShoppingCommonServiceProxy;
import com.ctrip.dcs.dsp.delay.model.ChannelNumber;
import com.ctrip.dcs.dsp.delay.util.MetricsUtil;
import com.ctrip.dcs.shopping.common.interfaces.dto.ChannelNumberDTO;
import com.ctrip.dcs.shopping.common.interfaces.message.QueryChannelNumbersRequestType;
import com.ctrip.dcs.shopping.common.interfaces.message.QueryChannelNumbersResponseType;
import com.ctrip.igt.ResponseResult;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * <AUTHOR>
 */
@Component
public class ChannelGatewayImpl implements ChannelGateway {

    private static final Logger logger = LoggerFactory.getLogger(ChannelGatewayImpl.class);

    @Autowired
    private ShoppingCommonServiceProxy shoppingCommonServiceProxy;

    @Override
    public ChannelNumber queryChannelNumber(Long channelId) {

        try {
            QueryChannelNumbersRequestType request = new QueryChannelNumbersRequestType();
            request.setChannelNumberIds(Lists.newArrayList(channelId));

            QueryChannelNumbersResponseType response = shoppingCommonServiceProxy.queryChannelNumbers(request);
            boolean success = Optional.ofNullable(response).map(QueryChannelNumbersResponseType::getResponseResult).map(ResponseResult::isSuccess).orElse(false);
            if (!success) {
                MetricsUtil.recordValue("query.channel.number.soa.error", 1);
                return null;
            }
            ChannelNumberDTO dto = Optional.ofNullable(response)
                    .filter(res -> CollectionUtils.isNotEmpty(res.getChannelNumberList()))
                    .map(QueryChannelNumbersResponseType::getChannelNumberList)
                    .orElse(Lists.newArrayList())
                    .stream()
                    .findFirst()
                    .orElse(null);

            return CommonConverter.INSTANCE.toChannelNumber(dto);
        } catch (Exception e) {
            logger.error(e);
        }
        return null;
    }
}
