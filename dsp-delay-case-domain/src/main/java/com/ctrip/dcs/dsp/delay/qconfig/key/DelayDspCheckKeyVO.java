package com.ctrip.dcs.dsp.delay.qconfig.key;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;

/**
 * Created by xing'xing'yu on 2023/8/25
 */
@Getter
public class DelayDspCheckKeyVO implements CarConfigKeyVO {

    private Integer cityId;

    @JsonCreator
    public DelayDspCheckKeyVO(@JsonProperty("cityId") Integer cityId) {
        this.cityId = cityId;
    }

    @Override
    public String toKey() {
        return cityId.toString();
    }
}