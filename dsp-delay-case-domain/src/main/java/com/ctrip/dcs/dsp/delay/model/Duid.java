package com.ctrip.dcs.dsp.delay.model;

import lombok.Getter;
import lombok.Setter;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class Duid {

    private static final String SPLIT_LABEL = "-";

    public static final String DATE_FORMAT = "yyMMddHHmmss";

    private String orderId;

    private Integer subSku;

    /**
     * 派发方式， 2-指派
     */
    private Integer dspType = 2;

    /**
     * 接单方式 5-指派接单
     */
    private Integer takenType = 5;

    private Integer sceneId = 0;

    private String dspTime = new SimpleDateFormat(DATE_FORMAT).format(new Date());

    private String duidVersion = "v2.0";

    private String roundKey = "1:1:1";

    private Integer broadcastScore = 0;

    private int selectType = 0;

    public Duid(String orderId, Integer subSku) {
        this.orderId = orderId;
        this.subSku = subSku;
    }

    @Override
    public String toString() {
        return this.getOrderId() + SPLIT_LABEL +
                this.getDuidVersion() + SPLIT_LABEL +
                this.getSceneId() + SPLIT_LABEL +
                this.getSubSku() + SPLIT_LABEL +
                this.getRoundKey() + SPLIT_LABEL +
                this.getDspTime() + SPLIT_LABEL +
                this.getDspType() + SPLIT_LABEL +
                this.getTakenType() + SPLIT_LABEL +
                this.getBroadcastScore() + SPLIT_LABEL +
                this.getSelectType();
    }
}
