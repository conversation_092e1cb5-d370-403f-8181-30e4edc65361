package com.ctrip.dcs.dsp.delay.infrastructure.trocks;

import com.ctrip.dcs.dsp.delay.cache.DistributedCache;
import com.ctrip.dcs.dsp.delay.lock.DistributedLock;
import com.ctrip.dcs.dsp.delay.util.JsonUtil;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.google.common.collect.Lists;
import credis.java.client.sync.trocks.TRocksCacheProvider;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 */
@Component
public class TRocksProviderProxy implements DistributedCache {

    private static final Logger logger = LoggerFactory.getLogger(TRocksProviderProxy.class);

    private static final int SIZE_LIMIT = 1000;

    @Qualifier("tRocksCacheProvider")
    @Autowired
    private TRocksCacheProvider trocksCacheProvider;

    @Override
    public String get(String key) {
        try {
            String v = trocksCacheProvider.get(key);
            logger.info("TRocksProviderProxy", "get,keys:{},value:{}", JsonUtil.toJson(key), JsonUtil.toJson(v));
            return v;
        } catch (Exception e) {
            logger.error(e);
        }
        return StringUtils.EMPTY;
    }

    @Override
    public Boolean set(String key, String value) {
        return trocksCacheProvider.set(key, value);
    }

    @Override
    public List<String> mget(List<String> keys) {
        List<String> result = Lists.newArrayList();
        List<List<String>> partition = Lists.partition(keys, SIZE_LIMIT);
        for (List<String> list : partition) {
            String[] k = list.toArray(new String[list.size()]);
            List<String> values = trocksCacheProvider.mget(k);
            result.addAll(values);
        }
        logger.info("TRocksProviderProxy", "mget,keys:{},values:{}", JsonUtil.toJson(keys), JsonUtil.toJson(result));
        return result;
    }

    @Override
    public Boolean setex(String key, String value, int seconds) {
        try {
            return trocksCacheProvider.setex(key, seconds, value);
        } catch (Exception e) {
            logger.error(e);
        }
        return false;
    }

    @Override
    public String mset(int seconds, String... keysvalues) {
        if (seconds <= 0) {
            return "ok";
        }
        logger.info("TRocksProviderProxy", "mset,seconds:{},keysvalues:{}", seconds, JsonUtil.toJson(keysvalues));
        if (keysvalues.length < 2 * SIZE_LIMIT) {
            return trocksCacheProvider.mset(seconds, keysvalues);
        }
        int i = 0, j = 2 * SIZE_LIMIT;
        while (i < keysvalues.length) {
            String[] kv = Arrays.copyOfRange(keysvalues, i, j);
            trocksCacheProvider.mset(seconds, kv);
            i = j;
            j = Math.min(keysvalues.length, j + 2 * SIZE_LIMIT);
        }
        return "ok";
    }

    @Override
    public Long incr(String key, long seconds) {
        Long count = trocksCacheProvider.incr(key);
        trocksCacheProvider.expire(key, seconds);
        return count;
    }

    @Override
    public Boolean del(String key) {
        return trocksCacheProvider.del(key);
    }

    @Override
    public Boolean exists(String key) {
        return trocksCacheProvider.exists(key);
    }

    @Override
    public Boolean expire(String key, long seconds) {
        return trocksCacheProvider.expire(key, seconds);
    }

    @Override
    public Long sadd(String key, long seconds, String... values) {
        Long count = trocksCacheProvider.sadd(key, values);
        trocksCacheProvider.expire(key, seconds);
        return count;
    }

    @Override
    public Long scard(String key) {
        return trocksCacheProvider.scard(key);
    }

}
