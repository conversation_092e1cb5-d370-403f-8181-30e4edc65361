package com.ctrip.dcs.dsp.delay.qconfig.value;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@NoArgsConstructor
public class DSPScheduleMatchValueVO implements CarConfigValueVO {

    private Integer timeRange;

    private Integer minInterval;

    private Integer maxInterval;

    private String isOut;

    private Integer outLimit;

    public DSPScheduleMatchValueVO(Integer timeRange, Integer minInterval, Integer maxInterval, String isOut, Integer outLimit) {
        this.timeRange = timeRange;
        this.minInterval = minInterval;
        this.maxInterval = maxInterval;
        this.isOut = isOut;
        this.outLimit = outLimit;
    }
}
