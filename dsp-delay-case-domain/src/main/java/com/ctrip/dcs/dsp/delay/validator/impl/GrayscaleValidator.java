package com.ctrip.dcs.dsp.delay.validator.impl;

import com.ctrip.dcs.dsp.delay.model.SupplyOrder;
import com.ctrip.dcs.dsp.delay.qconfig.GrayscaleQConfig;
import com.ctrip.dcs.dsp.delay.validator.Validator;
import com.ctrip.dcs.dsp.delay.validator.ValidatorCode;
import com.ctrip.dcs.dsp.delay.validator.ValidatorDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 灰度验证器
 * <AUTHOR>
 */
@Component("grayscaleValidator")
public class GrayscaleValidator implements Validator {

    @Autowired
    private GrayscaleQConfig grayscaleQConfig;

    @Override
    public ValidatorDTO validate(SupplyOrder order) {
        Boolean isGrayscaleUid = grayscaleQConfig.isGrayscaleUid(order.getUid());
        if (!isGrayscaleUid) {
            // 非灰度uid
            return new ValidatorDTO(ValidatorCode.GRAYSCALE_UID);
        }
        Boolean isRateLimit = grayscaleQConfig.isGrayscaleCityAndCarTypeIdRateLimit(order.getCityId(), order.getCarTypeId(), order.getOrderId());
        if (!isRateLimit) {
            // 非灰度城市流量比例
            return new ValidatorDTO(ValidatorCode.GRAYSCALE_CITY_RATE);
        }
        return new ValidatorDTO(ValidatorCode.OK);
    }
}
