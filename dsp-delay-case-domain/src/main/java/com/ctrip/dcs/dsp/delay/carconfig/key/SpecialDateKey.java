package com.ctrip.dcs.dsp.delay.carconfig.key;

import com.ctrip.dcs.dsp.delay.carconfig.Key;
import com.ctrip.dcs.dsp.delay.consts.CommonConstant;

/**
 * <AUTHOR>
 * @since 2024/8/12 11:48
 */
public class SpecialDateKey implements Key {
    
    private Integer cityId;
    
    private Integer carTypeId;
    
    @Override
    public String toKey() {
        return cityId + CommonConstant.PLACEHOLDER + carTypeId;
    }
    
    public Integer getCityId() {
        return cityId;
    }
    
    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }
    
    public Integer getCarTypeId() {
        return carTypeId;
    }
    
    public void setCarTypeId(Integer carTypeId) {
        this.carTypeId = carTypeId;
    }
}
