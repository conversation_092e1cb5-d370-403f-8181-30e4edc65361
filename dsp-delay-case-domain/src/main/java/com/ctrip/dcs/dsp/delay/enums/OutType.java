package com.ctrip.dcs.dsp.delay.enums;

/**
 * <AUTHOR>
 */
public enum OutType {

    TAKEN(0), REDISPATCH(1);

    private OutType(int code) {
        this.code = code;
    }

    private int code;

    public static boolean isTaken(Integer outType) {
        if (outType == null) {
            return false;
        }
        return outType == TAKEN.getCode();
    }

    public int getCode() {
        return code;
    }
}
