package com.ctrip.dcs.dsp.delay.infrastructure.converter;
import java.math.BigDecimal;

import com.ctrip.dcs.dsp.delay.enums.TransportGroupMode;
import com.ctrip.dcs.dsp.delay.model.DelayDspOrder;
import com.ctrip.dcs.dsp.delay.model.Driver;
import com.ctrip.dcs.dsp.delay.util.DateUtil;
import com.ctrip.dcs.oms.driver.order.domain.api.drvOrder.CreateDriverOrderRequestType;
import com.ctrip.dcs.oms.driver.order.domain.api.drvOrder.dto.*;
import com.ctrip.dcs.self.order.query.dto.BaseDetail;
import com.ctrip.dcs.self.order.query.dto.OrderDetail;
import com.ctrip.dcs.self.order.query.dto.OrderPackageService;
import com.ctrip.dcs.self.order.query.dto.OrderWayPointInfoDTO;
import com.ctrip.dcs.tms.transport.api.model.DriverInfo;
import com.ctrip.dcs.tms.transport.api.model.TransportGroup;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
public class DriverOrderConverter {

    public CreateDriverOrderRequestType toCreateDriverOrderRequestType(OrderDetail order, DriverInfo driver) {
        CreateDriverOrderRequestType request = new CreateDriverOrderRequestType();
        request.setDspOrder(toDspOrderDTO(order));
        request.setServiceProvider(toServiceProviderDTO(order));
        request.setSupplier(toSupplierDTO(driver));
        request.setTransportGroup(toTransportGroupDTO(driver));
        request.setDriver(toDriverDTO(driver));
        request.setCar(toCarDTO(driver));
        request.setDriverSupportServiceList(toDriverSupportServiceDTO(order, driver));
        request.setDriverOrderWayPointList(toDriverOrderWayPointList(order));
        // 忽略210虚拟派的库存
        request.setDispatchType("2");
        return request;
    }

    public List<DriverOrderWayPointDTO> toDriverOrderWayPointList(OrderDetail order) {
        return Optional.ofNullable(order)
                .map(OrderDetail::getWayPointInfoList)
                .orElse(Collections.emptyList())
                .stream()
                .filter(Objects::nonNull)
                .map(this::toDriverOrderWayPointDTO).collect(Collectors.toList());
    }

    public DriverOrderWayPointDTO toDriverOrderWayPointDTO(OrderWayPointInfoDTO orderWayPointDTO) {
        DriverOrderWayPointDTO driverOrderWayPointDTO = new DriverOrderWayPointDTO();
        driverOrderWayPointDTO.setUserOrderId( orderWayPointDTO.getUserOrderId() );
        driverOrderWayPointDTO.setDspOrderId( orderWayPointDTO.getDspOrderId() );
        driverOrderWayPointDTO.setPointIndex( orderWayPointDTO.getPointIndex() );
        driverOrderWayPointDTO.setPointName( orderWayPointDTO.getPointName() );
        driverOrderWayPointDTO.setPointAddress( orderWayPointDTO.getPointAddress() );
        driverOrderWayPointDTO.setPointLongitude( orderWayPointDTO.getPointLongitude() );
        driverOrderWayPointDTO.setPointLatitude( orderWayPointDTO.getPointLatitude() );
        driverOrderWayPointDTO.setPointCoordSys( orderWayPointDTO.getPointCoordSys() );
        driverOrderWayPointDTO.setCarPlaceId( orderWayPointDTO.getCarPlaceId() );
        driverOrderWayPointDTO.setWaitTimeLength( orderWayPointDTO.getWaitTimeLength() );
        driverOrderWayPointDTO.setWaitUnitPrice(  orderWayPointDTO.getWaitUnitPrice());
        driverOrderWayPointDTO.setPriceCurrency( orderWayPointDTO.getPriceCurrency() );
        return driverOrderWayPointDTO;
    }

    private List<XProductDTO> toDriverSupportServiceDTO(OrderDetail order, DriverInfo driver) {
        Set<String> set = Sets.newHashSet();
        if (driver.isRaisingPickUp()) {
            set.add("pick_up_card");
        }
        if (driver.isChildSeat()) {
            set.add("childseat");
        }
        return Optional.ofNullable(order).map(OrderDetail::getOrderPackageServices).orElse(Collections.emptyList()).stream().filter(x -> set.contains(x.getXCategoryCode())).map(x -> {
            XProductDTO dto = new XProductDTO();
            dto.setXSkuId(x.getXSkuId().intValue());
            dto.setXProductName(x.getXProductName());
            dto.setCategoryCode(x.getXCategoryCode());
            dto.setSupplierCategoryCode(x.getSupplierCategoryCode());
            dto.setQuantity(x.getQuantity());
            dto.setSupplierTotalPrice(x.getSupplierTotalPrice());
            dto.setSupplierCurrency(x.getSupplierCurrency());
            dto.setUserTotalPrice(x.getUserTotalPrice());
            dto.setUserCurrency(x.getUserCurrency());
            dto.setFirstFree(x.getFirstFree());
            dto.setUnitPrice(x.getUnitPrice());
            return dto;
        }).collect(Collectors.toList());
    }

    private CarDTO toCarDTO(DriverInfo driver) {
        CarDTO dto = new CarDTO();
        dto.setCarId(driver.getCarId());
        dto.setCarLicense(driver.getCarLicense());
        dto.setCarColorId(driver.getCarColorId());
        dto.setCarBrandId(driver.getCarBrandId());
        dto.setCarTypeId(driver.getCarTypeId().longValue());
        dto.setCarSeriesId(driver.getCarSeriesId());
        return dto;
    }

    private DriverDTO toDriverDTO(DriverInfo driver) {
        DriverDTO dto = new DriverDTO();
        dto.setDriverId(driver.getDriverId());
        dto.setDriverName(driver.getDriverName());
        dto.setCoopMode(driver.getCoopMode());
        dto.setDriverPhone(driver.getDriverPhone());
        dto.setDriverPhoneAreaCode(driver.getPhoneAreaCode());
        dto.setDriverLanguage(driver.getDriverLanguage());
        return dto;
    }

    private TransportGroupDTO toTransportGroupDTO(DriverInfo driver) {
        TransportGroup tg = null;
        if (CollectionUtils.isNotEmpty(driver.getTransportGroups())) {
            tg = driver.getTransportGroups().get(0);
            for (TransportGroup group : driver.getTransportGroups()) {
                if (group.getTransportGroupMode() == TransportGroupMode.REGISTER_DISPATCH.getCode()) {
                    tg = group;
                    break;
                }
            }
        }
        if (tg == null) {
            return null;
        }
        TransportGroupDTO dto = new TransportGroupDTO();
        dto.setTransportGroupId(tg.getTransportGroupId());
        dto.setTransportGroupName(tg.getTransportGroupName());
        dto.setTransportGroupMode(tg.getTransportGroupMode());
        return dto;
    }

    private DspOrderDTO toDspOrderDTO(OrderDetail order) {
        BaseDetail baseDetail = order.getBaseDetail();
        DspOrderDTO dto = new DspOrderDTO();
        dto.setDspOrderId(baseDetail.getDspOrderId());
        dto.setSupplyOrderId(baseDetail.getSupplyOrderId());
        dto.setUserOrderId(baseDetail.getUserOrderId());
        dto.setCategoryCode(baseDetail.getCategoryCode());
        dto.setIsInland(true);
        dto.setFromCityId(baseDetail.getFromCityId());
        dto.setToCityId(baseDetail.getToCityId());
        dto.setUseDays(baseDetail.getUseDays().intValue());
        dto.setSaleMode(baseDetail.getSalesMode());
        dto.setSysExpectBookTimeBJ(baseDetail.getEstimatedUseTimeBj());
        dto.setSysExpectBookTimeLocal(baseDetail.getEstimatedUseTime());
        dto.setSysExpectFinishTimeBJ(baseDetail.getPredicServiceStopTimeBj());
        dto.setIsSeparateOrder(false);
        dto.setCarTypeId(baseDetail.getVehicleGroupId().longValue());
        dto.setRewardAmount(BigDecimal.ZERO);
        dto.setVendorCurrency(order.getEstimatedCostInfo().getSupplierCurrency());
        dto.setUserCurrency(order.getEstimatedCostInfo().getUserCurrency());
        dto.setUid(baseDetail.getUid());
        dto.setTimeZone(baseDetail.getTimeZone());
        dto.setCrossBorderType(baseDetail.getCrossBorderType());
        dto.setCityId(baseDetail.getCityId().longValue());
        dto.setShortDisOrder(baseDetail.getShortDisOrder());
        return dto;
    }

    private ServiceProviderDTO toServiceProviderDTO(OrderDetail order) {
        BaseDetail baseDetail = order.getBaseDetail();
        ServiceProviderDTO dto = new ServiceProviderDTO();
        dto.setServiceProviderId(baseDetail.getServiceProviderId().longValue());
        return dto;
    }

    private SupplierDTO toSupplierDTO(DriverInfo driver) {
        SupplierDTO dto = new SupplierDTO();
        dto.setSupplierId(driver.getSupplierId());
        return dto;
    }
}
