package com.ctrip.dcs.dsp.delay.infrastructure.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 远程调用标记注解
 * <p>
 * 不支持幂等的服务不要标记！！！<br/>
 * 不支持幂等的服务不要标记！！！<br/>
 * 不支持幂等的服务不要标记！！！<br/>
 * <p>
 * 注：谨记方法名勿重复，避免混淆统计
 *
 * <AUTHOR> Zhang<PERSON>hen
 * @create 2023/2/12 19:47
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface Retryable {

    /**
     * 默认三次
     */
    int maxAttempts() default 3;

    /**
     * 执行需要重试的异常code
     * 若不配置，则请求异常才会重试
     * @return
     */
    Class<? extends Throwable>[] include() default {};

}