package com.ctrip.dcs.dsp.delay.interfaces.consumer;

import com.ctrip.dcs.dsp.delay.consts.CommonConstant;
import com.ctrip.dcs.dsp.delay.enums.SupplyOrderStatus;
import com.ctrip.dcs.dsp.delay.enums.SupplyOrderStatusDetail;
import com.ctrip.dcs.dsp.delay.gateway.SelfDispatcherOrderGateway;
import com.ctrip.dcs.dsp.delay.model.SupplyOrder;
import com.ctrip.dcs.dsp.delay.util.MetricsUtil;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.exception.BizException;
import com.ctrip.igt.framework.qmq.annontation.QmqLogTag;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.NeedRetryException;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;

import java.util.Objects;

/**
 * 临近最晚出池时间，订单还是服务商确认状态检查
 * <AUTHOR>
 */
@Component
public class DelayDspOrderDeadlineCheckConsumer {

    private static final Logger logger = LoggerFactory.getLogger(DelayDspOrderDeadlineCheckConsumer.class);

    @Autowired
    private SelfDispatcherOrderGateway selfDispatcherOrderGateway;

    @QmqLogTag()
    @QmqConsumer(prefix = CommonConstant.DELAY_DSP_ORDER_DEADLINE_CHECK_SUBJECT, consumerGroup = CommonConstant.APP_ID)
    public void onMessage(Message message) {
        try {
            String userOrderId = message.getStringProperty("userOrderId");
            int checkMinutes = message.getIntProperty("checkMinutes");
            if (StringUtils.isBlank(userOrderId)) {
                return;
            }
            SupplyOrder supplyOrder = selfDispatcherOrderGateway.queryByUserOrderId(userOrderId);
            if (supplyOrder == null) {
                throw new BizException("supplyOrder is null, userOrderId:" + userOrderId);
            }
            if (isServiceConfirm(supplyOrder)) {
                logger.warn("DelayDspOrderDeadlineCheckConsumerWarn", "DelayDspOrderDeadlineCheck, userOrderId:{}", userOrderId);
                MetricsUtil.recordValueWithTag("dispatch.delay.order.deadline.timeout", 1, "checkMinutes", String.valueOf(checkMinutes));
            }
        } catch (Exception e) {
            logger.warn("DelayDspOrderDeadlineCheckConsumerError", "DelayDspOrderDeadlineCheck", e);
            //60秒后再来重试
            throw new NeedRetryException(System.currentTimeMillis() + 60 * 1000, "DelayDspOrderDeadlineCheck");
        }
    }

    public boolean isServiceConfirm(SupplyOrder order) {
        if (order == null ) {
            return false;
        }
        return Objects.equals(order.getOrderStatus(), SupplyOrderStatus.SEND_ORDER.getCode()) && Objects.equals(order.getOrderStatusDetail(), SupplyOrderStatusDetail.SERVICE_CONFIRM.getCode());
    }
}
