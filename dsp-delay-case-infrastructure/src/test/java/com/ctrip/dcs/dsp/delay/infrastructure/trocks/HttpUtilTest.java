package com.ctrip.dcs.dsp.delay.infrastructure.trocks;

import com.ctrip.dcs.dsp.delay.infrastructure.util.HttpUtil;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.modules.junit4.PowerMockRunner;

/**
 * <AUTHOR>
 */
@RunWith(PowerMockRunner.class)
public class HttpUtilTest {

    @Test
    public void testFormatUrl() {
        HttpUtil.HttpPostParam param = new HttpUtil.HttpPostParam.Builder()
                .withUrl("http://carinner.qunar.com/dsp/test")
                .build();
        String s = param.formatUrl();
        Assert.assertEquals(s, "dsp_test");
    }
}
