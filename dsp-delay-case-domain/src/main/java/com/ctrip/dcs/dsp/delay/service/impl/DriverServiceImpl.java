package com.ctrip.dcs.dsp.delay.service.impl;

import com.ctrip.dcs.dsp.delay.carconfig.value.DriverProfitDayBaselineValue;
import com.ctrip.dcs.dsp.delay.consts.CommonConstant;
import com.ctrip.dcs.dsp.delay.enums.DelayDspTaskType;
import com.ctrip.dcs.dsp.delay.enums.YesOrNo;
import com.ctrip.dcs.dsp.delay.filter.FilterChain;
import com.ctrip.dcs.dsp.delay.gateway.DriverGateway;
import com.ctrip.dcs.dsp.delay.model.*;
import com.ctrip.dcs.dsp.delay.qconfig.DelayDspCommonQConfig;
import com.ctrip.dcs.dsp.delay.qconfig.GrayscaleQConfig;
import com.ctrip.dcs.dsp.delay.repository.DelayDspOrderRepository;
import com.ctrip.dcs.dsp.delay.service.DriverService;
import com.ctrip.dcs.dsp.delay.util.DateUtil;
import com.ctrip.dcs.dsp.delay.util.JsonUtil;
import com.ctrip.dcs.dsp.delay.util.MetricsUtil;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
public class DriverServiceImpl implements DriverService {

    private static final Logger logger = LoggerFactory.getLogger(DriverServiceImpl.class);

    @Autowired
    private DriverGateway driverGateway;

    @Autowired
    private DelayDspOrderRepository delayDspOrderRepository;

    @Autowired
    private FilterChain filterChain;

    @Autowired
    private DelayDspCommonQConfig delayDspCommonQConfig;

    @Autowired
    private GrayscaleQConfig grayscaleQConfig;

    @Override
    public List<DriverAggregation> queryDriverAggregationWithProfitFilter(DelayDspTask task, DriverProfitDayBaselineValue driverProfitDayBaselineValue) {
        // 查询司机
        List<Driver> drivers = queryDriver(task);
        logger.info("DriverServiceImpl_queryDriverAggregationWithProfitFilter_drivers", JsonUtil.toJson(drivers));
        if (CollectionUtils.isEmpty(drivers)) {
            return Lists.newArrayList();
        }
        // 查询司机已接订单
        Map<String/*driverId*/, List<DelayDspOrder>> taken = queryDriverOrder(task);
        logger.info("DriverServiceImpl_queryDriverAggregationWithProfitFilter_taken", JsonUtil.toJson(taken));
        // 查询司机时段外订单
        Map<String/*driverId*/, List<SupplyOrder>> out = queryDriverOutTimeOrder(task, drivers);
        logger.info("DriverServiceImpl_queryDriverAggregationWithProfitFilter_out", JsonUtil.toJson(out));
        //过滤掉收益达标的司机
        Boolean reachStandardCitySwitch = delayDspCommonQConfig.isReachStandardCitySwitch(task.getCityId());
        logger.info("DriverServiceImpl_queryDriverAggregationWithProfitFilter_reachStandardCitySwitch", JsonUtil.toJson(reachStandardCitySwitch));
        if(reachStandardCitySwitch){
            List<String> list = filterReachStandard(driverProfitDayBaselineValue, taken, out, Maps.newHashMap());
            drivers = drivers.stream().filter(driver -> !list.contains(driver.getDriverId())).collect(Collectors.toList());
        }
        logger.info("DriverServiceImpl_queryDriverAggregationWithProfitFilter_final_drivers", JsonUtil.toJson(drivers));
        if (CollectionUtils.isEmpty(drivers)) {
            return Lists.newArrayList();
        }
        // 查询司机分
        Map<String/*driverId*/, DriverScore> scores = queryDriverScore(drivers);
        logger.info("DriverServiceImpl_queryDriverAggregationWithProfitFilter_scores", JsonUtil.toJson(scores));
        List<DriverAggregation> result = Lists.newArrayList();
        for (Driver driver : drivers) {
            List<DelayDspOrder> list = taken.getOrDefault(driver.getDriverId(), Lists.newArrayList());
            List<SupplyOrder> outTimeOrders = out.getOrDefault(driver.getDriverId(), Lists.newArrayList());
            DriverScore score = scores.getOrDefault(driver.getDriverId(), new DriverScore(driver.getDriverId()));
            result.add(new DriverAggregation(driver, score, list, outTimeOrders));
        }
        // 司机分从高到低排序
        result.sort((d1, d2) -> d2.getScore().getTotalScore().compareTo(d1.getScore().getTotalScore()));
        MetricsUtil.recordValue("available.driver.count", result.size());
        logger.info("DriverServiceImpl_queryDriverAggregationWithProfitFilter_result", JsonUtil.toJson(result));
        return result;
    }

    @Override
    public List<String> queryPreOutPoolDrivers(DelayDspTask task, DriverProfitDayBaselineValue profitDayBaselineValue, Map<String, List<String>> driverOrderIdsMapping) {
        // 查询司机已接订单
        Map<String/*driverId*/, List<DelayDspOrder>> taken = queryDriverOrder(task);
        logger.info("DriverServiceImpl_queryPreOutPoolDrivers_taken", JsonUtil.toJson(taken));
        // 查询司机时段外订单
        Set<String> strings = driverOrderIdsMapping.keySet();
        Map<String/*driverId*/, List<SupplyOrder>> out = queryDriverOutTimeOrderWithDriverIds(task, new ArrayList<>(strings));
        logger.info("DriverServiceImpl_queryDriverAggregationWithProfitFilter_out", JsonUtil.toJson(out));
        List<String> list = filterReachStandard(profitDayBaselineValue, taken, out, driverOrderIdsMapping);
        logger.info("DriverServiceImpl_queryPreOutPoolDrivers_list", JsonUtil.toJson(list));
        return list;
    }

    private List<String> filterReachStandard(DriverProfitDayBaselineValue driverProfitDayBaselineValue, Map<String, List<DelayDspOrder>> taken, Map<String/*driverId*/, List<SupplyOrder>> out, Map<String, List<String>> delayDsp) {
        List<String> list = new ArrayList<>();
        double standardProfit = Double.parseDouble(driverProfitDayBaselineValue.getStandardProfit().toString());
        Map<String, List<String>> takenMap = taken.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue().stream().map(DelayDspOrder::getOrderId).collect(Collectors.toList())));
        Map<String, List<String>> outMap = out.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue().stream().map(SupplyOrder::getOrderId).collect(Collectors.toList())));
        Map<String, List<String>> map = mergeMaps(Lists.newArrayList(takenMap, outMap, delayDsp));
        map.forEach((driverId, orderIds) -> {
            boolean reachStandardDriver = getReachStandardDriver(driverId, standardProfit, orderIds);
            if (reachStandardDriver) {
                list.add(driverId);
            }
        });
        logger.info("DriverServiceImpl_filterReachStandard", JsonUtil.toJson(list));
        return list;
    }

    public Map<String, List<String>> mergeMaps(List<Map<String, List<String>>> maps) {
        logger.info("DriverServiceImpl_mergeMaps_req", JsonUtil.toJson(maps));
        Map<String, List<String>> collect = maps.stream()
                .flatMap(map -> map.entrySet().stream())
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue,
                        (list1, list2) -> {
                            Set<String> mergedSet = new HashSet<>(list1);
                            mergedSet.addAll(list2);
                            return new ArrayList<>(mergedSet);
                        }
                ));
        logger.info("DriverServiceImpl_mergeMaps_res", JsonUtil.toJson(collect));
        return collect;

    }

    private boolean getReachStandardDriver(String driverId, double standardProfit, List<String> orderIds) {
        List<DelayDspOrder> delayDspOrders = delayDspOrderRepository.queryByOrderIds(orderIds);
        List<DelayDspOrder> uniqueOrders = new ArrayList<>(delayDspOrders.stream().collect(Collectors.toMap(DelayDspOrder::getOrderId, Function.identity(), (existing, replacement) -> existing)).values());
        BigDecimal reduce = uniqueOrders.stream().map(x -> BigDecimal.valueOf(x.getDriverOrderFee())).reduce(BigDecimal.ZERO, BigDecimal::add);
        logger.info("DriverServiceImpl_getReachStandardDriver", "sum: " + reduce + ", driverId: " + driverId + ", standardProfit: " + standardProfit);
        return reduce.compareTo(BigDecimal.valueOf(standardProfit)) >= 0;
    }


    @Override
    public List<DriverAggregation> queryDriverAggregation(DriverProfitDayBaselineValue driverProfitDayBaselineValue, DelayDspTask task) {
        // 查询司机
        List<Driver> drivers = queryDriver(task);
        if (CollectionUtils.isEmpty(drivers)) {
            return Lists.newArrayList();
        }
        // 查询司机已接订单
        Map<String/*driverId*/, List<DelayDspOrder>> taken = queryDriverOrder(task);
        // 查询司机时段外订单
        Map<String/*driverId*/, List<SupplyOrder>> out = queryDriverOutTimeOrder(task, drivers);

        //过滤掉收益达标的司机,只对打包生效
        DelayDspTaskType of = DelayDspTaskType.getType(task.getTaskType());
        if(Objects.nonNull(of) && of.equals(DelayDspTaskType.DP) && Objects.nonNull(driverProfitDayBaselineValue) && driverProfitDayBaselineValue.getStandardProfit() > 0){
            Boolean reachStandardCitySwitch = delayDspCommonQConfig.isReachStandardCitySwitch(task.getCityId());
            logger.info("DriverServiceImpl_queryDriverAggregation_reachStandardCitySwitch", JsonUtil.toJson(reachStandardCitySwitch));
            if(reachStandardCitySwitch){
                List<String> list = filterReachStandard(driverProfitDayBaselineValue, taken, out, Maps.newHashMap());
                drivers = drivers.stream().filter(driver -> !list.contains(driver.getDriverId())).collect(Collectors.toList());
            }
        }
        logger.info("DriverServiceImpl_queryDriverAggregation_drivers", JsonUtil.toJson(drivers));
        if (CollectionUtils.isEmpty(drivers)) {
            return Lists.newArrayList();
        }

        // 查询司机分
        Map<String/*driverId*/, DriverScore> scores = queryDriverScore(drivers);

        List<DriverAggregation> result = Lists.newArrayList();
        for (Driver driver : drivers) {
            List<DelayDspOrder> list = taken.getOrDefault(driver.getDriverId(), Lists.newArrayList());
            List<SupplyOrder> outTimeOrders = out.getOrDefault(driver.getDriverId(), Lists.newArrayList());
            DriverScore score = scores.getOrDefault(driver.getDriverId(), new DriverScore(driver.getDriverId()));
            result.add(new DriverAggregation(driver, score, list, outTimeOrders));
        }
        // 司机分从高到低排序
        result.sort((d1, d2) -> d2.getScore().getTotalScore().compareTo(d1.getScore().getTotalScore()));
        MetricsUtil.recordValue("available.driver.count", result.size());
        return result;
    }

    @Override
    public List<DriverAggregation> queryDriverAggregationForInsert(DelayDspTask task) {
        // 查询司机
        List<Driver> drivers = queryDriver(task);
        logger.info("DriverServiceImpl_queryDriver", JsonUtil.toJson(drivers));
        if (CollectionUtils.isEmpty(drivers)) {
            return Lists.newArrayList();
        }
        // 查询司机分
        Map<String/*driverId*/, DriverScore> scores = queryDriverScore(drivers);
        logger.info("DriverServiceImpl_queryDriverScore", JsonUtil.toJson(scores));
        List<DriverAggregation> result = Lists.newArrayList();
        for (Driver driver : drivers) {
            DriverScore score = scores.getOrDefault(driver.getDriverId(), new DriverScore(driver.getDriverId()));
            result.add(new DriverAggregation(driver, score, null, null));
        }
        // 司机分从高到低排序
        result.sort((d1, d2) -> d2.getScore().getTotalScore().compareTo(d1.getScore().getTotalScore()));
        MetricsUtil.recordValue("available.driver.count", result.size());
        logger.info("DriverServiceImpl_result", JsonUtil.toJson(result));
        return result;
    }

    private Map<String, List<SupplyOrder>> queryDriverOutTimeOrder(DelayDspTask task, List<Driver> drivers) {
        if (CollectionUtils.isEmpty(drivers)) {
            return Maps.newHashMap();
        }
        List<String> driverIds = drivers.stream().map(Driver::getDriverId).collect(Collectors.toList());
        Map<String, List<SupplyOrder>> stringListMap = queryDriverOutTimeOrderWithDriverIds(task, driverIds);
        logger.info("DriverServiceImpl_queryDriverOutTimeOrder", JsonUtil.toJson(stringListMap));
        return stringListMap;
    }

    private Map<String, List<SupplyOrder>> queryDriverOutTimeOrderWithDriverIds(DelayDspTask task, List<String> driverIds) {
        return queryDriverOutTimeOrderFromC(task, driverIds);
    }


    private Map<String, List<SupplyOrder>> queryDriverOutTimeOrderFromC(DelayDspTask task, List<String> driverIds) {
        List<DelayDspOrder> list = Lists.newArrayList();
        try {
            List<DelayDspOrder> before = delayDspOrderRepository.query(driverIds, DateUtil.addHours(task.getBeginTime(), -CommonConstant.DRIVER_WORK_TIME_RANGE_HOUR), task.getBeginTime());
            list.addAll(before);
            List<DelayDspOrder> after = delayDspOrderRepository.query(driverIds, task.getEndTime(), DateUtil.addHours(task.getEndTime(), CommonConstant.DRIVER_WORK_TIME_RANGE_HOUR));
            list.addAll(after);
        } catch (Exception e) {
            logger.error(e);
        }
        return list.stream().map(this::toSupplyOrder).collect(Collectors.groupingBy(SupplyOrder::getDriverId));
    }

    @Override
    public List<Driver> queryDriver(DelayDspTask task) {
        // 初始化司机
        List<Driver> drivers = driverGateway.query(task.getCityId(), task.getCarTypeId(),task.getTaskType());
        logger.info("DriverServiceImpl_queryDriver", JsonUtil.toJson(drivers));
        // 过滤司机
        return filterChain.filter(drivers, task);
    }

    private Map<String/*driverId*/, DriverScore> queryDriverScore(List<Driver> drivers) {
        Set<String> driverIds = drivers.stream()
                .map(Driver::getDriverId)
                .collect(Collectors.toSet());
        List<DriverScore> driverScores = driverGateway.queryDriverScore(driverIds);
        return driverScores.stream()
                .collect(Collectors.toMap(DriverScore::getDrivId, s -> s));
    }

    private Map<String/*driverId*/, List<DelayDspOrder>> queryDriverOrder(DelayDspTask task) {
        List<DelayDspOrder> orders = delayDspOrderRepository.queryByTaskId(task.getTaskId(), YesOrNo.NO.getCode());
        Map<String/*driverId*/, List<DelayDspOrder>> taken = Maps.newHashMap();
        for (DelayDspOrder order : orders) {
            if (!taken.containsKey(order.getDriverId())) {
                taken.put(order.getDriverId(), Lists.newArrayList());
            }
            taken.get(order.getDriverId()).add(order);
        }
        return taken;
    }

    public SupplyOrder toSupplyOrder(DelayDspOrder delayDspOrder) {
        if (Objects.isNull(delayDspOrder)) {
            return null;
        }
        SupplyOrder supplyOrder = new SupplyOrder();
        supplyOrder.setOrderId(delayDspOrder.getOrderId());
        supplyOrder.setSourceOrderId(delayDspOrder.getMainOrderId());
        supplyOrder.setCityCode(delayDspOrder.getCityCode());
        supplyOrder.setCityId(delayDspOrder.getCityId());
        supplyOrder.setCarTypeId(delayDspOrder.getCarTypeId());
        supplyOrder.setSysExpectBookTime(delayDspOrder.getSysExpectBookTime());
        supplyOrder.setPredicServiceStopTime(delayDspOrder.getPredictServiceStopTime());
        supplyOrder.setIsOtaBookOrder(YesOrNo.NO.getCode());
        supplyOrder.setDriverId(delayDspOrder.getDriverId());
        supplyOrder.setSkuId(delayDspOrder.getSkuId());
        supplyOrder.setCategoryCode(delayDspOrder.getCategoryCode());
        supplyOrder.setDistributionChannel(delayDspOrder.getChannelId());
        supplyOrder.setDriverOrderFee(delayDspOrder.getDriverOrderFee());
        supplyOrder.setActualFromLongitude(delayDspOrder.getFromLongitude());
        supplyOrder.setActualFromLatitude(delayDspOrder.getFromLatitude());
        supplyOrder.setActualFromCoordsys(delayDspOrder.getFromCoordsys());
        supplyOrder.setActualToLongitude(delayDspOrder.getToLongitude());
        supplyOrder.setActualToLatitude(delayDspOrder.getToLatitude());
        supplyOrder.setActualToCoordsys(delayDspOrder.getToCoordsys());
        PredictPriceInfo predictPriceInfo = new PredictPriceInfo();
        predictPriceInfo.setKiloLength(delayDspOrder.getKiloLength());
        predictPriceInfo.setDrivTollFee(delayDspOrder.getDriverOrderFee());
        supplyOrder.setPredictPriceInfo(predictPriceInfo);
        return supplyOrder;
    }
}
