package com.ctrip.dcs.dsp.delay.validator.impl;

import com.ctrip.dcs.dsp.delay.gateway.TransportGateway;
import com.ctrip.dcs.dsp.delay.model.SupplyOrder;
import com.ctrip.dcs.dsp.delay.model.TransportSku;
import com.ctrip.dcs.dsp.delay.validator.Validator;
import com.ctrip.dcs.dsp.delay.validator.ValidatorCode;
import com.ctrip.dcs.dsp.delay.validator.ValidatorDTO;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.google.common.collect.Sets;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;

/**
 * 订单的sku是否关联了报名制运力组
 * <AUTHOR>
 */
@Component("skuValidator")
public class SkuValidator implements Validator {

    private static final Logger logger = LoggerFactory.getLogger(ChannelValidator.class);

    @Autowired
    private TransportGateway transportGateway;

    @Override
    public ValidatorDTO validate(SupplyOrder order) {
        List<TransportSku> list = transportGateway.queryTransportSku(order.getCityId(), order.getCarTypeId());
        if (CollectionUtils.isEmpty(list)) {
            logger.info("SkuValidator", "transport sku is empty!, order id: {}", order.getOrderId());
            return new ValidatorDTO(ValidatorCode.SKU);
        }
        Set<Long> skuIds = Sets.newHashSet();
        for (TransportSku ts : list) {
            skuIds.addAll(ts.getSkuIds());
        }
        if (!skuIds.contains(order.getSkuId())) {
            logger.info("SkuValidator", "transport sku is out of range!, order id: {}", order.getOrderId());
            return new ValidatorDTO(ValidatorCode.SKU);

        }
        return new ValidatorDTO(ValidatorCode.OK);
    }
}
