package com.ctrip.dcs.dsp.delay.service;

import com.ctrip.dcs.dsp.delay.carconfig.value.DriverProfitDayBaselineValue;
import com.ctrip.dcs.dsp.delay.model.DelayDspTask;
import com.ctrip.dcs.dsp.delay.model.Driver;
import com.ctrip.dcs.dsp.delay.model.DriverAggregation;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface DriverService {

    List<DriverAggregation> queryDriverAggregation(DriverProfitDayBaselineValue profitDayBaselineValue, DelayDspTask task);

    List<DriverAggregation> queryDriverAggregationForInsert(DelayDspTask task);

    List<Driver> queryDriver(DelayDspTask task);


    List<DriverAggregation> queryDriverAggregationWithProfitFilter(DelayDspTask task, DriverProfitDayBaselineValue driverProfitDayBaselineValue);

    List<String> queryPreOutPoolDrivers(DelayDspTask delayDspTask, DriverProfitDayBaselineValue profitDayBaselineValue, Map<String, List<String>> driverOrderIdsMapping);
}
