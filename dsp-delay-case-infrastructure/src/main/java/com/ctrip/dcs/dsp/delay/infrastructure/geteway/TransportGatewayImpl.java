package com.ctrip.dcs.dsp.delay.infrastructure.geteway;

import com.ctrip.dcs.dsp.delay.enums.CategoryCode;
import com.ctrip.dcs.dsp.delay.gateway.TransportGateway;
import com.ctrip.dcs.dsp.delay.infrastructure.converter.CommonConverter;
import com.ctrip.dcs.dsp.delay.infrastructure.soa.TmsTransportServiceProxy;
import com.ctrip.dcs.dsp.delay.model.SupplyOrder;
import com.ctrip.dcs.dsp.delay.model.TransportGroupInfo;
import com.ctrip.dcs.dsp.delay.model.TransportSku;
import com.ctrip.dcs.dsp.delay.util.MetricsUtil;
import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.igt.ResponseResult;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
public class TransportGatewayImpl implements TransportGateway {

    private static final Logger logger = LoggerFactory.getLogger(TransportGatewayImpl.class);

    @Autowired
    private TmsTransportServiceProxy transportServiceProxy;

    public List<TransportSku> queryTransportSku(Integer cityId, Integer carTypeId) {

        try {
            QueryApplyTransGroupsSkuForDspRequestType request = new QueryApplyTransGroupsSkuForDspRequestType();
            request.setCityIds(Lists.newArrayList(cityId.longValue()));
            request.setCarTypeIds(Lists.newArrayList(carTypeId.longValue()));

            QueryApplyTransGroupsSkuForDspResponseType response = transportServiceProxy.queryApplyTransGroupsSkuForDsp(request);
            boolean success = Optional.ofNullable(response).map(QueryApplyTransGroupsSkuForDspResponseType::getResponseResult).map(ResponseResult::isSuccess).orElse(false);
            if (!success) {
                MetricsUtil.recordValue("query.apply.transport.sku.soa.error", 1);
                return Lists.newArrayList();
            }
            List<QueryApplyTransGroupsSkuForDspInfo> list = Optional.ofNullable(response)
                    .map(QueryApplyTransGroupsSkuForDspResponseType::getData)
                    .orElse(Lists.newArrayList());

            return CommonConverter.INSTANCE.toTransportSku(list);
        } catch (Exception e) {
            logger.error(e);
        }
        return Lists.newArrayList();
    }

    @Override
    public List<TransportGroupInfo> queryTransportGroups(SupplyOrder supplyOrder) {
        QueryTransportGroupsSOARequestType request = toQueryTransportGroupsSOARequestType(supplyOrder);
        QueryTransportGroupsSOAResponseType response = transportServiceProxy.queryTransportGroups(request);
        return toTransportGroupList(response);
    }

    public QueryTransportGroupsSOARequestType toQueryTransportGroupsSOARequestType(SupplyOrder supplyOrder) {
        QueryTransportGroupsSOARequestType request = new QueryTransportGroupsSOARequestType();
        request.setSkuId(supplyOrder.getSkuId());
        if (CategoryCode.isPickup(supplyOrder.getCategoryCode())) { // 接机或接站
            request.setPoiType(supplyOrder.getActualToCoordsys());
            request.setLongitude(supplyOrder.getActualToLongitude());
            request.setLatitude(supplyOrder.getActualToLatitude());
            request.setCarPlaceId(supplyOrder.getArriveCarPlaceId());
        }
        if (CategoryCode.isDropOff(supplyOrder.getCategoryCode())) { // 送机或送站
            request.setPoiType(supplyOrder.getActualFromCoordsys());
            request.setLongitude(supplyOrder.getActualFromLongitude());
            request.setLatitude(supplyOrder.getActualFromLatitude());
            request.setCarPlaceId(supplyOrder.getDeptCarPlaceId());
        }
        request.setGroupStatus(NumberUtils.INTEGER_ZERO);
        return request;
    }

    public List<TransportGroupInfo> toTransportGroupList(QueryTransportGroupsSOAResponseType response) {
        List<TransportGroupDetailSOAType> details = Optional.ofNullable(response).map(QueryTransportGroupsSOAResponseType::getData).orElse(Collections.emptyList());
        return details.stream()
                .filter(Objects::nonNull)
                .map(this::toTransportGroup)
                .collect(Collectors.toList());
    }

    private TransportGroupInfo toTransportGroup(TransportGroupDetailSOAType dto) {
        Long transportGroupId = Optional.of(dto).map(TransportGroupDetailSOAType::getTransportGroupId).orElse(NumberUtils.LONG_ZERO);
        TransportGroupInfo transportGroup = new TransportGroupInfo();
        transportGroup.setTransportGroupId(transportGroupId.intValue());
        transportGroup.setTransportGroupName(dto.getTransportGroupName());
        transportGroup.setTransportGroupMode(dto.getTransportGroupMode());
        transportGroup.setSupplierId(dto.getSupplierId());
        transportGroup.setTakeOrderLimitTime(dto.getTakeOrderLimitTime());
        transportGroup.setInOrderConfigs(dto.getInOrderConfigs());
        transportGroup.setDispatcherLanguage(dto.getDispatcherLanguage());
        transportGroup.setDispatcherPhone(dto.getDispatcherPhone());
        transportGroup.setShortTransportGroup(dto.getShortTransportGroup());
        return transportGroup;
    }

}
