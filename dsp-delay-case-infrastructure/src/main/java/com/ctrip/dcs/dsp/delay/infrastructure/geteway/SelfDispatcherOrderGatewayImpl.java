package com.ctrip.dcs.dsp.delay.infrastructure.geteway;

import com.ctrip.dcs.dsp.delay.enums.OrderTakenCode;
import com.ctrip.dcs.dsp.delay.gateway.SelfDispatcherOrderGateway;
import com.ctrip.dcs.dsp.delay.infrastructure.annotation.Retryable;
import com.ctrip.dcs.dsp.delay.infrastructure.converter.SupplyOrderConverter;
import com.ctrip.dcs.dsp.delay.infrastructure.exception.SOANeedRetryException;
import com.ctrip.dcs.dsp.delay.infrastructure.soa.SelfDispatcherOrderServiceProxy;
import com.ctrip.dcs.dsp.delay.infrastructure.soa.SelfOrderQueryServiceProxy;
import com.ctrip.dcs.dsp.delay.infrastructure.util.ResponseUtil;
import com.ctrip.dcs.dsp.delay.model.SupplyOrder;
import com.ctrip.dcs.dsp.delay.qconfig.DelayDspCommonQConfig;
import com.ctrip.dcs.dsp.delay.util.JsonUtil;
import com.ctrip.dcs.self.dispatchorder.interfaces.*;
import com.ctrip.dcs.self.dispatchorder.interfaces.dto.AvailableDriverDTO;
import com.ctrip.dcs.self.order.query.api.*;
import com.ctrip.dcs.self.order.query.dto.DataSwitch;
import com.ctrip.dcs.self.order.query.dto.DataSwitchForSchedule;
import com.ctrip.igt.RequestHeader;
import com.ctrip.igt.ResponseResult;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
public class SelfDispatcherOrderGatewayImpl implements SelfDispatcherOrderGateway {

    private static final Logger logger = LoggerFactory.getLogger(SelfDispatcherOrderGatewayImpl.class);

    @Autowired
    private SelfDispatcherOrderServiceProxy dispatcherOrderServiceProxy;

    @Autowired
    private SelfOrderQueryServiceProxy orderQueryServiceProxy;

    @Autowired
    private DelayDspCommonQConfig delayDspCommonQConfig;

    @Autowired
    private SupplyOrderConverter supplyOrderConverter;

    @Override
    public SupplyOrder queryBase(String orderId) {
        try {
            QueryOrderDetailForScheduleRequestType request = new QueryOrderDetailForScheduleRequestType();
            request.setDspOrderId(orderId);
            DataSwitchForSchedule dataSwitch = new DataSwitchForSchedule();
            dataSwitch.setDspOrderBaseSwitch(true);
            request.setDataSwitch(dataSwitch);
            QueryOrderDetailForScheduleResponseType responseType = orderQueryServiceProxy.queryOrderDetailForSchedule(request);
            SupplyOrder supplyOrder = supplyOrderConverter.toSupplyOrderForSchedule(responseType);
            logger.info("SelfDispatcherOrderGatewayImpl_queryBase", JsonUtil.toJson(supplyOrder));
            return supplyOrder;
        } catch (Exception e) {
            logger.error(e);
        }
        return null;
    }

    @Override
    public SupplyOrder query(String orderId) {
        try {
            QueryOrderDetailRequestType request = new QueryOrderDetailRequestType();
            request.setDspOrderId(orderId);
            DataSwitch dataSwitch = new DataSwitch();
            dataSwitch.setBaseDetailSwitch(true);
            dataSwitch.setEstimatedCostInfoSwitch(true);
            dataSwitch.setActualCostInfoSwitch(true);
            dataSwitch.setSopRecordSwitch(true);
            dataSwitch.setOrderStandardRecordSwitch(true);
            dataSwitch.setOrderPackageServiceSwitch(true);
            dataSwitch.setCancelRecordSwitch(true);
            dataSwitch.setBreakAppointmentRecordSwitch(true);
            dataSwitch.setConfirmRecordSwitch(true);
            dataSwitch.setPlatformPriceStrategy(true);
            request.setDataSwitch(dataSwitch);
            QueryOrderDetailResponseType responseType = orderQueryServiceProxy.queryOrderDetail(request);
            SupplyOrder supplyOrder = supplyOrderConverter.toSupplyOrder(responseType);
            logger.info("SelfDispatcherOrderGatewayImpl_query_supplyOrder", JsonUtil.toJson(supplyOrder));
            return supplyOrder;
        } catch (Exception e) {
            logger.error(e);
        }
        return null;
    }

    @Override
    public Boolean isRedispatch(String userOrderId) {
        QueryOrderRedispatchRecordsRequestType request = new QueryOrderRedispatchRecordsRequestType();
        request.setUserOrderId(userOrderId);
        QueryOrderRedispatchRecordsResponseType response = orderQueryServiceProxy.queryOrderRedispatchRecords(request);
        List<OrderRedispatchRecordDTO> list = Optional.ofNullable(response).map(QueryOrderRedispatchRecordsResponseType::getRedispatchRecords).orElse(Collections.emptyList());
        return CollectionUtils.isNotEmpty(list);
    }

    @Override
    public SupplyOrder queryByUserOrderId(String userOrderId) {
        try {
            QueryOrderDetailRequestType request = new QueryOrderDetailRequestType();
            request.setUserOrderId(userOrderId);
            DataSwitch dataSwitch = new DataSwitch();
            dataSwitch.setBaseDetailSwitch(true);
            request.setDataSwitch(dataSwitch);
            QueryOrderDetailResponseType responseType = orderQueryServiceProxy.queryOrderDetail(request);
            return supplyOrderConverter.toSupplyOrder(responseType);
        } catch (Exception e) {
            logger.error(e);
        }
        return null;
    }

    @Retryable
    @Override
    public void redispatch(String userOrderId) {
        ReDispatchSubmitRequestType request = new ReDispatchSubmitRequestType();
        request.setRequestHeader(new RequestHeader());
        request.setUserOrderId(userOrderId);
        request.setRoleId(delayDspCommonQConfig.getRedispatchRoleId());
        request.setReasonDetailId(delayDspCommonQConfig.getRedispatchReasonDetailId());
        request.setReasonId(delayDspCommonQConfig.getRedispatchReasonId());
        ReDispatchSubmitResponseType responseType = dispatcherOrderServiceProxy.reDispatchSubmit(request);
        if (!ResponseUtil.isSuccess(responseType)) {
            throw new SOANeedRetryException();
        }
    }

    @Retryable
    @Override
    public int taken(String dspOrderId, String driverId, Integer transportGroupId, String duid) {
        ConfirmDelayDspOrderRequestType request = new ConfirmDelayDspOrderRequestType();
        request.setDspOrderId(dspOrderId);
        request.setDriverId(Long.valueOf(driverId));
        request.setTransportGroupId(Long.valueOf(transportGroupId));
        request.setDuid(duid);
        ConfirmDelayDspOrderResponseType responseType = dispatcherOrderServiceProxy.confirmDelayDspOrder(request);
        Boolean success = Optional.ofNullable(responseType).map(ConfirmDelayDspOrderResponseType::getResponseResult).map(ResponseResult::isSuccess).orElse(false);
        if(success){
            return OrderTakenCode.SUCCESS.getCode();
        }
        if(responseType == null || responseType.getResponseResult() == null || responseType.getResponseResult().getReturnCode() == null){
            return OrderTakenCode.ERROR.getCode();
        }
        return Integer.parseInt(responseType.getResponseResult().getReturnCode());
    }


    @Override
    public Map<String, String> queryAvailableDrivers(String orderId, String duid) {
        QueryAvailableDriversRequestType request = new QueryAvailableDriversRequestType();
        request.setDspOrderId(orderId);
        request.setDuid(duid);
        QueryAvailableDriversResponseType response = dispatcherOrderServiceProxy.queryAvailableDrivers(request);
        return Optional.ofNullable(response)
                .map(QueryAvailableDriversResponseType::getAvailableDrivers)
                .orElse(Collections.emptyList())
                .stream()
                .collect(Collectors.toMap(d -> d.getDriverId().toString(), AvailableDriverDTO::getValue, (k1, k2) -> k2));
    }
}
