package com.ctrip.dcs.dsp.delay.carconfig;

import com.ctrip.dcs.dsp.delay.carconfig.key.DelayDspTaskKeyV2;
import com.ctrip.dcs.dsp.delay.carconfig.value.DelayDspTaskValue;
import com.ctrip.dcs.dsp.delay.consts.CommonConstant;
import com.ctrip.dcs.dsp.delay.lock.DistributedLock;
import com.ctrip.dcs.dsp.delay.mq.MessageProducer;
import com.ctrip.dcs.dsp.delay.qconfig.DelayDspCommonQConfig;
import com.ctrip.dcs.dsp.delay.util.JsonUtil;
import com.ctrip.dcs.dsp.delay.util.MetricsUtil;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerContext;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.google.common.collect.Lists;
import com.googlecode.aviator.utils.ArrayHashMap;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qconfig.client.spring.QConfig;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
public class DelayTaskConfigV2 {

    private static final Logger logger = LoggerFactory.getLogger(DelayTaskConfigV2.class);

    public static final String SPLIT = "#";

    @Autowired
    private MessageProducer messageProducer;
    @Autowired
    private DelayDspCommonQConfig delayDspCommonQConfig;
    @Autowired
    private DistributedLock distributedLock;


    private HashMap<String, Config> map = new HashMap<>();

    @QConfig("visualConfig_delayTaskConf_1.json")
    public void onChange(HashMap<String, Config> newMap) {//监听模式
        HashMap<String, Config> originMap = map;
        if(map.isEmpty()){
            //应用刚启动的时候，map为空，直接赋值
            map = newMap;
            return;
        }
        map = newMap;
        compareAndSendMessage(newMap, originMap);
    }


    public void compareAndSendMessage(HashMap<String, Config> newMap, HashMap<String, Config> originMap) {
        try{
            LoggerContext.newContext().withGlobalTraceId(UUID.randomUUID().toString()).init();
            //修改后半小时内的修改都不会同步
            Integer seconds = delayDspCommonQConfig.getOutPoolTimeChangeEffectTime();
            logger.info("DelayTaskConfig_compareAndSendMessage_seconds", JsonUtil.toJson(seconds));
            boolean ok = distributedLock.tryAcquire("visualConfig_delayTaskConf_1", "ok", seconds);
            logger.info("DelayTaskConfig_compareAndSendMessage_lock", JsonUtil.toJson(ok));
            if (!ok) {
                return;
            }
            Map<String, List<DelayDspTaskValue>> configs = new HashMap<>();
            for (Map.Entry<String, Config> entry : newMap.entrySet()) {
                Map<String, List<DelayDspTaskValue>> stringListMap = compareConfig(entry, originMap);
                if(Objects.nonNull(stringListMap)){
                    configs.putAll(stringListMap);
                }
            }
            logger.info("DelayTaskConfig_onChange_configs", JsonUtil.toJson(configs));
            if (configs.isEmpty()) {
                return;
            }
            Map<String, Object> data = new ArrayHashMap<>();
            data.put("list", JsonUtil.toJson(configs));
            messageProducer.sendMessage(CommonConstant.DELAY_DSP_OUT_POOL_TIME_UPDATE, data);
        }catch (Exception exception){
            logger.error("DelayTaskConfig_compareAndSendMessage_error", exception);
            MetricsUtil.recordValue("delayDsp.compareAndSendMessage.error", 1);
        }
    }


    private Map<String, List<DelayDspTaskValue>> compareConfig(Map.Entry<String, Config> entry, HashMap<String, Config> originMap) {
        try {
            String cityId = entry.getKey();
            if (StringUtils.isEmpty(cityId)) {
                return null;
            }
            Boolean outPoolTimeChangeCitySwitch = delayDspCommonQConfig.isOutPoolTimeChangeCitySwitch(cityId);
            logger.info("DelayTaskConfig_compareConfig_outPoolTimeChangeCitySwitch", JsonUtil.toJson(outPoolTimeChangeCitySwitch));
            if (!outPoolTimeChangeCitySwitch) {
                return null;
            }
            Config originConfig = originMap.get(cityId);
            Config newConfig = entry.getValue();
            if (originConfig == null || newConfig == null || CollectionUtils.isEmpty(originConfig.getValues()) || CollectionUtils.isEmpty(newConfig.getValues())) {
                logger.info("DelayTaskConfig_compareConfig_not_match", cityId);
                return null;
            }
            Map<String, DelayDspTaskValue> originConfigMap = originConfig.getValues().stream().collect(Collectors.toMap(x -> x.getBegin() + SPLIT + x.getEnd(), t -> t));
            Map<String, DelayDspTaskValue> newConfigMap = newConfig.getValues().stream().collect(Collectors.toMap(x -> x.getBegin() + SPLIT + x.getEnd(), t -> t));
            List<DelayDspTaskValue> delayDspTaskValueList = Lists.newArrayList();
            newConfigMap.forEach((k, v) -> {
                DelayDspTaskValue delayDspTaskValueTemp = buildDelayDspTaskValue(originConfigMap, k, v);
                if(Objects.nonNull(delayDspTaskValueTemp)){
                    delayDspTaskValueList.add(delayDspTaskValueTemp);
                }
            });
            logger.info("DelayTaskConfig_compareConfig_delayDspTaskValueList", JsonUtil.toJson(delayDspTaskValueList));
            if (CollectionUtils.isEmpty(delayDspTaskValueList)) {
                return null;
            }
            DelayDspTaskKeyV2 key1 = new DelayDspTaskKeyV2(Integer.parseInt(cityId));
            logger.info("DelayTaskConfig_compareConfig_key1", JsonUtil.toJson(key1));
            Map<String, List<DelayDspTaskValue>> map = new HashMap<>();
            map.put(key1.toKey(), delayDspTaskValueList);
            return map;
        }catch (Exception e){
            logger.error("DelayTaskConfig_compareConfig_error", e);
            MetricsUtil.recordValue("delayDsp.changeOutPoolTime.error", 1);
            return null;
        }
    }

    public DelayDspTaskValue buildDelayDspTaskValue(Map<String, DelayDspTaskValue> originConfigMap, String k, DelayDspTaskValue v) {
        DelayDspTaskValue originValue = originConfigMap.get(k);
        if (originValue == null) {
            logger.info("DelayTaskConfig_compareConfig_newValue", JsonUtil.toJson(v));
            return null;
        }
        if (!v.getType().equals(originValue.getType())) {
            logger.info("DelayTaskConfig_compareConfig_change_type", JsonUtil.toJson(v));
            return null;
        }
        if (v.getHour().equals(originValue.getHour())) {
            logger.info("DelayTaskConfig_compareConfig_change_hour", JsonUtil.toJson(v));
            return null;
        }
        return v;
    }


    public DelayDspTaskValue get(Integer cityId, Date date, List<String> types) {
        DelayDspTaskKeyV2 key = new DelayDspTaskKeyV2(cityId);
        Config config = map.get(key.toKey());
        logger.info("DelayTaskConfig_get_cityId", JsonUtil.toJson(config));
        if (config == null || config.getValues() == null) {
            MetricsUtil.recordValue("DelayTaskConfig2.config.null", 1);
            return null;
        }
        for (DelayDspTaskValue value : config.getValues()) {
            //增加判断type 不为空，需要判断type
            if (!org.springframework.util.CollectionUtils.isEmpty(types)) {
                if (!types.contains(value.getType())) {
                    continue;
                }
            }
            if (value.match(date)) {
                return value;
            }
        }
        MetricsUtil.recordValue("DelayTaskConfig2.config.null", 1);
        return null;
    }

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Config {

        private DelayDspTaskKeyV2 key;

        private List<DelayDspTaskValue> values;
    }


}
