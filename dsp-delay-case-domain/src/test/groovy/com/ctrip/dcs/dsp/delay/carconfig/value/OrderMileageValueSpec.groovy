package com.ctrip.dcs.dsp.delay.carconfig.value

import com.ctrip.dcs.dsp.delay.util.DateUtil
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 */
class OrderMileageValueSpec extends Specification {

    def value = new OrderMileageValue("startKilo" : 10, "endKilo" : 50, "value" : 10D)

    @Unroll
    def "match"() {

        given: "Mock数据"
        value.setStartTime(startTime)
        value.setEndTime(endTime)

        when: "执行校验方法"
        def res = value.match(param)

        then: "验证校验结果"
        res == ok

        where:
        param                                          | startTime | endTime || ok
        getParameter(5D, "2022-05-07 12:00:00")  | "08:00"   | "20:00" || false
        getParameter(55D, "2022-05-07 12:00:00") | "08:00"   | "20:00" || false
        getParameter(30, "2022-05-07 06:00:00")  | "08:00"   | "20:00" || false
        getParameter(30, "2022-05-07 22:00:00")  | "08:00"   | "20:00" || false
        getParameter(30, "2022-05-07 12:00:00")  | "08:00"   | "20:00" || true
        getParameter(30, "2022-05-07 12:00:00")  | "20:00"   | "08:00" || false
        getParameter(30, "2022-05-08 09:00:00")  | "20:00"   | "08:00" || false
        getParameter(30, "2022-05-07 23:00:00")  | "20:00"   | "08:00" || true
        getParameter(30, "2022-05-08 03:00:00")  | "20:00"   | "08:00" || true
    }

    def getParameter(double k, String d) {
        return new OrderMileageValue.Parameter(k, DateUtil.parseDate(d))
    }
}
