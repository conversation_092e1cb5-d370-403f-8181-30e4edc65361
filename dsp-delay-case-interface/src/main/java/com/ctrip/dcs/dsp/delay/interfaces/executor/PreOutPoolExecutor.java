package com.ctrip.dcs.dsp.delay.interfaces.executor;

import com.ctrip.dcs.dsp.delay.model.PreOutPoolDTO;
import com.ctrip.dcs.dsp.delay.service.PreOutPoolService;
import com.ctrip.dcs.dsp.delay.service.interfaces.message.PreOutPoolRequestType;
import com.ctrip.dcs.dsp.delay.service.interfaces.message.PreOutPoolResponseType;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class PreOutPoolExecutor extends AbstractRpcExecutor<PreOutPoolRequestType, PreOutPoolResponseType> implements Validator<PreOutPoolRequestType> {

    @Autowired
    private PreOutPoolService preOutPoolService;

    @Override
    public void validate(AbstractValidator<PreOutPoolRequestType> validator) {
        validator.ruleFor("userOrderId").notNull().notEmpty();
        validator.ruleFor("pressedDispatchSource").notNull().notEmpty();

    }

    @Override
    public PreOutPoolResponseType execute(PreOutPoolRequestType requestType) {
        PreOutPoolResponseType responseType = new PreOutPoolResponseType();
        int resCode = preOutPoolService.preOutPool(getPreOutPoolDTO(requestType));
        responseType.setResCode(resCode);
        return ServiceResponseUtils.success(responseType);
    }

    private PreOutPoolDTO getPreOutPoolDTO(PreOutPoolRequestType requestType) {
        PreOutPoolDTO preOutPoolDTO = new PreOutPoolDTO();
        preOutPoolDTO.setUserOrderId(requestType.getUserOrderId());
        preOutPoolDTO.setPressedDispatchSource(requestType.getPressedDispatchSource());
        preOutPoolDTO.setCustomerServiceUid(requestType.getCustomerServiceUid());
        return preOutPoolDTO;
    }
}
