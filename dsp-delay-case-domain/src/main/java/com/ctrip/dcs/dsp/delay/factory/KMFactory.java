package com.ctrip.dcs.dsp.delay.factory;

import com.ctrip.dcs.dsp.delay.core.KM;
import com.ctrip.dcs.dsp.delay.model.DelayDspOrder;
import com.ctrip.dcs.dsp.delay.model.Driver;
import com.google.common.collect.Lists;
import com.google.common.collect.Table;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Component
public class KMFactory {

    public KM create(Map<String, DelayDspOrder> orderMap, Map<Long, Driver> driverMap, Table<Long /*driverId*/, String /*orderId*/, Double /*value*/> table) {
        List<Long> driverIds = Lists.newArrayList(driverMap.keySet());
        List<String> orderIds = Lists.newArrayList(orderMap.keySet());
        int n = Math.max(driverIds.size(), orderIds.size());
        int[][] graph = new int[n][n];
        for (int i = 0; i < n; i++) {
            for (int j = 0; j < n; j++) {
                if (i >= orderIds.size() || j >= driverIds.size()) {
                    graph[i][j] = 0;
                    continue;
                }
                String orderId = orderIds.get(i);
                Long driverId = driverIds.get(j);
                Double value = table.get(driverId, orderId);
                graph[i][j] = value == null ? 0 : Double.valueOf(value * 1000).intValue();
            }
        }
        return new KM(graph);
    }
}
