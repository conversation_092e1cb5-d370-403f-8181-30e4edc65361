package com.ctrip.dcs.dsp.delay.interfaces.executor

import com.ctrip.dcs.dsp.delay.model.DelayDspConfigResInfoDTO
import com.ctrip.dcs.dsp.delay.service.DelayTaskService
import com.ctrip.dcs.dsp.delay.service.PreOutPoolService
import com.ctrip.dcs.dsp.delay.service.interfaces.message.PreOutPoolRequestType
import com.ctrip.dcs.dsp.delay.service.interfaces.message.PreOutPoolResponseType
import com.ctrip.dcs.dsp.delay.service.interfaces.message.QueryDelayDspConfigInfoRequestType
import com.ctrip.dcs.dsp.delay.service.interfaces.message.QueryDelayDspConfigResInfoResponseType
import com.ctrip.dcs.dsp.transport.inventory.service.api.dto.DelayDspConfigInfo
import com.google.common.collect.Lists
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import spock.lang.Specification

import static org.mockito.ArgumentMatchers.any
import static org.mockito.Mockito.when

class PreOutPoolExecutorTest extends Specification {
    @Mock
    PreOutPoolService preOutPoolService
    @InjectMocks
    PreOutPoolExecutor preOutPoolExecutor

    def setup() {
        MockitoAnnotations.initMocks(this)
    }


    def "test execute"() {
        given:
        when(preOutPoolService.preOutPool(any())).thenReturn(1)

        when:
        PreOutPoolRequestType requestType = new PreOutPoolRequestType();
        requestType.setUserOrderId("1")
        requestType.setPressedDispatchSource(1)
        requestType.setCustomerServiceUid(null)
        PreOutPoolResponseType result = preOutPoolExecutor.execute(requestType)

        then:
        result != null
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme