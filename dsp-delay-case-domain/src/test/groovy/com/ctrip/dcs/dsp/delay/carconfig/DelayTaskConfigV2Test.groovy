package com.ctrip.dcs.dsp.delay.carconfig

import com.ctrip.dcs.dsp.delay.carconfig.key.DelayDspTaskKeyV2
import com.ctrip.dcs.dsp.delay.carconfig.value.DelayDspTaskValue
import com.ctrip.dcs.dsp.delay.lock.DistributedLock
import com.ctrip.dcs.dsp.delay.mq.MessageProducer
import com.ctrip.dcs.dsp.delay.qconfig.DelayDspCommonQConfig
import com.ctrip.dcs.dsp.delay.util.DateUtil
import com.google.common.collect.Maps
import org.assertj.core.util.Lists
import org.junit.Assert
import spock.lang.Specification
import spock.lang.Unroll

class DelayTaskConfigV2Test extends Specification {


    def messageProducer = Mock(MessageProducer)
    def delayDspCommonQConfig = Mock(DelayDspCommonQConfig)
    def distributedLock = Mock(DistributedLock)
    def testObj = new DelayTaskConfigV2(messageProducer: messageProducer, delayDspCommonQConfig: delayDspCommonQConfig, distributedLock: distributedLock)


    def setup() {
        testObj.map = buildConfigMap()
    }


    def "test onChange1"() {
        given:
        testObj.map = Maps.newHashMap()

        when:
        HashMap<String, DelayTaskConfigV2.Config> newMap = Maps.newHashMap();
        newMap.put("11", new DelayTaskConfigV2.Config());
        testObj.onChange(newMap);

        then:
        Assert.assertTrue(Objects.nonNull(newMap));
    }

    def "test onChange2"() {
        given:
        testObj.map = buildConfigMap1();

        HashMap<String, DelayTaskConfigV2.Config> newMap = buildConfigMap();
        when:
        testObj.onChange(newMap);

        then:
        Assert.assertTrue(Objects.nonNull(newMap));
    }

    @Unroll
    def "test compareAndSendMessage"() {
        given:
        delayDspCommonQConfig.getOutPoolTimeChangeEffectTime() >> seconds
        distributedLock.tryAcquire("visualConfig_delayTaskConf_1", "ok", seconds) >> resultOk
        delayDspCommonQConfig.isOutPoolTimeChangeCitySwitch(_) >> outPoolTimeChangeCitySwitch


        when:
        def result = testObj.compareAndSendMessage(newMap, originMap)

        then:
        result == expectedResult

        where:
        newMap            | originMap         | seconds | resultOk | outPoolTimeChangeCitySwitch || expectedResult
        null              | Maps.newHashMap() | 1       | false    | false                       || null
        Maps.newHashMap() | Maps.newHashMap() | 1       | true     | false                       || null
        buildConfigMap()  | buildConfigMap()  | 1       | true     | false                       || null
        buildConfigMap()  | buildConfigMap()  | 1       | true     | true                        || null
        buildConfigMap1() | buildConfigMap2() | 1       | true     | true                        || null
        buildConfigMap1() | Maps.newHashMap() | 1       | true     | true                        || null
        buildConfigMap3() | buildConfigMap3() | 1       | true     | true                        || null
        buildConfigMap4() | buildConfigMap3() | 1       | true     | true                        || null
        buildConfigMap3() | buildConfigMap()  | 1       | true     | true                        || null

    }


    @Unroll
    def "test buildDelayDspTaskValue"() {
        given:

        when:
        def result = testObj.buildDelayDspTaskValue(originMap, key, value)

        then:

        then:
        expectedResult == Objects.isNull(result)

        where:
        originMap         | key           | value                                       || expectedResult
        Maps.newHashMap() | "00:00-23:59" | new DelayDspTaskValue()                     || true
        buildConfigMap5() | "00:00-23:59" | new DelayDspTaskValue(type: "KM")           || true
        buildConfigMap5() | "00:00-23:59" | new DelayDspTaskValue(type: "DP", hour: 11) || true
        buildConfigMap5() | "00:00-23:59" | new DelayDspTaskValue(type: "DP", hour: 12) || false

    }


    @Unroll
    def "test get"() {
        given:
        testObj.map = setUpMap
        when:
        def result = testObj.get(cityId, date, type)

        then:
        Objects.equals(Objects.nonNull(result), expectedResult)

        where:
        setUpMap           | cityId | type | date                                      || expectedResult
        Maps.newHashMap()  | 111    | null | new Date()                                || false
        buildConfigMap3()  | 111    | null | new Date()                                || false
        buildConfigMap()   | 111    | null | DateUtil.parseDate("2024-10-01 10:00:00") || true
        buildConfigMap()   | 111    | ["KM"] | DateUtil.parseDate("2024-10-01 10:00:00") || false
        buildConfigMap()   | 111    | ["DP"] | DateUtil.parseDate("2024-10-01 10:00:00") || true
        buildConfigMap22() | 111    |  ["DP"]|  DateUtil.parseDate("2024-10-01 10:00:00") || false

    }


    def HashMap<String, DelayTaskConfigV2.Config> buildConfigMap1() {
        HashMap<String, DelayTaskConfigV2.Config> configMap = Maps.newHashMap();
        DelayTaskConfigV2.Config config = new DelayTaskConfigV2.Config();
        DelayDspTaskKeyV2 delayDspTaskKeyV2 = new DelayDspTaskKeyV2();
        delayDspTaskKeyV2.setCityId(111);
        config.setKey(delayDspTaskKeyV2);
        List<DelayDspTaskValue> values = Lists.newArrayList();
        DelayDspTaskValue value1 = new DelayDspTaskValue();
        value1.setBegin("00:00")
        value1.setEnd("23:59")
        value1.setDeadline(7)
        value1.setHour(12)
        value1.setTaskSeq(0)
        value1.setType("DP")
        values.add(value1);
        config.setValues(values);
        configMap.put("111", config);
        return configMap;
    }

    def HashMap<String, DelayTaskConfigV2.Config> buildConfigMap2() {
        HashMap<String, DelayTaskConfigV2.Config> configMap = Maps.newHashMap();
        DelayTaskConfigV2.Config config = new DelayTaskConfigV2.Config();
        DelayDspTaskKeyV2 delayDspTaskKeyV2 = new DelayDspTaskKeyV2();
        delayDspTaskKeyV2.setCityId(111);
        config.setKey(delayDspTaskKeyV2);
        List<DelayDspTaskValue> values = Lists.newArrayList();
        DelayDspTaskValue value1 = new DelayDspTaskValue();
        value1.setBegin("00:00")
        value1.setEnd("23:59")
        value1.setDeadline(7)
        value1.setHour(11)
        value1.setTaskSeq(0)
        value1.setType("DP")
        values.add(value1);
        config.setValues(values);
        configMap.put("111", config);
        return configMap;
    }

    def HashMap<String, DelayTaskConfigV2.Config> buildConfigMap3() {
        HashMap<String, DelayTaskConfigV2.Config> configMap = Maps.newHashMap();
        DelayTaskConfigV2.Config config = new DelayTaskConfigV2.Config();
        DelayDspTaskKeyV2 delayDspTaskKeyV2 = new DelayDspTaskKeyV2();
        delayDspTaskKeyV2.setCityId(111);
        config.setKey(delayDspTaskKeyV2);
        configMap.put("111", config);
        return configMap;
    }

    def HashMap<String, DelayTaskConfigV2.Config> buildConfigMap4() {
        HashMap<String, DelayTaskConfigV2.Config> configMap = Maps.newHashMap();
        DelayTaskConfigV2.Config config = new DelayTaskConfigV2.Config();
        DelayDspTaskKeyV2 delayDspTaskKeyV2 = new DelayDspTaskKeyV2();
        delayDspTaskKeyV2.setCityId(111);
        config.setKey(delayDspTaskKeyV2);
        configMap.put("111", null);
        return configMap;
    }

    def HashMap<String, DelayDspTaskValue> buildConfigMap5() {
        HashMap<String, DelayDspTaskValue> configMap = Maps.newHashMap();
        DelayDspTaskValue value1 = new DelayDspTaskValue();
        value1.setBegin("00:00")
        value1.setEnd("23:59")
        value1.setDeadline(7)
        value1.setHour(11)
        value1.setTaskSeq(0)
        value1.setType("DP")
        configMap.put("00:00-23:59", value1);
        return configMap;
    }

    def HashMap<String, DelayTaskConfigV2.Config> buildConfigMap() {
        HashMap<String, DelayTaskConfigV2.Config> configMap = Maps.newHashMap();
        DelayTaskConfigV2.Config config = new DelayTaskConfigV2.Config();
        DelayDspTaskKeyV2 delayDspTaskKeyV2 = new DelayDspTaskKeyV2();
        delayDspTaskKeyV2.setCityId(111);
        config.setKey(delayDspTaskKeyV2);
        List<DelayDspTaskValue> values = Lists.newArrayList();
        DelayDspTaskValue value1 = new DelayDspTaskValue();
        value1.setBegin("00:00")
        value1.setEnd("10:59")
        value1.setDeadline(7)
        value1.setHour(12)
        value1.setTaskSeq(0)
        value1.setType("DP")
        DelayDspTaskValue value2 = new DelayDspTaskValue();
        value2.setBegin("11:00")
        value2.setEnd("23:59")
        value2.setDeadline(7)
        value2.setHour(12)
        value2.setTaskSeq(0)
        value2.setType("DP")
        values.add(value1);
        values.add(value2);
        config.setValues(values);
        configMap.put("111", config);
        return configMap;
    }

    def HashMap<String, DelayTaskConfigV2.Config> buildConfigMap22() {
        HashMap<String, DelayTaskConfigV2.Config> configMap = Maps.newHashMap();
        DelayTaskConfigV2.Config config = new DelayTaskConfigV2.Config();
        DelayDspTaskKeyV2 delayDspTaskKeyV2 = new DelayDspTaskKeyV2();
        delayDspTaskKeyV2.setCityId(111);
        config.setKey(delayDspTaskKeyV2);
        List<DelayDspTaskValue> values = Lists.newArrayList();
        DelayDspTaskValue value2 = new DelayDspTaskValue();
        value2.setBegin("11:00")
        value2.setEnd("23:59")
        value2.setDeadline(7)
        value2.setHour(12)
        value2.setTaskSeq(0)
        value2.setType("DP")
        values.add(value2);
        config.setValues(values);
        configMap.put("111", config);
        return configMap;
    }
}
