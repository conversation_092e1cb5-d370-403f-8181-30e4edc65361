package com.ctrip.dcs.dsp.delay.validator.impl

import com.ctrip.dcs.dsp.delay.carconfig.DelayTaskConfig
import com.ctrip.dcs.dsp.delay.carconfig.value.DelayDspTaskValue
import com.ctrip.dcs.dsp.delay.gateway.TransportGateway
import com.ctrip.dcs.dsp.delay.model.SupplyOrder
import com.ctrip.dcs.dsp.delay.model.TransportGroupInfo
import com.ctrip.dcs.dsp.delay.validator.ValidatorCode
import com.ctrip.dcs.dsp.delay.validator.ValidatorDTO
import spock.lang.Specification

/**
 * <AUTHOR>
 */
class TransportGroupValidatorTest extends Specification {

    TransportGateway transportGateway = Mock(TransportGateway)

    DelayTaskConfig delayTaskConfig = Mock(DelayTaskConfig)

    TransportGroupValidator validator = new TransportGroupValidator(transportGateway: transportGateway, delayTaskConfig: delayTaskConfig)

    def "test validate"() {

        given:
        Date now = new Date()
        SupplyOrder supplyOrder = new SupplyOrder(cityId: 1, carTypeId: 117, sysExpectBookTime: now,shortDisOrder: 0)
        delayTaskConfig.get(1, 117, now,0) >> config
        transportGateway.queryTransportGroups(supplyOrder) >> transportGroups

        when:
        ValidatorDTO result = validator.validate(supplyOrder)

        then:
        result.getCode() == code

        where:
        config                            | transportGroups                                    || code
        null                              | null                                               || ValidatorCode.TASK_NOT_EXIST
        new DelayDspTaskValue(type: "KM") | null                                               || ValidatorCode.TRANSPORT_GROUP_EMPTY
        new DelayDspTaskValue(type: "KM") | [new TransportGroupInfo(transportGroupMode: 1003)] || ValidatorCode.TRANSPORT_GROUP_MODE_NOT_MATCH
        new DelayDspTaskValue(type: "KM") | [new TransportGroupInfo(transportGroupMode: 1001)] || ValidatorCode.OK
        new DelayDspTaskValue(type: "DP") | [new TransportGroupInfo(transportGroupMode: 1001)] || ValidatorCode.TRANSPORT_GROUP_MODE_NOT_MATCH
        new DelayDspTaskValue(type: "DP") | [new TransportGroupInfo(transportGroupMode: 1006)] || ValidatorCode.OK
    }
}
