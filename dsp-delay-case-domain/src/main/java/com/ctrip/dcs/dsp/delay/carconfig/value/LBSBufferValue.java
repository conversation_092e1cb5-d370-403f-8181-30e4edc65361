package com.ctrip.dcs.dsp.delay.carconfig.value;

import com.ctrip.dcs.dsp.delay.carconfig.Value;
import com.ctrip.dcs.dsp.delay.util.DateUtil;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class LBSBufferValue implements Value<Date> {

    public static final LBSBufferValue DEFAULT = new LBSBufferValue("00:00", "23:59", 0, 10);

    /**
     * 开始时间(相对00:00)
     */
    private String startTime;

    /**
     * 结束时间(相对00:00)
     */
    private String endTime;

    //0 固定值 1比例
    private int valueType;
    /**
     * 值   分钟数或者比例（比例的基数是行程的lbs时长）
     */
    private double value;

    public LBSBufferValue() {
    }

    public LBSBufferValue(String startTime, String endTime, int valueType, double value) {
        this.startTime = startTime;
        this.endTime = endTime;
        this.valueType = valueType;
        this.value = value;
    }

    @Override
    public boolean match(Date datetime) {
        String bookTimeStr = DateUtil.formatDate(datetime, DateUtil.HOUR_MIN_FMT);
        int compareToStartTimeVal = bookTimeStr.compareTo(startTime);
        int compareToEndTimeVal = bookTimeStr.compareTo(endTime);
        return startTime.compareTo(endTime) < 0
                ? compareToStartTimeVal >= 0 && compareToEndTimeVal <= 0
                :( compareToStartTimeVal >= 0 || compareToEndTimeVal <= 0);
    }
}
