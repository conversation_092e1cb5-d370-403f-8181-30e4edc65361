package com.ctrip.dcs.dsp.delay.interfaces.executor;

import com.ctrip.dcs.dsp.delay.carconfig.DelayTaskConfig;
import com.ctrip.dcs.dsp.delay.model.DelayDspConfigInfoDTO;
import com.ctrip.dcs.dsp.delay.model.DelayDspConfigResInfoDTO;
import com.ctrip.dcs.dsp.delay.service.DelayTaskService;
import com.ctrip.dcs.dsp.delay.service.interfaces.message.QueryDelayDspConfigInfoRequestType;
import com.ctrip.dcs.dsp.delay.service.interfaces.message.QueryDelayDspConfigResInfoResponseType;
import com.ctrip.dcs.dsp.delay.util.DateUtil;
import com.ctrip.dcs.dsp.transport.inventory.service.api.dto.DelayDspConfigInfo;
import com.ctrip.dcs.dsp.transport.inventory.service.api.dto.DelayDspConfigResInfo;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class QueryDelayDspConfigExecutor extends AbstractRpcExecutor<QueryDelayDspConfigInfoRequestType, QueryDelayDspConfigResInfoResponseType> {

    @Autowired
    private DelayTaskService delayTaskService;

    @Override
    public QueryDelayDspConfigResInfoResponseType execute(QueryDelayDspConfigInfoRequestType requestType) {
        QueryDelayDspConfigResInfoResponseType responseType = new QueryDelayDspConfigResInfoResponseType();
        if(CollectionUtils.isNotEmpty(requestType.getDelayDspConfigList())){
            List<DelayDspConfigResInfoDTO> delayDspConfigResInfoDTOS = delayTaskService.queryDelayDspConfigList(getDelayDspConfigInfoDTOS(requestType));
            List<DelayDspConfigResInfo> data = getData(delayDspConfigResInfoDTOS);
            responseType.setData(data);
        }
        return ServiceResponseUtils.success(responseType);
    }

    private List<DelayDspConfigResInfo> getData(List<DelayDspConfigResInfoDTO> delayDspConfigResInfoDTOS) {
        List<DelayDspConfigResInfo> data = Lists.newArrayList();
        if(CollectionUtils.isNotEmpty(delayDspConfigResInfoDTOS)){
            for(DelayDspConfigResInfoDTO resDTO:delayDspConfigResInfoDTOS){
                DelayDspConfigResInfo delayDspConfigResInfo = new DelayDspConfigResInfo();
                delayDspConfigResInfo.setCityId(resDTO.getCityId());
                delayDspConfigResInfo.setCarTypeId(resDTO.getCarTypeId());
                delayDspConfigResInfo.setSysExpectBookTime(resDTO.getSysExpectBookTime());
                delayDspConfigResInfo.setTaskDeadTime(resDTO.getTaskDeadTime());
                delayDspConfigResInfo.setTaskEndTime(resDTO.getTaskEndTime());
                data.add(delayDspConfigResInfo);
            }
        }
        return data;
    }

    private List<DelayDspConfigInfoDTO> getDelayDspConfigInfoDTOS(QueryDelayDspConfigInfoRequestType requestType) {
        List<DelayDspConfigInfoDTO> delayDspConfigInfoList = Lists.newArrayList();
        for (DelayDspConfigInfo delayInfo : requestType.getDelayDspConfigList()) {
            DelayDspConfigInfoDTO delayDspConfigInfoDTO = new DelayDspConfigInfoDTO();
            delayDspConfigInfoDTO.setCityId(delayInfo.getCityId());
            delayDspConfigInfoDTO.setCarTypeId(delayInfo.getCarTypeId());
            delayDspConfigInfoDTO.setSysExpectBookTime(DateUtil.parseDate(delayInfo.getSysExpectBookTime()));
            if (CollectionUtils.isEmpty(delayInfo.getTaskTypes())) {
                // 默认为DP
                delayDspConfigInfoDTO.setTaskTypes(Lists.newArrayList(DelayTaskConfig.DELAY_TASK_CONFIG_TYPE_DP));
            } else {
                delayDspConfigInfoDTO.setTaskTypes(delayInfo.getTaskTypes());
            }
            delayDspConfigInfoList.add(delayDspConfigInfoDTO);
        }
        return delayDspConfigInfoList;
    }
}
