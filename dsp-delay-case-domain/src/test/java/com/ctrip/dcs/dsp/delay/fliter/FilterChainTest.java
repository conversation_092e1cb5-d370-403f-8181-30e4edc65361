package com.ctrip.dcs.dsp.delay.fliter;

import com.ctrip.dcs.dsp.delay.filter.Filter;
import com.ctrip.dcs.dsp.delay.filter.FilterChain;
import com.ctrip.dcs.dsp.delay.filter.impl.AddressFilter;
import com.ctrip.dcs.dsp.delay.model.DelayDspTask;
import com.ctrip.dcs.dsp.delay.model.Driver;
import com.ctrip.dcs.dsp.delay.qconfig.DelayDspCommonQConfig;
import com.ctrip.dcs.dsp.delay.util.SpringApplicationContextUtil;
import com.google.common.collect.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.List;

/**
 * <AUTHOR>
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest(value = {SpringApplicationContextUtil.class})
public class FilterChainTest {

    @InjectMocks
    private FilterChain filterChain;

    @Mock
    private DelayDspCommonQConfig delayDspCommonQConfig;

    @Test
    public void test() {
        Driver driver = new Driver();
        driver.setAddressLatitude(1.1);
        driver.setAddressLongitude(1.1);

        Driver driver1 = new Driver();

        DelayDspTask task = new DelayDspTask();
        PowerMockito.when(delayDspCommonQConfig.getDriverFilterList()).thenReturn(Lists.newArrayList("addressFilter"));
        PowerMockito.mockStatic(SpringApplicationContextUtil.class);
        PowerMockito.when(SpringApplicationContextUtil.getBean("addressFilter", Filter.class)).thenReturn(new AddressFilter());
        List<Driver> list = filterChain.filter(Lists.newArrayList(driver, driver1), task);
        Assert.assertNotNull(list);
    }
}
