package com.ctrip.dcs.dsp.delay.interfaces.consumer;

import com.ctrip.dcs.dsp.delay.consts.CommonConstant;
import com.ctrip.dcs.dsp.delay.infrastructure.mapper.DelayDspTaskRecordMapper;
import com.ctrip.dcs.dsp.delay.model.DelayDspTaskRecord;
import com.ctrip.dcs.dsp.delay.service.PreOutPoolService;
import com.ctrip.dcs.dsp.delay.util.JsonUtil;
import com.ctrip.dcs.dsp.delay.util.MetricsUtil;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.NeedRetryException;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;

import java.util.Objects;

/**
 * 延后派任务匹配完成消费者
 */
@Component
public class DelayPreOutPoolCheckConsumer {

    private static final Logger logger = LoggerFactory.getLogger(DelayPreOutPoolCheckConsumer.class);

    @Autowired
    private PreOutPoolService preOutPoolService;
    @Autowired
    private DelayDspTaskRecordMapper delayDspTaskRecordMapper;


    @QmqConsumer(prefix = CommonConstant.DELAY_PRE_OUT_POOL_CHECK, consumerGroup = CommonConstant.APP_ID)
    public void onMessage(Message message) {
        try {
            String userOrderId = message.getStringProperty("userOrderId");
            Integer pressedDispatchSource = message.getIntProperty("pressedDispatchSource");
            String customerServiceUid = message.getStringProperty("customerServiceUid");
            String dspOrderId = message.getStringProperty("dspOrderId");
            String orderSource = message.getStringProperty("orderSource");
            DelayDspTaskRecord delayDspTaskRecord = delayDspTaskRecordMapper.queryByDspOrderId(dspOrderId);
            logger.info("DelayPreOutPoolCheckConsumer_onMessage", JsonUtil.toJson(delayDspTaskRecord));
            if(Objects.nonNull(delayDspTaskRecord)){
                MetricsUtil.recordValueWithTag("pre_out_pool", 1, "outRes", "0");
                return;
            }

            // 改派
            int res = preOutPoolService.redispatchForPreOutPool(dspOrderId, userOrderId, pressedDispatchSource, customerServiceUid, orderSource);
            MetricsUtil.recordValueWithTag("pre_out_pool", 1, "outRes", String.valueOf(res));
        } catch (Exception e) {
            logger.error("DelayPreOutPoolCheckConsumer_error", e);
            throw new NeedRetryException("DelayPreOutPoolCheckConsumer");
        }
    }
}
