package com.ctrip.dcs.dsp.delay.model;

import com.ctrip.dcs.dsp.delay.enums.YesOrNo;
import com.google.common.collect.Lists;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class DispatchResult {

    List<DispatchResultDetail> details;

    public DispatchResult() {
        this.details = Lists.newArrayList();
    }

    public DispatchResult(List<DelayDspOrder> orders) {
        this.details = orders.stream().map(DispatchResultDetail::new).collect(Collectors.toList());
    }

    /**
     * 匹配到司机的订单
     * 需要接单
     * @return
     */
    public List<DispatchResultDetail> getMatchDetails() {
        return this.details.stream()
                .filter(d -> Objects.nonNull(d.getOrder()))
                .filter(d -> StringUtils.isNotBlank(d.getOrder().getOrderId()))
                .filter(d -> Objects.equals(d.getOrder().getIsDelay(), YesOrNo.YES.getCode()))
                .filter(d -> Objects.equals(d.getOrder().getIsCancel(), YesOrNo.NO.getCode()))
                .filter(d -> Objects.nonNull(d.getDriver()))
                .filter(d -> Objects.equals(d.getMatchSuccess(), YesOrNo.YES.getCode()))
                .collect(Collectors.toList());
    }

    /**
     * 未匹配到司机的订单
     * 需要改派
     * @return
     */
    public List<DispatchResultDetail> getUnMatchDetails() {
        return this.details.stream()
                .filter(d -> Objects.nonNull(d.getOrder()))
                .filter(d -> StringUtils.isNotBlank(d.getOrder().getOrderId()))
                .filter(d -> Objects.equals(d.getOrder().getIsDelay(), YesOrNo.YES.getCode()))
                .filter(d -> Objects.equals(d.getOrder().getIsCancel(), YesOrNo.NO.getCode()))
                .filter(d -> Objects.isNull(d.getDriver()) || !Objects.equals(d.getMatchSuccess(), YesOrNo.YES.getCode()))
                .collect(Collectors.toList());
    }
}
