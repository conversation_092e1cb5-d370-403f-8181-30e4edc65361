package com.ctrip.dcs.dsp.delay.qconfig.value;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version V1.0
 * @date 2023/3/21 11:00
 */
@Getter
public class OrderDisLimitValueVO implements CarConfigValueVO {

    private Integer coopMode;

    private Integer highPriorityDriver;

    private Integer limitDisLower;

    private Integer limitDisUpper;

    private Integer takenLimit;

    private Integer inUse;

    @JsonCreator
    public OrderDisLimitValueVO(@JsonProperty("coopMode") Integer coopMode, @JsonProperty("highPriorityDriver") Integer highPriorityDriver, @JsonProperty("limitDisLower") Integer limitDisLower, @JsonProperty("limitDisUpper") Integer limitDisUpper, @JsonProperty("takenLimit") Integer takenLimit, @JsonProperty("inUse") Integer inUse) {
        this.coopMode = coopMode;
        this.highPriorityDriver = highPriorityDriver;
        this.limitDisLower = limitDisLower;
        this.limitDisUpper = limitDisUpper;
        this.takenLimit = takenLimit;
        this.inUse = inUse;
    }
    public OrderDisLimitValueVO(){}
}
