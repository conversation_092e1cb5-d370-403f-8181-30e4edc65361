package com.ctrip.dcs.dsp.delay.infrastructure.soa;

import com.ctrip.dcs.dsp.driver.level.api.*;
import com.ctrip.igt.framework.soa.client.ServiceClient;
import com.ctrip.igt.framework.soa.client.ServiceClientConfigOptions;

/**
 * <AUTHOR>
 * @version V1.0
 * @date 2022/12/8 11:20
 */
@ServiceClient(value = DcsDriverLevelServiceClient.class)
public interface RhbDriverPointProxy {


    /**
     * 查询司机分
     * @param qry
     * @return
     */
    @ServiceClientConfigOptions(connectTimeout = 3000, socketTimeout = 2000, timeout = 2000)
    DriverPointDetailResp queryDspDriverPointDetails(DspDriverPointDetailQry qry);
}
