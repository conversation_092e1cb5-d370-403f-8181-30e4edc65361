package com.ctrip.dcs.dsp.delay.infrastructure.repository;

import com.ctrip.dcs.dsp.delay.infrastructure.mapper.ck.ClickHouseAdapter;
import com.ctrip.dcs.dsp.delay.model.InsertPoolReasonRecord;
import com.ctrip.dcs.dsp.delay.repository.InsertPoolReasonRecordRepository;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2024/8/15 15:17
 */
@Repository
public class InsertPoolReasonRecordRepositoryImpl implements InsertPoolReasonRecordRepository {
    private static final String INSERT_DSP_POOL_REASON_RECORD = "dcs-dsp-delay-insert-pool-reason-record";
    @Resource
    ClickHouseAdapter clickHouseAdapter;
    
    @Override
    public void record(InsertPoolReasonRecord record) {
        clickHouseAdapter.save(INSERT_DSP_POOL_REASON_RECORD, record.toMap());
    }
    
}
