package com.ctrip.dcs.dsp.delay.service;

import com.ctrip.dcs.dsp.delay.context.DelayDspContext;
import com.ctrip.dcs.dsp.delay.core.KM;
import com.ctrip.dcs.dsp.delay.factory.KMFactory;
import com.ctrip.dcs.dsp.delay.model.*;
import com.ctrip.dcs.dsp.delay.service.impl.KMDispatcherTaskServiceImpl;
import com.ctrip.dcs.dsp.delay.service.impl.SDDispatcherTaskServiceImpl;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@RunWith(PowerMockRunner.class)
public class SDDispatcherTaskServiceImplTest {

    @InjectMocks
    private SDDispatcherTaskServiceImpl dispatcherTaskService;

    @Mock
    private QueryAvailableDriversService queryAvailableDriversService;


    @Mock
    private DelayDspContext context;

    @Mock
    private DelayDspTask delayDspTask;

    @Mock
    private DelayDspOrder delayDspOrder;

    @Mock
    private Driver driver;

    @Mock
    private AvailableDriver availableDriver;

    @Mock
    private AvailableDriverDetail detail;


    @Test
    public void test1() {
        PowerMockito.when(context.getDelayDspTask()).thenReturn(delayDspTask);
        PowerMockito.when(context.getOrders()).thenReturn(Lists.newArrayList(delayDspOrder));
        PowerMockito.when(queryAvailableDriversService.query(Mockito.any())).thenReturn(availableDriver);
        DispatchResult result = dispatcherTaskService.dispatch(context);
        Assert.assertTrue(result !=null);
    }

    @Test
    public void test2() {
        PowerMockito.when(context.getDelayDspTask()).thenReturn(delayDspTask);
        PowerMockito.when(context.getOrders()).thenReturn(Lists.newArrayList(delayDspOrder));
        PowerMockito.when(queryAvailableDriversService.query(Mockito.any())).thenReturn(availableDriver);
        PowerMockito.when(availableDriver.getDetails()).thenReturn(Lists.newArrayList(detail));
        PowerMockito.when(detail.getDriver()).thenReturn(driver);
        PowerMockito.when(detail.getOrder()).thenReturn(delayDspOrder);
        PowerMockito.when(detail.getValue()).thenReturn("883.9");
        PowerMockito.when(availableDriver.orders()).thenReturn(ImmutableMap.of("1", delayDspOrder));
        PowerMockito.when(availableDriver.drivers()).thenReturn(ImmutableMap.of(1L, driver));
        PowerMockito.when(delayDspOrder.getOrderId()).thenReturn("1");
        PowerMockito.when(driver.getDriverId()).thenReturn("1");
        DispatchResult result = dispatcherTaskService.dispatch(context);
        Assert.assertTrue(result !=null);
    }

    @Test
    public void test3() {
        PowerMockito.when(availableDriver.isAvailable(Mockito.any(), Mockito.any())).thenReturn(true);
        Map<String, DelayDspOrder> orderMap = ImmutableMap.of("1", delayDspOrder, "2", delayDspOrder);
        Map<Long, Driver> driverMap = ImmutableMap.of(1L, driver);
        List<DispatchResultDetail> result = dispatcherTaskService.match(orderMap, driverMap, availableDriver);
        Assert.assertEquals(result.size(), orderMap.size());
    }
}
