package com.ctrip.dcs.dsp.delay.infrastructure.getway

import com.ctrip.dcs.dsp.delay.infrastructure.geteway.DriverGatewayImpl
import com.ctrip.dcs.dsp.delay.infrastructure.geteway.TransportGatewayImpl
import com.ctrip.dcs.dsp.delay.infrastructure.http.DriverScoreServiceProxy
import com.ctrip.dcs.dsp.delay.infrastructure.soa.TmsTransportServiceProxy
import com.ctrip.dcs.dsp.delay.model.Driver
import com.ctrip.dcs.dsp.delay.model.DriverScore
import com.ctrip.dcs.dsp.delay.model.TransportSku
import com.ctrip.dcs.dsp.delay.qconfig.DelayDspCommonQConfig
import com.ctrip.dcs.tms.transport.api.model.DriverInfo
import com.ctrip.dcs.tms.transport.api.model.DriverInfoSOAResponseType
import com.ctrip.dcs.tms.transport.api.model.DrvLeaveDetailSOAType
import com.ctrip.dcs.tms.transport.api.model.QueryDrvIdByTransportGroupsResponseType
import com.ctrip.dcs.tms.transport.api.model.QueryDrvLeaveDetailForDspSOAResponseType
import com.ctrip.igt.ResponseResult
import com.ctrip.igt.framework.common.concurrent.threadpool.ThreadPoolBuilder
import com.google.common.collect.Lists
import spock.lang.Specification
import spock.lang.Unroll

import java.util.concurrent.Executors

/**
 * <AUTHOR>
 */
class DriverGatewayImplSpec extends Specification {


    def driverThreadPool = Executors.newSingleThreadExecutor()
    def tmsTransportServiceProxy = Mock(TmsTransportServiceProxy)
    def transportGateway = Mock(TransportGatewayImpl)
    def driverScoreServiceProxy = Mock(DriverScoreServiceProxy)
    def delayDspCommonQConfig = Mock(DelayDspCommonQConfig)


    def gateway = new DriverGatewayImpl("tmsTransportServiceProxy": tmsTransportServiceProxy, "transportGateway": transportGateway, "driverScoreServiceProxy": driverScoreServiceProxy, driverThreadPool : driverThreadPool, delayDspCommonQConfig:delayDspCommonQConfig)

    @Unroll
    def "query by driver id"() {

        given: "Mock数据"
        tmsTransportServiceProxy.queryDriver(_) >> dto
        when: "执行校验方法"
        def res = gateway.query(driverId as String)

        then: "验证校验结果"
        res.driverId == driver.driverId

        where:
        driverId | dto                  || driver
        "1"      | getDriverInfoDTO(1L) || new Driver("driverId": "1")
    }

    @Unroll
    def "query by city and car"() {

        given: "Mock数据"
        QueryDrvIdByTransportGroupsResponseType responseType = new QueryDrvIdByTransportGroupsResponseType()
        responseType.setDrvIdList(Lists.newArrayList(1L))
        transportGateway.queryTransportSku(cityId as Integer, carTypeId as Integer) >> ts
        tmsTransportServiceProxy.queryDrvIdByTransportGroups(_) >> responseType
        tmsTransportServiceProxy.queryDriver(_) >> dto
        when: "执行校验方法"
        def list = gateway.query(cityId as Integer, carTypeId as Integer,null)

        then: "验证校验结果"
        list.size() == size

        where:
        cityId | carTypeId | ts       | dto            || size
//        1      | 117       | null     | getDriverInfoDTO(1L) || 0
        1      | 117       | getSku() | getDriverInfoDTO(1L) || 1
    }

    @Unroll
    def "query driver leave"() {

        given: "Mock数据"
        tmsTransportServiceProxy.queryDrvLeaveDetailForDsp(_) >> dto
        when: "执行校验方法"
        def list = gateway.queryLeave(driverIds as List<String>)

        then: "验证校验结果"
        list.size() == size

        where:
        driverIds                         | dto      || size
        Lists.newArrayList("1") | getLeaveDTO() || 1
    }

    @Unroll
    def "query driver score"() {

        given: "Mock数据"
        driverScoreServiceProxy.queryDriverScore(_) >> dto
        when: "执行校验方法"
        def list = gateway.queryDriverScore(driverIds as Set<String>)

        then: "验证校验结果"
        list.size() == size

        where:
        driverIds                         | dto      || size
        Lists.newArrayList("1") | getScoreDTO() || 1
    }

    def getDriverInfoDTO(driverId) {
        def driverList = Lists.newArrayList(new DriverInfo("driverId": driverId, "qunarCityCode" : "beijing_city"))
        return new DriverInfoSOAResponseType("driverList": driverList, responseResult: new ResponseResult(success: true))
    }

    def getSku() {
        return Lists.newArrayList(new TransportSku("transportGroupId" : 1))
    }

    def getLeaveDTO() {
        def data = Lists.newArrayList(new DrvLeaveDetailSOAType("drvId": 1L))
        return new QueryDrvLeaveDetailForDspSOAResponseType("data": data, responseResult: new ResponseResult(success: true))
    }

    def getScoreDTO() {
        return Lists.newArrayList(new DriverScore("drivId" : "1"))
    }
}
