package com.ctrip.dcs.dsp.delay.infrastructure.gateway;

import com.ctrip.dcs.dsp.delay.infrastructure.converter.DriverOrderConverter;
import com.ctrip.dcs.dsp.delay.infrastructure.dto.DriverUdlVO;
import com.ctrip.dcs.dsp.delay.infrastructure.geteway.DriverGatewayImpl;
import com.ctrip.dcs.dsp.delay.infrastructure.geteway.SelfDriverOrderGatewayImpl;
import com.ctrip.dcs.dsp.delay.infrastructure.soa.SelfDriverOrderServiceProxy;
import com.ctrip.dcs.dsp.delay.infrastructure.soa.SelfOrderQueryServiceProxy;
import com.ctrip.dcs.dsp.delay.infrastructure.soa.TmsTransportServiceProxy;
import com.ctrip.dcs.dsp.delay.infrastructure.util.TmsTransportServiceClientUtil;
import com.ctrip.dcs.dsp.delay.model.DelayDspOrder;
import com.ctrip.dcs.dsp.delay.model.Driver;
import com.ctrip.dcs.dsp.delay.qconfig.DelayDspCommonQConfig;
import com.ctrip.dcs.oms.driver.order.domain.api.drvOrder.CreateDriverOrderResponseType;
import com.ctrip.dcs.self.order.query.api.QueryOrderDetailResponseType;
import com.ctrip.dcs.self.order.query.dto.BaseDetail;
import com.ctrip.dcs.self.order.query.dto.OrderDetail;
import com.ctrip.dcs.tms.transport.api.model.DriverInfo;
import com.ctrip.dcs.tms.transport.api.model.DriverInfoSOAResponseType;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.beans.factory.annotation.Autowired;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@RunWith(PowerMockRunner.class)
public class SelfDriverOrderGatewayImplTest {

    @InjectMocks
    private SelfDriverOrderGatewayImpl driverOrderGateway;

    @Mock
    private SelfDriverOrderServiceProxy driverOrderServiceProxy;

    @Mock
    private DriverOrderConverter driverOrderConverter;

    @Mock
    private SelfOrderQueryServiceProxy orderQueryServiceProxy;

    @Mock
    private TmsTransportServiceProxy tmsTransportServiceProxy;

    @Mock
    private DelayDspOrder order;

    @Mock
    private Driver driver;

    @Mock
    private QueryOrderDetailResponseType queryOrderDetailResponseType;

    @Mock
    private DriverInfoSOAResponseType driverInfoSOAResponseType;

    @Mock
    private CreateDriverOrderResponseType createDriverOrderResponseType;

    @Mock
    private OrderDetail orderDetail;

    @Mock
    private BaseDetail baseDetail;

    @Mock
    private DriverInfo driverInfo;
    
    
    @Mock
    DriverGatewayImpl driverGateway;
    @Mock
    DelayDspCommonQConfig delayDspCommonQConfig;
    @Mock
    TmsTransportServiceClientUtil tmsTransportServiceClientUtil;
    

    @Test
    public void testCreate() {
        String id = driverOrderGateway.create(order, driver);
        Assert.assertEquals(id, "");
    }

    @Test
    public void testCreate1() {
        PowerMockito.when(orderQueryServiceProxy.queryOrderDetail(Mockito.any())).thenReturn(queryOrderDetailResponseType);
        PowerMockito.when(queryOrderDetailResponseType.getOrderDetail()).thenReturn(orderDetail);
        PowerMockito.when(orderDetail.getBaseDetail()).thenReturn(baseDetail);
        PowerMockito.when(baseDetail.getOrderStatus()).thenReturn(210);
        PowerMockito.when(tmsTransportServiceProxy.queryDriver(Mockito.any())).thenReturn(driverInfoSOAResponseType);
        PowerMockito.when(driverInfoSOAResponseType.getDriverList()).thenReturn(Lists.newArrayList(driverInfo));
        PowerMockito.when(driverOrderServiceProxy.createDriverOrder(Mockito.any())).thenReturn(createDriverOrderResponseType);
        PowerMockito.when(createDriverOrderResponseType.getDrvOrderId()).thenReturn("1");
        String id = driverOrderGateway.create(order, driver);
        Assert.assertEquals(id, "1");
    }
    
    
    @Test
    public void testQueryDriver() {
        PowerMockito.when(delayDspCommonQConfig.isSelfCloudSwitch()).thenReturn(false);
        DriverInfoSOAResponseType responseType = new DriverInfoSOAResponseType();
        responseType.setDriverList(Lists.newArrayList(new DriverInfo()));
        PowerMockito.when(tmsTransportServiceProxy.queryDriver(Mockito.any())).thenReturn(responseType);
        DriverInfo driverInfo = driverOrderGateway.queryDriver("123");
        Assert.assertTrue(Objects.nonNull(driverInfo));
        
        
        PowerMockito.when(delayDspCommonQConfig.isSelfCloudSwitch()).thenReturn(true);
        Map<Long, DriverUdlVO> udlMap = Maps.newHashMap();
        PowerMockito.when(driverGateway.getDrvUdlMap(Mockito.any())).thenReturn(udlMap);
        driverInfo = driverOrderGateway.queryDriver("123");
        Assert.assertTrue(Objects.nonNull(driverInfo));
    }
}
