package com.ctrip.dcs.dsp.delay.infrastructure.mapper.ck;

import com.ctrip.dcs.dsp.delay.util.JsonUtil;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.dianping.cat.Cat;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024/8/15 15:19
 */
@Component
public class ClickHouseAdapter {
    
    private static final Logger logger = LoggerFactory.getLogger(ClickHouseAdapter.class);
    
    public void save(String scenario, Map<String, String> tags) {
        logger.info(scenario, JsonUtil.toJson(tags));
        Cat.logTags(scenario, tags, null);
    }
}
