package com.ctrip.dcs.dsp.delay.infrastructure.soa;

import com.ctrip.dcs.oms.driver.order.domain.api.SelfDriverOrderDomainServiceClient;
import com.ctrip.dcs.oms.driver.order.domain.api.drvOrder.CreateDriverOrderRequestType;
import com.ctrip.dcs.oms.driver.order.domain.api.drvOrder.CreateDriverOrderResponseType;
import com.ctrip.igt.framework.soa.client.ServiceClient;

/**
 * <AUTHOR>
 */
@ServiceClient(value = SelfDriverOrderDomainServiceClient.class, format = "json")
public interface SelfDriverOrderServiceProxy {


    CreateDriverOrderResponseType createDriverOrder(CreateDriverOrderRequestType request);
}
