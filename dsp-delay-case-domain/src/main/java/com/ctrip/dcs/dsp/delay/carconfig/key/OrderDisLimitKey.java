package com.ctrip.dcs.dsp.delay.carconfig.key;

import com.ctrip.dcs.dsp.delay.carconfig.Key;
import com.ctrip.dcs.dsp.delay.consts.CommonConstant;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@AllArgsConstructor
public class OrderDisLimitKey implements Key {

    private String cityCode;

    private Integer carTypeId;

    @Override
    public String toKey() {
        return cityCode + CommonConstant.PLACEHOLDER + carTypeId;
    }

}
