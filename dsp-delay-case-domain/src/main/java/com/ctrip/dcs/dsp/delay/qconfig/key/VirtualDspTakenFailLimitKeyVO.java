package com.ctrip.dcs.dsp.delay.qconfig.key;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
public class VirtualDspTakenFailLimitKeyVO implements CarConfigKeyVO {

    private Integer cityId;

    private Integer carTypeId;

    @JsonCreator
    public VirtualDspTakenFailLimitKeyVO(@JsonProperty("cityId") Integer cityId, @JsonProperty("carTypeId") Integer carTypeId) {
        this.cityId = cityId;
        this.carTypeId = carTypeId;
    }

    @Override
    public String toKey() {
        return cityId + PLACEHOLDER + carTypeId;
    }
}
