package com.ctrip.dcs.dsp.delay.infrastructure.http;

import com.ctrip.dcs.dsp.delay.infrastructure.soa.RhbDriverPointProxy;
import com.ctrip.dcs.dsp.delay.model.DriverScore;
import com.ctrip.dcs.dsp.delay.util.MetricsUtil;
import com.ctrip.dcs.dsp.driver.level.api.DriverPointDetailResp;
import com.ctrip.dcs.dsp.driver.level.api.DspDriverPointDetail;
import com.ctrip.dcs.dsp.driver.level.api.DspDriverPointDetailQry;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Component
public class DriverScoreServiceProxy {

    private static final Logger logger = LoggerFactory.getLogger(DriverScoreServiceProxy.class);

    @Autowired
    RhbDriverPointProxy proxy;


    public List<DriverScore> queryDriverScore(Set<String> driverIds) {
        if (CollectionUtils.isEmpty(driverIds)) {
            return Lists.newArrayList();
        }
        try {
            DspDriverPointDetailQry detailQry = new DspDriverPointDetailQry();
            detailQry.setDrvIdList(Joiner.on(",").join(driverIds));
            DriverPointDetailResp resp = proxy.queryDspDriverPointDetails(detailQry);
            if(resp == null || CollectionUtils.isEmpty(resp.getPointDetails())){
                return Lists.newArrayList();
            }
            List<DriverScore> driverScores = new ArrayList<>();
            List<DspDriverPointDetail> pointDetails = resp.getPointDetails();
            for (DspDriverPointDetail pointDetail : pointDetails) {
                DriverScore driverScore = new DriverScore();
                driverScore.setDriverBaseScore(pointDetail.getDriverBaseScore().doubleValue());
                driverScore.setCarBaseScore(pointDetail.getCarBaseScore().doubleValue());
                driverScore.setActiveScore(pointDetail.getActiveScore().doubleValue());
                driverScore.setDrivId(pointDetail.getDrvId());
                driverScore.setOrderServiceScore(pointDetail.getOrderServiceScore().doubleValue());
                driverScore.setTotalScore(pointDetail.getTotalScore().doubleValue());
                driverScore.setStudyScore(0d);
                driverScore.setExtraBonusScore(0D);
                driverScores.add(driverScore);
            }
            return driverScores;
        } catch (Exception e) {
            MetricsUtil.recordValue("query.driver.score.http.error", 1);
            logger.error(e);
        }
        return Lists.newArrayList();
    }
}
