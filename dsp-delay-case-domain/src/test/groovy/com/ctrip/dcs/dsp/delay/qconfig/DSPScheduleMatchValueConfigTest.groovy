package com.ctrip.dcs.dsp.delay.qconfig

import com.ctrip.dcs.dsp.delay.qconfig.key.DSPScheduleMatchKeyVO
import com.ctrip.dcs.dsp.delay.qconfig.key.HeadTailLimitKeyVO
import com.ctrip.dcs.dsp.delay.qconfig.value.DSPScheduleMatchValueVO
import com.ctrip.dcs.dsp.delay.qconfig.value.HeadTailLimitValueVO
import com.google.common.collect.Lists
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 */
class DSPScheduleMatchValueConfigTest extends Specification {

    def map = new HashMap();

    def config = new DSPScheduleMatchValueConfig(context: map)

    @Unroll
    def "test"() {
        given: "Mock数据"
        DSPScheduleMatchKeyVO keyVO1 = new DSPScheduleMatchKeyVO("1", 117)
        DSPScheduleMatchKeyVO keyVO2 = new DSPScheduleMatchKeyVO("1", 0)
        DSPScheduleMatchKeyVO keyVO3 = new DSPScheduleMatchKeyVO("0", 117)
        DSPScheduleMatchKeyVO keyVO4 = new DSPScheduleMatchKeyVO("0", 0)

        DSPScheduleMatchValueVO valueVO1 = new DSPScheduleMatchValueVO(24, 60, 300, "1", 5)
        DSPScheduleMatchValueVO valueVO2 = new DSPScheduleMatchValueVO(23, 59, 301, "1", 6)
        DSPScheduleMatchValueVO valueVO3 = new DSPScheduleMatchValueVO(22, 58, 302, "1", 7)
        DSPScheduleMatchValueVO valueVO4 = new DSPScheduleMatchValueVO(21, 57, 303, "1", 8)

        map.put("1#117", new BaseCarConfig.Config(keyVO1, Lists.newArrayList(valueVO1)))
        map.put("1#0", new BaseCarConfig.Config(keyVO2, Lists.newArrayList(valueVO2)))
        map.put("0#117", new BaseCarConfig.Config(keyVO3, Lists.newArrayList(valueVO3)))
        map.put("0#0", new BaseCarConfig.Config(keyVO4, Lists.newArrayList(valueVO4)))

        when: "执行校验方法"
        def result = config.getValue(cityId, carTypeId)

        then: "验证校验结果"
        res == Objects.isNull(result)

        where:
        cityId | carTypeId || res
        null   | 1         || true
        "null" | null      || true
        "1"    | 117       || false
    }

    @Unroll
    def "test2"() {
        given: "Mock数据"
        DSPScheduleMatchKeyVO keyVO1 = new DSPScheduleMatchKeyVO("1", 117)
        DSPScheduleMatchKeyVO keyVO2 = new DSPScheduleMatchKeyVO("1", 0)
        DSPScheduleMatchKeyVO keyVO3 = new DSPScheduleMatchKeyVO("0", 117)
        DSPScheduleMatchKeyVO keyVO4 = new DSPScheduleMatchKeyVO("0", 0)

        DSPScheduleMatchValueVO valueVO1 = new DSPScheduleMatchValueVO(24, 60, 300, "1", 5)
        DSPScheduleMatchValueVO valueVO2 = new DSPScheduleMatchValueVO(23, 59, 301, "1", 6)
        DSPScheduleMatchValueVO valueVO3 = new DSPScheduleMatchValueVO(22, 58, 302, "1", 7)
        DSPScheduleMatchValueVO valueVO4 = new DSPScheduleMatchValueVO(21, 57, 303, "1", 8)

        map.put("1#0", new BaseCarConfig.Config(keyVO2, Lists.newArrayList(valueVO2)))
        map.put("0#117", new BaseCarConfig.Config(keyVO3, Lists.newArrayList(valueVO3)))
        map.put("0#0", new BaseCarConfig.Config(keyVO4, Lists.newArrayList(valueVO4)))

        when: "执行校验方法"
        def result = config.getValue(cityId, carTypeId)

        then: "验证校验结果"
        res == Objects.isNull(result)

        where:
        cityId | carTypeId || res
        "1"    | 117       || false
    }

    @Unroll
    def "test3"() {
        given: "Mock数据"
        DSPScheduleMatchKeyVO keyVO1 = new DSPScheduleMatchKeyVO("1", 117)
        DSPScheduleMatchKeyVO keyVO2 = new DSPScheduleMatchKeyVO("1", 0)
        DSPScheduleMatchKeyVO keyVO3 = new DSPScheduleMatchKeyVO("0", 117)
        DSPScheduleMatchKeyVO keyVO4 = new DSPScheduleMatchKeyVO("0", 0)

        DSPScheduleMatchValueVO valueVO1 = new DSPScheduleMatchValueVO(24, 60, 300, "1", 5)
        DSPScheduleMatchValueVO valueVO2 = new DSPScheduleMatchValueVO(23, 59, 301, "1", 6)
        DSPScheduleMatchValueVO valueVO3 = new DSPScheduleMatchValueVO(22, 58, 302, "1", 7)
        DSPScheduleMatchValueVO valueVO4 = new DSPScheduleMatchValueVO(21, 57, 303, "1", 8)

        map.put("0#117", new BaseCarConfig.Config(keyVO3, Lists.newArrayList(valueVO3)))
        map.put("0#0", new BaseCarConfig.Config(keyVO4, Lists.newArrayList(valueVO4)))

        when: "执行校验方法"
        def result = config.getValue(cityId, carTypeId)

        then: "验证校验结果"
        res == Objects.isNull(result)

        where:
        cityId | carTypeId || res
        "1"    | 117       || false
    }

    @Unroll
    def "test4"() {
        given: "Mock数据"
        DSPScheduleMatchKeyVO keyVO1 = new DSPScheduleMatchKeyVO("1", 117)
        DSPScheduleMatchKeyVO keyVO2 = new DSPScheduleMatchKeyVO("1", 0)
        DSPScheduleMatchKeyVO keyVO3 = new DSPScheduleMatchKeyVO("0", 117)
        DSPScheduleMatchKeyVO keyVO4 = new DSPScheduleMatchKeyVO("0", 0)

        DSPScheduleMatchValueVO valueVO1 = new DSPScheduleMatchValueVO(24, 60, 300, "1", 5)
        DSPScheduleMatchValueVO valueVO2 = new DSPScheduleMatchValueVO(23, 59, 301, "1", 6)
        DSPScheduleMatchValueVO valueVO3 = new DSPScheduleMatchValueVO(22, 58, 302, "1", 7)
        DSPScheduleMatchValueVO valueVO4 = new DSPScheduleMatchValueVO(21, 57, 303, "1", 8)

        map.put("0#0", new BaseCarConfig.Config(keyVO4, Lists.newArrayList(valueVO4)))

        when: "执行校验方法"
        def result = config.getValue(cityId, carTypeId)

        then: "验证校验结果"
        res == Objects.isNull(result)

        where:
        cityId | carTypeId || res
        "1"    | 117       || false
    }

    @Unroll
    def "test5"() {
        given: "Mock数据"
        DSPScheduleMatchKeyVO keyVO1 = new DSPScheduleMatchKeyVO("1", 117)
        DSPScheduleMatchKeyVO keyVO2 = new DSPScheduleMatchKeyVO("1", 0)
        DSPScheduleMatchKeyVO keyVO3 = new DSPScheduleMatchKeyVO("0", 117)
        DSPScheduleMatchKeyVO keyVO4 = new DSPScheduleMatchKeyVO("0", 0)

        DSPScheduleMatchValueVO valueVO1 = new DSPScheduleMatchValueVO(24, 60, 300, "1", 5)
        DSPScheduleMatchValueVO valueVO2 = new DSPScheduleMatchValueVO(23, 59, 301, "1", 6)
        DSPScheduleMatchValueVO valueVO3 = new DSPScheduleMatchValueVO(22, 58, 302, "1", 7)
        DSPScheduleMatchValueVO valueVO4 = new DSPScheduleMatchValueVO(21, 57, 303, "1", 8)

        when: "执行校验方法"
        def result = config.getValue(cityId, carTypeId)

        then: "验证校验结果"
        res == Objects.isNull(result)

        where:
        cityId | carTypeId || res
        "1"    | 117       || true
    }
}
