package com.ctrip.dcs.dsp.delay.carconfig

import com.ctrip.dcs.dsp.delay.carconfig.key.SpecialDateKey
import com.ctrip.dcs.dsp.delay.carconfig.value.SpecialDateValue
import com.ctrip.dcs.dsp.delay.consts.CommonConstant
import com.ctrip.dcs.dsp.delay.util.DateUtil
import com.google.common.collect.Maps
import org.assertj.core.util.Lists
import org.junit.Assert
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 * @since 2024/8/19 17:04
 */
class SpecialDateConfigTest extends Specification {
    def testObj = new SpecialDateConfig()


    def setup() {
        testObj.map = buildConfigMap()
    }


    def "test onChange1"() {
        given:
        testObj.map = null;

        when:
        HashMap<String, SpecialDateConfig.Config> newMap = Maps.newHashMap();
        newMap.put("11",new SpecialDateConfig.Config());
        testObj.onChange(newMap);

        then:
        Assert.assertTrue(Objects.nonNull(newMap));
    }

    def "test onChange2"() {
        given:
        testObj.map = buildConfigMap1();

        HashMap<String, SpecialDateConfig.Config> newMap = buildConfigMap();
        when:
        testObj.onChange(newMap);

        then:
        Assert.assertTrue(Objects.nonNull(newMap));
    }

    @Unroll
    def "test diff"() {
        given:
        when:
        def result = testObj.diff(newMap, originMap)

        then:
        Objects.equals(result.size(), expectedResult)

        where:
        newMap            | originMap         || expectedResult
        null              | Maps.newHashMap() || 0
        Maps.newHashMap() | null              || 0
        Maps.newHashMap() | Maps.newHashMap() || 0
        buildConfigMap()  | buildConfigMap()  || 0
        buildConfigMap()  | buildConfigMap1() || 1
    }


    @Unroll
    def "test get"() {
        given:
        when:
        def result = testObj.get(cityId, carTypeId, date)

        then:
        Objects.equals(Objects.nonNull(result), expectedResult)

        where:
        cityId | carTypeId | date       || expectedResult
        111    | 1         | new Date() || false
        88     | 124       | new Date() || false
        111     | 124       | DateUtil.parseDate("2024-10-01 10:00:00") || true
        111     | 124       | DateUtil.parseDate("2024-10-07 11:00:00") || true
        111     | 124       | DateUtil.parseDate("2024-10-02 10:10:00") || true
        111     | 124       | DateUtil.parseDate("2024-10-02 15:10:00") || false
        111     | 124       | DateUtil.parseDate("2024-10-08 15:10:00") || false
        111     | 124       | DateUtil.parseDate("2024-11-01 15:10:00") || true
        111     | 124       | DateUtil.parseDate("2024-11-01 16:10:00") || false
    }


    def HashMap<String, SpecialDateConfig.Config> buildConfigMap1() {
        HashMap<String, SpecialDateConfig.Config> configMap = Maps.newHashMap();
        SpecialDateConfig.Config config = new SpecialDateConfig.Config();
        SpecialDateKey specialDateKey = new SpecialDateKey();
        specialDateKey.setCityId(111);
        specialDateKey.setCarTypeId(124);
        config.setKey(specialDateKey);
        List<SpecialDateValue> values = Lists.newArrayList();
        SpecialDateValue value1 = new SpecialDateValue();
        value1.setStartTime("10:00:00")
        value1.setStartDate("2024-10-01")
        value1.setEndTime("11:00:00")
        value1.setEndDate("2024-10-07")
        value1.setType("once")
        values.add(value1);
        config.setValues(values);
        configMap.put("111" + CommonConstant.PLACEHOLDER + "124", config);
        return configMap;
    }

    def HashMap<String, SpecialDateConfig.Config> buildConfigMap() {
        HashMap<String, SpecialDateConfig.Config> configMap = Maps.newHashMap();
        SpecialDateConfig.Config config = new SpecialDateConfig.Config();
        SpecialDateKey specialDateKey = new SpecialDateKey();
        specialDateKey.setCityId(111);
        specialDateKey.setCarTypeId(124);
        config.setKey(specialDateKey);
        List<SpecialDateValue> values = Lists.newArrayList();
        SpecialDateValue value1 = new SpecialDateValue();
        value1.setStartTime("10:00:00")
        value1.setStartDate("2024-10-01")
        value1.setEndTime("11:00:00")
        value1.setEndDate("2024-10-07")
        value1.setType("once")
        SpecialDateValue value2 = new SpecialDateValue();
        value2.setStartTime("15:00");
        value2.setStartDate("2024-11-01");
        value2.setEndTime("16:00");
        value2.setEndDate("2024-11-01");
        value2.setType("years");
        values.add(value1);
        values.add(value2);
        config.setValues(values);
        configMap.put("111" + CommonConstant.PLACEHOLDER + "124", config);
        return configMap;
    }
}
