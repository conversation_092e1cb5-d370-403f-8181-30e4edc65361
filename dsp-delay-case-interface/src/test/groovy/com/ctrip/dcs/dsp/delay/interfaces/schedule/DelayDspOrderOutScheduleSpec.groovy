package com.ctrip.dcs.dsp.delay.interfaces.schedule

import com.ctrip.dcs.dsp.delay.infrastructure.mapper.DelayDspTaskMapper
import com.ctrip.dcs.dsp.delay.infrastructure.mapper.DelayDspTaskRecordMapper
import com.ctrip.dcs.dsp.delay.model.DelayDspTaskRecord
import com.ctrip.dcs.dsp.delay.mq.MessageProducer
import com.ctrip.dcs.dsp.delay.qconfig.DelayDspCommonQConfig
import com.google.common.collect.Lists
import qunar.tc.schedule.MockParameter
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 */
class DelayDspOrderOutScheduleSpec extends Specification {

    def delayDspTaskRecordMapper = Mock(DelayDspTaskRecordMapper)

    def messageProducer = Mock(MessageProducer)

    def delayDspCommonQConfig = Mock(DelayDspCommonQConfig)

    def delayDspTaskMapper = Mock(DelayDspTaskMapper)

    def schedule = new DelayDspOrderOutSchedule(delayDspTaskRecordMapper: delayDspTaskRecordMapper, messageProducer: messageProducer, delayDspCommonQConfig: delayDspCommonQConfig, delayDspTaskMapper: delayDspTaskMapper)

    @Unroll
    def "test taken"() {

        given: "Mock数据"
        delayDspTaskRecordMapper.queryNotOut(_, _) >> Lists.newArrayList() >> Lists.newArrayList(new DelayDspTaskRecord(id: 1L, orderId: "1", taskId: 1))
        delayDspCommonQConfig.getCheckOrderOutMillisecond() >> 1

        when: "执行校验方法"
        schedule.taken(new MockParameter())
        schedule.taken(new MockParameter())

        then: "验证校验结果"
        true
    }


    @Unroll
    def "test redispatc1h"() {

        given: "Mock数据"
        delayDspTaskRecordMapper.queryCountNotOut(_) >> countNotOut
        delayDspTaskRecordMapper.getDistinctTaskIdNotRedispatch() >> taskIds
        delayDspCommonQConfig.getMinutesBeforeDeadTime() >> minutesBeforeDeadTime
        delayDspTaskMapper.queryByTaskIdsAndDeadTime(_, _) >> redispatchTaskIds
        delayDspTaskRecordMapper.queryNotOutByTask(_, _) >> queryNotOutByTask
        delayDspTaskRecordMapper.queryNotOut(_, _) >> queryNotOut
        delayDspCommonQConfig.getOutRedispatchSwitch() >> 1
        when: "执行校验方法"
        def result = schedule.redispatch(new MockParameter())

        then: "验证校验结果"
        res == result

        where:
        countNotOut | taskIds                 | minutesBeforeDeadTime | redispatchTaskIds       | queryNotOutByTask | queryNotOut                                  || res
        1           | null                    | null                  | null                    | null              | null                                         || null
        1           | Lists.newArrayList("1") | 1                     | null                    | null              | null                                         || null
        1           | Lists.newArrayList("1") | 1                     | Lists.newArrayList("1") | null              | null                                         || null
        1           | Lists.newArrayList("1") | 1                     | Lists.newArrayList("1") | null              | null                                         || null
        null        | Lists.newArrayList("1") | 1                     | Lists.newArrayList("1") | null              | Lists.newArrayList(new DelayDspTaskRecord()) || null
        0           | Lists.newArrayList("1") | 1                     | Lists.newArrayList("1") | null              | Lists.newArrayList(new DelayDspTaskRecord()) || null

    }
}
