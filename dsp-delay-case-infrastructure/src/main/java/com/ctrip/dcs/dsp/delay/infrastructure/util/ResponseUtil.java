package com.ctrip.dcs.dsp.delay.infrastructure.util;

import com.ctrip.igt.HasIGTResponseResult;
import com.ctrip.igt.ResponseResult;

import java.util.Optional;

/**
 * <AUTHOR>
 */
public class ResponseUtil {

    public static boolean isSuccess(HasIGTResponseResult response) {
        return Optional.ofNullable(response).map(HasIGTResponseResult::getResponseResult).map(ResponseResult::isSuccess).orElse(false);
    }
}
