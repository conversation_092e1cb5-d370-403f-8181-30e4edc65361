package com.ctrip.dcs.dsp.delay.qconfig.key;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class DriverHighProfitLineDayKeyVO implements CarConfigKeyVO {
    private Integer cityId;

    private Integer carTypeId;


    @Override
    public String toKey() {
        return cityId + PLACEHOLDER + carTypeId;
    }
}
