package com.ctrip.dcs.dsp.delay.validator.impl

import com.ctrip.dcs.dsp.delay.gateway.ChannelGateway
import com.ctrip.dcs.dsp.delay.model.ChannelNumber
import com.ctrip.dcs.dsp.delay.model.DelayDspTask
import com.ctrip.dcs.dsp.delay.model.Driver
import com.ctrip.dcs.dsp.delay.model.DriverAggregation
import com.ctrip.dcs.dsp.delay.model.SupplyOrder
import com.ctrip.dcs.dsp.delay.qconfig.DelayDspCommonQConfig
import com.ctrip.dcs.dsp.delay.qconfig.InventoryValidatorCityValueConfig
import com.ctrip.dcs.dsp.delay.service.DelayTaskService
import com.ctrip.dcs.dsp.delay.service.DriverInventoryService
import com.ctrip.dcs.dsp.delay.service.DriverService
import com.ctrip.dcs.dsp.delay.util.DateUtil
import com.ctrip.dcs.dsp.delay.validator.ValidatorCode
import com.google.common.collect.Sets
import org.assertj.core.util.Lists
import org.springframework.beans.factory.annotation.Autowired
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 */
class InventoryValidatorSpec extends Specification {

    def delayTaskService = Mock(DelayTaskService)

    def driverService = Mock(DriverService)

    def driverInventoryService = Mock(DriverInventoryService)

    def inventoryValidatorCityValueConfig = Mock(InventoryValidatorCityValueConfig)


    def validator = new InventoryValidator(delayTaskService: delayTaskService, driverService: driverService, driverInventoryService: driverInventoryService, inventoryValidatorCityValueConfig: inventoryValidatorCityValueConfig)

    @Unroll
    def "validate"() {

        given: "Mock数据"
        inventoryValidatorCityValueConfig.getValue(_) >> valueConfig
        delayTaskService.buildTaskTemp(_) >> taskTemp
        driverService.queryDriverAggregationForInsert(_) >> driverAggregations
        driverInventoryService.checkInventoryConflict(_, _) >> inventoryConflict

        when: "执行校验方法"
        def res = validator.validate(order)

        then: "验证校验结果"
        res.getCode() == code

        where:
        order            | valueConfig        | taskTemp           | driverAggregations        | inventoryConflict || code
        getSupplyOrder() | false              | null               | null                      | null              || ValidatorCode.OK
        getSupplyOrder() | getChannelNumber() | null               | null                      | null              || ValidatorCode.TASK_NOT_EXIST
        getSupplyOrder() | getChannelNumber() | new DelayDspTask() | Lists.newArrayList()      | null              || ValidatorCode.NO_DRIVER
        getSupplyOrder() | getChannelNumber() | new DelayDspTask() | buildDriverAggregations() | null              || ValidatorCode.NO_DRIVER
        getSupplyOrder() | getChannelNumber() | new DelayDspTask() | buildDriverAggregations() | "1111"            || ValidatorCode.NO_DRIVER
        getSupplyOrder() | getChannelNumber() | new DelayDspTask() | buildDriverAggregations() | "1"               || ValidatorCode.OK
        getSupplyOrder() | getChannelNumber() | new DelayDspTask() | buildDriverAggregations2() | "1"               || ValidatorCode.NO_DRIVER
        getSupplyOrder() | getChannelNumber() | new DelayDspTask() | buildDriverAggregations3() | "1"               || ValidatorCode.NO_DRIVER
        getSupplyOrder1() | getChannelNumber() | new DelayDspTask() | buildDriverAggregations4() | "1"               || ValidatorCode.OK
        getSupplyOrder2() | getChannelNumber() | new DelayDspTask() | buildDriverAggregations5() | "1"               || ValidatorCode.NO_DRIVER

    }


    def getSupplyOrder2() {
        return new SupplyOrder(distributionChannel: 1L, sysExpectBookTime:new Date())
    }


    List<DriverAggregation> buildDriverAggregations5() {
        DriverAggregation driverAggregation = new DriverAggregation()
        String date = DateUtil.formatDate(DateUtil.addMinutes(new Date(), -100), "yyyy-MM-dd HH:mm:ss")
        driverAggregation.setDriver(new Driver(driverId: "1", overAgeTime:date))
        return Lists.newArrayList(driverAggregation)
    }


    List<DriverAggregation> buildDriverAggregations4() {
        DriverAggregation driverAggregation = new DriverAggregation()
        String date = DateUtil.formatDate(DateUtil.addMinutes(new Date(), 100), "yyyy-MM-dd HH:mm:ss")
        driverAggregation.setDriver(new Driver(driverId: "1", overAgeTime:date))
        return Lists.newArrayList(driverAggregation)
    }

    def getSupplyOrder() {
        return new SupplyOrder(distributionChannel: 1L)
    }

    def getSupplyOrder1() {
        return new SupplyOrder(distributionChannel: 1L, sysExpectBookTime:new Date())
    }

    def getChannelNumber() {
        return new ChannelNumber(
                channelNumberId: 1L,
                primaryChannelGroupId: 2L,
                secondaryChannelGroupId: 3L,
                tertiaryChannelGroupId: 4L
        )
    }

    def newHashSet(long i) {
        return Sets.newHashSet(i)
    }

    List<DriverAggregation> buildDriverAggregations() {
        DriverAggregation driverAggregation = new DriverAggregation()
        driverAggregation.setDriver(new Driver(driverId: "1"))
        return Lists.newArrayList(driverAggregation)
    }

    List<DriverAggregation> buildDriverAggregations2() {
        DriverAggregation driverAggregation = new DriverAggregation()
        driverAggregation.setDriver(new Driver(driverId: "0"))
        return Lists.newArrayList(driverAggregation)
    }
    List<DriverAggregation> buildDriverAggregations3() {
        DriverAggregation driverAggregation = new DriverAggregation()
        driverAggregation.setDriver(new Driver(driverId: null))
        return Lists.newArrayList(driverAggregation)
    }



}
