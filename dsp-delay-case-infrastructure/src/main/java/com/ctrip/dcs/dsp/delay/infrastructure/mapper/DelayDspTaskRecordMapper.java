package com.ctrip.dcs.dsp.delay.infrastructure.mapper;

import com.ctrip.dcs.dsp.delay.model.DelayDspTaskRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface DelayDspTaskRecordMapper {

    void insert(@Param("items") List<DelayDspTaskRecord> records);

    void update(DelayDspTaskRecord record);

    List<DelayDspTaskRecord> queryNotOut(@Param("matchSuccess") Integer matchSuccess, @Param("pageSize") Integer pageSize);

    Integer queryCountNotOut(@Param("matchSuccess") Integer matchSuccess);


    DelayDspTaskRecord query(@Param("id") Long recordId);

    List<DelayDspTaskRecord> queryByTaskId(@Param("taskId") Long taskId, @Param("preOut") Integer preOut);


    void updateMatchStatus(@Param("isOut") Integer isOut, @Param("matchSuccess") Integer matchSuccess, @Param("recordId") Long recordId);

    List<Long> getDistinctTaskIdNotRedispatch();

    List<DelayDspTaskRecord> queryNotOutByTask(@Param("redispatchTaskIds")List<Long> redispatchTaskIds, @Param("pageSize") Integer pageSize);

    DelayDspTaskRecord queryByDspOrderId(@Param("dspOrderId") String dspOrderId);
}
