package com.ctrip.dcs.dsp.delay.qconfig.value;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
public class DriverHighProfitLineDayValueVO implements CarConfigValueVO {
    private Integer standardProfit;
    private Integer highProfit;

    @JsonCreator
    public DriverHighProfitLineDayValueVO(@JsonProperty("standardProfit") Integer standardProfit, @JsonProperty("highProfit") Integer highProfit) {
        this.standardProfit = standardProfit;
        this.highProfit = highProfit;
    }
}
