package com.ctrip.dcs.dsp.delay.limit;

import com.ctrip.dcs.dsp.delay.qconfig.DelayDspCommonQConfig;
import com.ctrip.dcs.dsp.delay.util.MetricsUtil;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.google.common.util.concurrent.RateLimiter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * 接单限流器
 * <AUTHOR>
 */
@Component
public class TakenRateLimiter {

    private static final Logger logger = LoggerFactory.getLogger(TakenRateLimiter.class);

    @Autowired
    private DelayDspCommonQConfig delayDspCommonQConfig;

    private RateLimiter limiter;

    @PostConstruct
    public void init() {
        limiter = RateLimiter.create(delayDspCommonQConfig.getTakenPermitsPerSecond());
    }

    public double acquire() {
        try {
            double acquire = limiter.acquire();
            MetricsUtil.recordTime("taken.rate.limiter.acquire.time", (long) acquire * 1000);
            return acquire;
        } catch (Exception e) {
            logger.error(e);
        }
        return 0D;
    }
}
