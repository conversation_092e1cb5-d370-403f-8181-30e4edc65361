package com.ctrip.dcs.dsp.delay.infrastructure.service;

import com.ctrip.dcs.dsp.delay.infrastructure.trocks.TRocksProviderProxy;
import com.ctrip.dcs.dsp.delay.model.*;
import com.ctrip.dcs.dsp.delay.qconfig.DelayDspCommonQConfig;
import com.ctrip.dcs.dsp.delay.util.DateUtil;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.google.common.collect.Lists;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.Arrays;
import java.util.Date;

import static org.mockito.Mockito.*;

/**
 * <AUTHOR>
 */
@RunWith(PowerMockRunner.class)
@PowerMockIgnore("javax.net.ssl.*")
public class DispatcherDriverServiceImplTest {

    @InjectMocks
    DispatcherDriverServiceImpl dispatcherDriverServiceImpl;
    @Mock
    Logger logger;
    @Mock
    TRocksProviderProxy rocksProviderProxy;
    @Mock
    DelayDspCommonQConfig delayDspCommonQConfig;
    @Mock
    DelayDspTask delayDspTask;
    @Mock
    DriverAggregation driverAggregation;
    @Mock
    Driver driver;

    @Test
    public void testJoinNullTask() throws Exception {
        dispatcherDriverServiceImpl.join(null, Lists.newArrayList());
        Mockito.verify(delayDspCommonQConfig, Mockito.times(0)).getDriverMatchMaxSeconds();
    }

    @Test
    public void testJoinNullDrivers() throws Exception {
        dispatcherDriverServiceImpl.join(delayDspTask, Lists.newArrayList());
        Mockito.verify(delayDspCommonQConfig, Mockito.times(0)).getDriverMatchMaxSeconds();
    }

    @Test
    public void testJoinNotMatchCity() throws Exception {
        PowerMockito.when(delayDspCommonQConfig.getDriverMatchCheckCityIds()).thenReturn("");
        dispatcherDriverServiceImpl.join(delayDspTask, Lists.newArrayList(driverAggregation));
        Mockito.verify(delayDspCommonQConfig, Mockito.times(0)).getDriverMatchMaxSeconds();
    }

    @Test
    public void testJoin() throws Exception {
        PowerMockito.when(delayDspCommonQConfig.getDriverMatchCheckCityIds()).thenReturn("1,2");
        PowerMockito.when(delayDspTask.getCityId()).thenReturn(1);
        PowerMockito.when(delayDspTask.getBeginTime()).thenReturn(new Date());
        PowerMockito.when(delayDspTask.getEndTime()).thenReturn(new Date());
        PowerMockito.when(driverAggregation.getDriver()).thenReturn(driver);
        PowerMockito.when(driver.getDriverId()).thenReturn("1");
        dispatcherDriverServiceImpl.join(delayDspTask, Lists.newArrayList(driverAggregation));
        Mockito.verify(delayDspCommonQConfig, Mockito.times(1)).getDriverMatchMaxSeconds();
    }

    @Test
    public void testExitNull() throws Exception {
        dispatcherDriverServiceImpl.exit(Lists.newArrayList());
        Mockito.verify(rocksProviderProxy, Mockito.times(0)).del("DRIVER_MATCH_DELAY_DSP_1");
    }

    @Test
    public void testExit() throws Exception {
        PowerMockito.when(driverAggregation.getDriver()).thenReturn(driver);
        PowerMockito.when(driver.getDriverId()).thenReturn("1");
        dispatcherDriverServiceImpl.exit(Lists.newArrayList(driverAggregation));
        Mockito.verify(rocksProviderProxy, Mockito.times(1)).del("DRIVER_MATCH_DELAY_DSP_1");
    }

    @Test
    public void testIsMatch() throws Exception {
        Boolean r1 = dispatcherDriverServiceImpl.isMatch("", "2023-11-23 12:00:00");
        Assert.assertFalse(r1);
        Boolean r2 = dispatcherDriverServiceImpl.isMatch("1", "");
        Assert.assertFalse(r2);
        PowerMockito.when(rocksProviderProxy.get("DRIVER_MATCH_DELAY_DSP_1")).thenReturn("");
        Boolean r3 = dispatcherDriverServiceImpl.isMatch("1", "2023-11-23 12:00:00");
        Assert.assertFalse(r3);
        PowerMockito.when(rocksProviderProxy.get("DRIVER_MATCH_DELAY_DSP_1")).thenReturn("2023-11-23 13:00:00_2023-11-23 14:00:00");
        Boolean r4 = dispatcherDriverServiceImpl.isMatch("1", "2023-11-23 12:00:00");
        Assert.assertFalse(r4);
        PowerMockito.when(rocksProviderProxy.get("DRIVER_MATCH_DELAY_DSP_1")).thenReturn("2023-11-23 13:00:00_2023-11-23 14:00:00");
        Boolean r5 = dispatcherDriverServiceImpl.isMatch("1", "2023-11-23 15:00:00");
        Assert.assertFalse(r5);
        PowerMockito.when(rocksProviderProxy.get("DRIVER_MATCH_DELAY_DSP_1")).thenReturn("2023-11-23 13:00:00_2023-11-23 14:00:00");
        Boolean r6 = dispatcherDriverServiceImpl.isMatch("1", "2023-11-23 13:30:00");
        Assert.assertTrue(r6);
    }

    @Test
    public void testIsBetween() throws Exception {
        Boolean r1 = dispatcherDriverServiceImpl.isBetween(null, null, null);
        Assert.assertFalse(r1);
        Boolean r2 = dispatcherDriverServiceImpl.isBetween("2023-11-23 12:00:00", null, null);
        Assert.assertFalse(r2);
        Boolean r3 = dispatcherDriverServiceImpl.isBetween("2023-11-23 12:00:00", "2023-11-23 12:00:00", null);
        Assert.assertFalse(r3);
        Boolean r4 = dispatcherDriverServiceImpl.isBetween("2023-11-23 12:00:00", "2023-11-23 12:00:00", "2023-11-23 12:00:00");
        Assert.assertTrue(r4);
    }

    @Test
    public void testIsMatchCity() throws Exception {
        Boolean r1 = dispatcherDriverServiceImpl.isMatchCity(null);
        Assert.assertFalse(r1);
        PowerMockito.when(delayDspCommonQConfig.getDriverMatchCheckCityIds()).thenReturn("");
        Boolean r2 = dispatcherDriverServiceImpl.isMatchCity(1);
        Assert.assertFalse(r2);
        PowerMockito.when(delayDspCommonQConfig.getDriverMatchCheckCityIds()).thenReturn("2,3");
        Boolean r3 = dispatcherDriverServiceImpl.isMatchCity(1);
        Assert.assertFalse(r3);
        PowerMockito.when(delayDspCommonQConfig.getDriverMatchCheckCityIds()).thenReturn("1,2,3");
        Boolean r4 = dispatcherDriverServiceImpl.isMatchCity(1);
        Assert.assertTrue(r4);
        PowerMockito.when(delayDspCommonQConfig.getDriverMatchCheckCityIds()).thenReturn("all");
        Boolean r5 = dispatcherDriverServiceImpl.isMatchCity(1);
        Assert.assertTrue(r5);
    }

}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme