package com.ctrip.dcs.dsp.delay.core;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.modules.junit4.PowerMockRunner;

/**
 * <AUTHOR>
 */
@RunWith(PowerMockRunner.class)
public class DPProfitFunctionTest {

    @Mock
    private DP dp;

    @Test
    public void testOrderNetProfitFunction() {
        PowerMockito.when(dp.profit()).thenReturn(new double[]{100, 200});
        PowerMockito.when(dp.kilo()).thenReturn(new double[]{5, 10});
        PowerMockito.when(dp.distance()).thenReturn(new double[][]{{0, 0, 0}, {0, 0, 10}, {0, 0, 0}});
        DP.OrderNetProfitFunction function = new DP.OrderNetProfitFunction();
        double profit = function.apply(2, 1, dp);
        Assert.assertEquals(profit, 190, 0.0000);
    }

    @Test
    public void testOrderProfitFunction() {
        PowerMockito.when(dp.profit()).thenReturn(new double[]{100, 200});
        PowerMockito.when(dp.kilo()).thenReturn(new double[]{5, 10});
        PowerMockito.when(dp.distance()).thenReturn(new double[][]{{0, 0, 0}, {0, 0, 10}, {0, 0, 0}});
        DP.OrderProfitFunction function = new DP.OrderProfitFunction();
        double profit = function.apply(2, 1, dp);
        Assert.assertEquals(profit, 200, 0.0000);
    }
}
