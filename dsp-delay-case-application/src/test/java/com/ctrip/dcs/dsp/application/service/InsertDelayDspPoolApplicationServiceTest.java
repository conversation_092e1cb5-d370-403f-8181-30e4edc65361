package com.ctrip.dcs.dsp.application.service;

import com.ctrip.dcs.dsp.application.service.impl.InsertDelayDspPoolApplicationServiceImpl;
import com.ctrip.dcs.dsp.delay.cache.DistributedCache;
import com.ctrip.dcs.dsp.delay.gateway.DriverGateway;
import com.ctrip.dcs.dsp.delay.gateway.SelfDispatcherOrderGateway;
import com.ctrip.dcs.dsp.delay.model.DelayDspTask;
import com.ctrip.dcs.dsp.delay.model.SupplyOrder;
import com.ctrip.dcs.dsp.delay.mq.MessageProducer;
import com.ctrip.dcs.dsp.delay.qconfig.DelayDspCommonQConfig;
import com.ctrip.dcs.dsp.delay.qconfig.GrayscaleQConfig;
import com.ctrip.dcs.dsp.delay.repository.DelayDspOrderRepository;
import com.ctrip.dcs.dsp.delay.repository.DelayDspTaskRepository;
import com.ctrip.dcs.dsp.delay.service.DelayDspOrderService;
import com.ctrip.dcs.dsp.delay.service.DelayTaskService;
import com.ctrip.dcs.dsp.delay.service.DispatchConfigService;
import com.ctrip.dcs.dsp.delay.service.interfaces.message.InsertDelayDspPoolRequestType;
import com.ctrip.dcs.dsp.delay.service.interfaces.message.InsertDelayDspPoolResponseType;
import com.ctrip.dcs.dsp.delay.util.DateUtil;
import com.ctrip.dcs.dsp.delay.util.JsonUtil;
import com.ctrip.dcs.dsp.delay.util.MetricsUtil;
import com.ctrip.dcs.dsp.delay.validator.ValidatorChain;
import com.ctrip.dcs.dsp.delay.validator.ValidatorCode;
import com.ctrip.dcs.dsp.delay.validator.ValidatorDTO;
import com.ctrip.igt.framework.common.metrics.MetricsFactory;
import com.google.common.collect.Maps;
import io.dropwizard.metrics5.MetricRegistry;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.Date;
import java.util.HashMap;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest(value = {MetricsUtil.class, MetricsFactory.class, JsonUtil.class})
public class InsertDelayDspPoolApplicationServiceTest {

    @InjectMocks
    private InsertDelayDspPoolApplicationServiceImpl insertDelayDspPoolApplicationService;

    @Mock
    private InsertDelayDspPoolRequestType requestType;

    @Mock
    private DispatchConfigService dispatchConfigService;

    @Mock
    private DelayDspOrderRepository delayDspOrderRepository;

    @Mock
    private DelayDspTaskRepository delayDspTaskRepository;

    @Mock
    private DelayDspOrderService delayDspOrderService;


    @Mock
    private SelfDispatcherOrderGateway dispatcherOrderGateway;

    @Mock
    private MessageProducer messageProducer;

    @Mock
    private DistributedCache distributedCache;

    @Mock
    private DelayDspCommonQConfig delayDspCommonQConfig;

    @Mock
    private GrayscaleQConfig grayscaleQConfig;

    @Mock
    private ValidatorChain validatorChain;

    @Mock
    private DelayTaskService delayTaskService;

    @Mock
    private DriverGateway driverGateway;

    @Mock
    private SupplyOrder supplyOrder;

    @Mock
    private MetricRegistry metricRegistry;

    @Mock
    private DelayDspTask delayDspTask;

    @Test
    public void testInsertDelayDspPool1() {
        PowerMockito.when(grayscaleQConfig.isGrayscaleCity(Mockito.any())).thenReturn(false);
        when(grayscaleQConfig.isGrayscaleCityAndCarTypeId(any(), any())).thenReturn(Boolean.FALSE);
        InsertDelayDspPoolResponseType response = insertDelayDspPoolApplicationService.insertDelayDspPool(requestType);
        Assert.assertEquals((long)response.getIsDelay(), 0L);
    }

    @Test
    public void testInsertDelayDspPool2() {
        PowerMockito.when(grayscaleQConfig.isGrayscaleCity(Mockito.any())).thenReturn(true);
        when(grayscaleQConfig.isGrayscaleCityAndCarTypeId(any(), any())).thenReturn(Boolean.TRUE);
        PowerMockito.when(dispatcherOrderGateway.query(Mockito.any())).thenReturn(null);
        InsertDelayDspPoolResponseType response = insertDelayDspPoolApplicationService.insertDelayDspPool(requestType);
        Assert.assertEquals((long)response.getIsDelay(), 0L);
    }

    @Test
    public void testInsertDelayDspPool3() {
        PowerMockito.when(grayscaleQConfig.isGrayscaleCity(Mockito.any())).thenReturn(true);
        when(grayscaleQConfig.isGrayscaleCityAndCarTypeId(any(), any())).thenReturn(Boolean.TRUE);
        PowerMockito.when(supplyOrder.isCancel()).thenReturn(true);
        PowerMockito.when(dispatcherOrderGateway.query(Mockito.any())).thenReturn(supplyOrder);
        InsertDelayDspPoolResponseType response = insertDelayDspPoolApplicationService.insertDelayDspPool(requestType);
        Assert.assertEquals((long)response.getIsDelay(), 0L);
    }

    @Test
    public void testInsertDelayDspPool4() {
        PowerMockito.mockStatic(MetricsFactory.class );
        PowerMockito.when(MetricsFactory.getMetricRegistry()).thenReturn(metricRegistry);
        PowerMockito.mockStatic(MetricsUtil.class );
        PowerMockito.mockStatic(JsonUtil.class );
        PowerMockito.when(JsonUtil.toJson(Mockito.any())).thenReturn("");
        PowerMockito.when(grayscaleQConfig.isGrayscaleCity(Mockito.any())).thenReturn(true);
        when(grayscaleQConfig.isGrayscaleCityAndCarTypeId(any(), any())).thenReturn(Boolean.TRUE);
        PowerMockito.when(dispatcherOrderGateway.query(Mockito.any())).thenReturn(supplyOrder);
        PowerMockito.when(supplyOrder.isCancel()).thenReturn(false);
        ValidatorDTO validatorDTO = new ValidatorDTO();
        validatorDTO.setCode(ValidatorCode.ERROR);
        PowerMockito.when(validatorChain.validate(supplyOrder)).thenReturn(validatorDTO);
        InsertDelayDspPoolResponseType response = insertDelayDspPoolApplicationService.insertDelayDspPool(requestType);
        Assert.assertEquals((long)response.getIsDelay(), 0L);
    }

    @Test
    public void testInsertDelayDspPool5() {
        PowerMockito.mockStatic(MetricsFactory.class );
        PowerMockito.when(MetricsFactory.getMetricRegistry()).thenReturn(metricRegistry);
        PowerMockito.mockStatic(MetricsUtil.class );
        PowerMockito.mockStatic(JsonUtil.class );
        PowerMockito.when(JsonUtil.toJson(Mockito.any())).thenReturn("");
        PowerMockito.when(grayscaleQConfig.isGrayscaleCity(Mockito.any())).thenReturn(true);
        when(grayscaleQConfig.isGrayscaleCityAndCarTypeId(any(), any())).thenReturn(Boolean.TRUE);
        PowerMockito.when(dispatcherOrderGateway.query(Mockito.any())).thenReturn(supplyOrder);
        PowerMockito.when(supplyOrder.isCancel()).thenReturn(false);
        ValidatorDTO validatorDTO = new ValidatorDTO();
        validatorDTO.setCode(ValidatorCode.OK);
        PowerMockito.when(validatorChain.validate(supplyOrder)).thenReturn(validatorDTO);
        PowerMockito.when(delayTaskService.queryOrCreate(supplyOrder)).thenReturn(null);
        InsertDelayDspPoolResponseType response = insertDelayDspPoolApplicationService.insertDelayDspPool(requestType);
        Assert.assertEquals((long)response.getIsDelay(), 0L);
    }

    @Test
    public void testInsertDelayDspPool6() {
        PowerMockito.mockStatic(MetricsFactory.class );
        PowerMockito.when(MetricsFactory.getMetricRegistry()).thenReturn(metricRegistry);
        PowerMockito.mockStatic(MetricsUtil.class );
        PowerMockito.mockStatic(JsonUtil.class );
        PowerMockito.when(JsonUtil.toJson(Mockito.any())).thenReturn("");
        PowerMockito.when(grayscaleQConfig.isGrayscaleCity(Mockito.any())).thenReturn(true);
        when(grayscaleQConfig.isGrayscaleCityAndCarTypeId(any(), any())).thenReturn(Boolean.TRUE);
        PowerMockito.when(dispatcherOrderGateway.query(Mockito.any())).thenReturn(supplyOrder);
        PowerMockito.when(supplyOrder.isCancel()).thenReturn(false);
        ValidatorDTO validatorDTO = new ValidatorDTO();
        validatorDTO.setCode(ValidatorCode.OK);
        PowerMockito.when(validatorChain.validate(supplyOrder)).thenReturn(validatorDTO);
        PowerMockito.when(delayTaskService.queryOrCreate(supplyOrder)).thenReturn(delayDspTask);
        PowerMockito.when(delayDspTask.isUnExecuted()).thenReturn(false);
        InsertDelayDspPoolResponseType response = insertDelayDspPoolApplicationService.insertDelayDspPool(requestType);
        Assert.assertEquals((long)response.getIsDelay(), 0L);
    }

    @Test
    public void testInsertDelayDspPool7() {
        PowerMockito.mockStatic(MetricsFactory.class );
        PowerMockito.when(MetricsFactory.getMetricRegistry()).thenReturn(metricRegistry);
        PowerMockito.mockStatic(MetricsUtil.class );
        PowerMockito.mockStatic(JsonUtil.class );
        PowerMockito.when(JsonUtil.toJson(Mockito.any())).thenReturn("");
        PowerMockito.when(grayscaleQConfig.isGrayscaleCity(Mockito.any())).thenReturn(true);
        when(grayscaleQConfig.isGrayscaleCityAndCarTypeId(any(), any())).thenReturn(Boolean.TRUE);
        PowerMockito.when(dispatcherOrderGateway.query(Mockito.any())).thenReturn(supplyOrder);
        PowerMockito.when(supplyOrder.isCancel()).thenReturn(false);
        ValidatorDTO validatorDTO = new ValidatorDTO();

        validatorDTO.setCode(ValidatorCode.OK);
        PowerMockito.when(validatorChain.validate(supplyOrder)).thenReturn(validatorDTO);
        PowerMockito.when(delayTaskService.queryOrCreate(supplyOrder)).thenReturn(delayDspTask);
        PowerMockito.when(delayDspTask.isUnExecuted()).thenReturn(true);
        PowerMockito.when(delayDspTask.getExecuteTime()).thenReturn(DateUtil.addHours(new Date(), 1));
        PowerMockito.when(supplyOrder.getSysExpectBookTime()).thenReturn(DateUtil.addHours(new Date(), -1));
        InsertDelayDspPoolResponseType response = insertDelayDspPoolApplicationService.insertDelayDspPool(requestType);
        Assert.assertEquals((long)response.getIsDelay(), 0L);
    }

    @Test
    public void testInsertDelayDspPool8() {
        PowerMockito.mockStatic(MetricsFactory.class );
        PowerMockito.when(MetricsFactory.getMetricRegistry()).thenReturn(metricRegistry);
        PowerMockito.mockStatic(MetricsUtil.class );
        PowerMockito.mockStatic(JsonUtil.class );
        PowerMockito.when(JsonUtil.toJson(Mockito.any())).thenReturn("");
        PowerMockito.when(grayscaleQConfig.isGrayscaleCity(Mockito.any())).thenReturn(true);
        when(grayscaleQConfig.isGrayscaleCityAndCarTypeId(any(), any())).thenReturn(Boolean.TRUE);
        PowerMockito.when(dispatcherOrderGateway.query(Mockito.any())).thenReturn(supplyOrder);
        PowerMockito.when(supplyOrder.isCancel()).thenReturn(false);
        ValidatorDTO validatorDTO = new ValidatorDTO();
        validatorDTO.setCode(ValidatorCode.OK);
        PowerMockito.when(validatorChain.validate(supplyOrder)).thenReturn(validatorDTO);
        PowerMockito.when(delayTaskService.queryOrCreate(supplyOrder)).thenReturn(delayDspTask);
        PowerMockito.when(delayDspTask.isUnExecuted()).thenReturn(true);
        PowerMockito.when(delayDspTask.getExecuteTime()).thenReturn(DateUtil.addHours(new Date(), 1));
        PowerMockito.when(supplyOrder.getSysExpectBookTime()).thenReturn(DateUtil.addHours(new Date(), 10));
        InsertDelayDspPoolResponseType response = insertDelayDspPoolApplicationService.insertDelayDspPool(requestType);
        Assert.assertEquals((long)response.getIsDelay(), 1L);
    }

    @Test
    public void testSendDeadlineMessage() {
        HashMap<String, Object> msg = Maps.newHashMap();
        PowerMockito.when(delayDspTask.getExecuteTimeDeadline()).thenReturn(DateUtil.addHours(new Date(), 1));
        insertDelayDspPoolApplicationService.sendDeadlineMessage(delayDspTask, msg, 60);
        Assert.assertFalse(msg.isEmpty());
    }
}
