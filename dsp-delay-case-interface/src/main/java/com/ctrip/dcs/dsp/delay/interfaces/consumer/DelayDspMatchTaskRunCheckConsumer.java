package com.ctrip.dcs.dsp.delay.interfaces.consumer;

import com.ctrip.dcs.dsp.delay.consts.CommonConstant;
import com.ctrip.dcs.dsp.delay.enums.DelayDspTaskStatus;
import com.ctrip.dcs.dsp.delay.enums.YesOrNo;
import com.ctrip.dcs.dsp.delay.model.DelayDspOrder;
import com.ctrip.dcs.dsp.delay.model.DelayDspTask;
import com.ctrip.dcs.dsp.delay.repository.DelayDspOrderRepository;
import com.ctrip.dcs.dsp.delay.repository.DelayDspTaskRepository;
import com.ctrip.dcs.dsp.delay.util.JsonUtil;
import com.ctrip.dcs.dsp.delay.util.MetricsUtil;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.qmq.annontation.QmqLogTag;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.NeedRetryException;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;

import java.util.List;
import java.util.Objects;

/**
 * 检查延后派匹配任务是否执行完成
 * <AUTHOR>
 */
@Component
public class DelayDspMatchTaskRunCheckConsumer {

    private static final Logger logger = LoggerFactory.getLogger(DelayDspMatchTaskRunCheckConsumer.class);

    @Autowired
    private DelayDspTaskRepository delayDspTaskRepository;

    @Autowired
    private DelayDspOrderRepository delayDspOrderRepository;

    @QmqLogTag(tagKeys = {"taskId"})
    @QmqConsumer(prefix = CommonConstant.DELAY_DSP_MATCH_TASK_RUN_CHECK_SUBJECT, consumerGroup = CommonConstant.APP_ID)
    public void onMessage(Message message) {
        try {
            Long taskId = message.getLongProperty("taskId");
            logger.info("DelayDspMatchTaskRunCheckConsumer", JsonUtil.toJson(taskId));
            if (taskId == 0L) {
                return;
            }
            DelayDspTask task = delayDspTaskRepository.queryByTaskId(taskId);
            logger.info("DelayDspMatchTaskRunCheckConsumer_task", JsonUtil.toJson(task));

            if (isComplete(task)) {
                return;
            }
            logger.warn("DelayDspMatchTaskRunCheckConsumer_notComplete", JsonUtil.toJson(taskId));
            MetricsUtil.recordValue("dispatch.delay.match.task.execute.timeout", 1);
        } catch (Exception e) {
            logger.warn("DelayDspMatchTaskRunCheckConsumer", e);
            MetricsUtil.recordValue("dispatch.delay.match.task.execute.timeout.error", 1);
            // 5秒后重试
            throw new NeedRetryException(System.currentTimeMillis() + 5 * 1000, "DelayDspMatchTaskRunCheckConsumer");
        }
    }

    public boolean isComplete(DelayDspTask task) {
        if (task == null) {
            throw new IllegalArgumentException("task is null");
        }
        if (Objects.equals(task.getTaskStatus(), DelayDspTaskStatus.MATCH_EXECUTION_COMPLETE.getCode()) || Objects.equals(task.getTaskStatus(), DelayDspTaskStatus.UN_EXECUTED.getCode())) {
            return true;
        }
        List<DelayDspOrder> orders = delayDspOrderRepository.queryByTaskId(task.getTaskId(), YesOrNo.YES.getCode());
        return CollectionUtils.isEmpty(orders);
    }
}
