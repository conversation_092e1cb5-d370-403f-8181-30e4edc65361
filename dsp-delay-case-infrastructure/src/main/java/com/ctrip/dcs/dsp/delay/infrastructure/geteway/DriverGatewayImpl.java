package com.ctrip.dcs.dsp.delay.infrastructure.geteway;

import com.ctrip.dcs.dsp.delay.enums.DelayDspTaskType;
import com.ctrip.dcs.dsp.delay.enums.YesOrNo;
import com.ctrip.dcs.driver.domain.account.DriverUDL;
import com.ctrip.dcs.driver.domain.account.QueryUDLByDriverIdRequestType;
import com.ctrip.dcs.driver.domain.account.QueryUDLByDriverIdResponseType;
import com.ctrip.dcs.dsp.delay.enums.UdlEnum;
import com.ctrip.dcs.dsp.delay.gateway.DriverGateway;
import com.ctrip.dcs.dsp.delay.infrastructure.converter.CommonConverter;
import com.ctrip.dcs.dsp.delay.infrastructure.dto.CommonThreadContext;
import com.ctrip.dcs.dsp.delay.infrastructure.dto.DriverUdlVO;
import com.ctrip.dcs.dsp.delay.infrastructure.http.DriverScoreServiceProxy;
import com.ctrip.dcs.dsp.delay.infrastructure.soa.DriverDomainServiceProxy;
import com.ctrip.dcs.dsp.delay.infrastructure.soa.TmsTransportServiceProxy;
import com.ctrip.dcs.dsp.delay.infrastructure.thread.EnableConfigThreadPool;
import com.ctrip.dcs.dsp.delay.infrastructure.util.ThreadlUtil;
import com.ctrip.dcs.dsp.delay.infrastructure.util.TmsTransportServiceClientUtil;
import com.ctrip.dcs.dsp.delay.model.Driver;
import com.ctrip.dcs.dsp.delay.model.DriverLeave;
import com.ctrip.dcs.dsp.delay.model.DriverScore;
import com.ctrip.dcs.dsp.delay.model.TransportSku;
import com.ctrip.dcs.dsp.delay.service.impl.SDDispatcherTaskServiceImpl;
import com.ctrip.dcs.dsp.delay.qconfig.DelayDspCommonQConfig;
import com.ctrip.dcs.dsp.delay.util.JsonUtil;
import com.ctrip.dcs.dsp.delay.util.MetricsUtil;
import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.igt.ResponseResult;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerContext;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.concurrent.threadpool.CThreadPool;
import com.ctrip.igt.framework.common.exception.BizException;
import com.ctrip.igt.framework.common.jackson.JacksonSerializer;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
public class DriverGatewayImpl implements DriverGateway {

    private static final Logger logger = LoggerFactory.getLogger(DriverGatewayImpl.class);

    @Autowired
    private TmsTransportServiceProxy tmsTransportServiceProxy;

    @Autowired
    private TransportGatewayImpl transportGateway;

    @Autowired
    private DriverScoreServiceProxy driverScoreServiceProxy;

    @Resource(name = "driverThreadPool")
    private ExecutorService driverThreadPool;
    
    @Resource
    DriverDomainServiceProxy driverDomainServiceProxy;
    
    @Resource
    private DelayDspCommonQConfig delayDspCommonQConfig;
    @Resource
    TmsTransportServiceClientUtil transportServiceClientUtil;
    @Resource
    EnableConfigThreadPool enableConfigThreadPool;

    @Override
    public Driver query(String driverId) {
        try {
            //上云开关开启，则传入udl
            if (delayDspCommonQConfig.isSelfCloudSwitch()) {
                Map<Long, DriverUdlVO> udlMap = getDrvUdlMap(Sets.newHashSet(Long.valueOf(driverId)));
                String udl = Optional.ofNullable(udlMap.get(Long.valueOf(driverId))).map(DriverUdlVO::getUdl).orElse(UdlEnum.SHA.getValue());
                logger.info("queryDriver_udl", "udl:" + udl);
                transportServiceClientUtil.setDrvUdl(udl);
            }
            DriverInfoSOARequestType request = new DriverInfoSOARequestType();
            request.setDriverIds(driverId);
            DriverInfoSOAResponseType response = tmsTransportServiceProxy.queryDriver(request);
            boolean success = Optional.ofNullable(response).map(DriverInfoSOAResponseType::getResponseResult).map(ResponseResult::isSuccess).orElse(false);
            if (!success) {
                MetricsUtil.recordValue("query.driver.soa.error", 1);
                return null;
            }
            List<Driver> drivers = CommonConverter.INSTANCE.toDriver(response.getDriverList());
            return CollectionUtils.isEmpty(drivers) ? null : drivers.get(0);
        } catch (Exception e) {
            logger.error("query driver error!", e);
        }
        return null;
    }

    @Override
    public List<Driver> query(Set<String> driverIds) {
        try {
            //上云开关打开，则走新逻辑
            if (delayDspCommonQConfig.isSelfCloudSwitch()) {
                List<DriverInfo> driverInfoList = getDriverVOs(driverIds.stream().map(Long::valueOf).collect(Collectors.toSet()));
                return CommonConverter.INSTANCE.toDriver(driverInfoList);
            }
            DriverInfoSOARequestType request = new DriverInfoSOARequestType();
            request.setDriverIds(Joiner.on(",").join(driverIds));
            DriverInfoSOAResponseType response = tmsTransportServiceProxy.queryDriver(request);
            boolean success = Optional.ofNullable(response).map(DriverInfoSOAResponseType::getResponseResult).map(ResponseResult::isSuccess).orElse(false);
            if (!success) {
                MetricsUtil.recordValue("query.driver.soa.error", 1);
                return null;
            }
            List<Driver> drivers = CommonConverter.INSTANCE.toDriver(response.getDriverList());
            return drivers;
        } catch (Exception e) {
            logger.error("query driver error!", e);
        }
        return Collections.emptyList();
    }
    
    public List<DriverInfo> getDriverVOs(Set<Long> driverIds) {
        Map<String, List<Long>> drvUdlGroup = groupDrvUdl(driverIds);
        return getDriverList(drvUdlGroup);
    }
    
    private List<DriverInfo> getDriverList(Map<String, List<Long>> drvUdlGroup) {
        CommonThreadContext threadContext = CommonThreadContext.builder().threadPoolName("queryDriverCloudThreadPool").drvUdlGroup(drvUdlGroup).build();
        List<DriverInfoSOAResponseType> resultList = ThreadlUtil.getData(this::batchQueryDriverInfo, threadContext, getTimeOut());
        return Optional.ofNullable(resultList).orElse(Lists.newArrayList()).stream().map(DriverInfoSOAResponseType::getDriverList).filter(CollectionUtils::isNotEmpty).flatMap(Collection::stream).sorted(Comparator.comparing(DriverInfo::getDriverId)).collect(Collectors.toList());
    }
    
    private List<Future<DriverInfoSOAResponseType>> batchQueryDriverInfo(CommonThreadContext context) {
        List<Future<DriverInfoSOAResponseType>> futureTasks = Lists.newArrayList();
        context.getDrvUdlGroup().forEach((udl, driverIds) -> {
            //每个udl分组下的司机去处理
            List<List<Long>> partitions = Lists.partition(driverIds, 99);
            partitions.forEach(udlDriverIds -> {
                Future<DriverInfoSOAResponseType> future = addBatchGetDriverInfo(context.getThreadPoolName(), udlDriverIds, udl);
                futureTasks.add(future);
            });
        });
        return futureTasks;
    }
    
    public Future<DriverInfoSOAResponseType> addBatchGetDriverInfo(String threadName, List<Long> driverIds, String udl) {
        String traceId = Optional.ofNullable(LoggerContext.getCurrent()).map(LoggerContext::getGlobalTraceId).orElse(UUID.randomUUID().toString());
        return enableConfigThreadPool.get(threadName).submit(() -> {
            //设置UDL
            try {
                LoggerContext.newContext().withGlobalTraceId(traceId).init();
                logger.info("addBatchGetDriverInfo_udl", "udl:" + udl);
                //设置UDL的线程池尽量单用，因为线程是共用的，以免被上个UDL影响.
                transportServiceClientUtil.setDrvUdl(udl);
                DriverInfoSOARequestType request = new DriverInfoSOARequestType();
                request.setDriverIds(Joiner.on(",").join(driverIds));
                logger.info("QueryDriverByUdlLess99", "SOA queryDriver,request={}", JacksonSerializer.INSTANCE().serialize(request));
                DriverInfoSOAResponseType responseType = tmsTransportServiceProxy.queryDriver(request);
                logger.info("QueryDriverByUdlLess99", "SOA queryDriver,responseType={}", JacksonSerializer.INSTANCE().serialize(responseType));
                return responseType;
            } catch (Exception ex) {
                logger.error("QueryDriverByUdlLess99_error", ex);
                return null;
            }
        });
    }
    
    
    @Override
    public List<Driver> query(Integer cityId, Integer carTypeId,String taskType) {
        try {
            List<TransportSku> transportSkus = transportGateway.queryTransportSku(cityId, carTypeId);
            if (Objects.equals(taskType, DelayDspTaskType.SD.name())) {
                transportSkus = transportSkus.stream().filter(t -> Objects.equals(t.getShortTransportGroup(), YesOrNo.YES.getCode())).collect(Collectors.toList());
            }
            if (CollectionUtils.isEmpty(transportSkus)) {
                return Lists.newArrayList();
            }
            List<Long> transportIds = transportSkus.stream()
                    .filter(ts -> Objects.nonNull(ts.getTransportGroupId()))
                    .map(TransportSku::getTransportGroupId)
                    .collect(Collectors.toList());
            return query(transportIds);
        } catch (Exception e) {
            logger.error("query driver error!", e);
        }
        return Lists.newArrayList();
    }

    @Override
    public List<Driver> query(List<Long> transportGroupIds) {
        List<Long> driverIds = queryDriverIdByTransportGroups(transportGroupIds);
        if (CollectionUtils.isEmpty(driverIds)) {
            return Lists.newArrayList();
        }
        List<Driver> result = Lists.newArrayList();
        List<List<Long>> partition = Lists.partition(driverIds, 99);
        for (List<Long> ids : partition) {
            try {
                Set<String> list = ids.stream().map(String::valueOf).collect(Collectors.toSet());
                List<Driver> drivers = query(list);
                if (CollectionUtils.isNotEmpty(drivers)) {
                    result.addAll(drivers);
                }
            } catch (Exception e) {
                logger.error("query driver error!", e);
            }
        }
        return result;
    }

    private List<Long> queryDriverIdByTransportGroups(List<Long> transportGroupIds) {
        QueryDrvIdByTransportGroupsRequestType requestType = new QueryDrvIdByTransportGroupsRequestType();
        requestType.setTransportGroupIdList(transportGroupIds);
        QueryDrvIdByTransportGroupsResponseType responseType = tmsTransportServiceProxy.queryDrvIdByTransportGroups(requestType);
       return Optional.ofNullable(responseType)
               .map(QueryDrvIdByTransportGroupsResponseType::getDrvIdList)
               .orElse(Collections.emptyList());
    }

    @Override
    public List<DriverLeave> queryLeave(List<String> driverIds) {
        try {
            //自营上云开关
            if (delayDspCommonQConfig.isSelfCloudSwitch()) {
                return getDriverLeaves(driverIds);
            }
            QueryDrvLeaveDetailForDspSOARequestType request = new QueryDrvLeaveDetailForDspSOARequestType();
            request.setDrvIds(Joiner.on(",").join(driverIds));
            QueryDrvLeaveDetailForDspSOAResponseType response = tmsTransportServiceProxy.queryDrvLeaveDetailForDsp(request);
            boolean success = Optional.ofNullable(response).map(QueryDrvLeaveDetailForDspSOAResponseType::getResponseResult).map(ResponseResult::isSuccess).orElse(false);
            if (!success) {
                response = tmsTransportServiceProxy.queryDrvLeaveDetailForDsp(request);
                success = Optional.ofNullable(response).map(QueryDrvLeaveDetailForDspSOAResponseType::getResponseResult).map(ResponseResult::isSuccess).orElse(false);
                if (!success) {
                    throw new BizException("query driver leave error!");
                }
            }
            return CommonConverter.INSTANCE.toDriverLeave(response.getData());
        } catch (Exception e) {
            MetricsUtil.recordValue("query.driver.leave.soa.error", 1);
            logger.error("query driver leave error!", e);
        }
        return null;
    }
    
    private List<DriverLeave> getDriverLeaves(List<String> driverIds) {
        Map<String, List<Long>> drvUdlGroup = groupDrvUdl(driverIds.stream().map(Long::valueOf).collect(Collectors.toSet()));
        CommonThreadContext threadContext = CommonThreadContext.builder().drvUdlGroup(drvUdlGroup).threadPoolName("queryDriverLeaveDetailThreadPool").build();
        List<List<DriverLeave>> list = ThreadlUtil.getData(this::batchQueryDrvLeaveDetails, threadContext, getTimeOut());
        return Optional.ofNullable(list).orElse(Lists.newArrayList()).stream().filter(CollectionUtils::isNotEmpty).flatMap(Collection::stream).collect(Collectors.toList());
    }
    
    private List<Future<List<DriverLeave>>> batchQueryDrvLeaveDetails(CommonThreadContext context) {
        List<Future<List<DriverLeave>>> futures = Lists.newArrayList();
        context.getDrvUdlGroup().forEach((udl, driverIds) -> {
            Future<List<DriverLeave>> future = addBatchQueryDrvLeaves(context.getThreadPoolName(), udl, driverIds);
            futures.add(future);
        });
        return futures;
    }
    
    
    private Future<List<DriverLeave>> addBatchQueryDrvLeaves(String threadPoolName, String udl, List<Long> driverIds) {
        String traceId = Optional.ofNullable(LoggerContext.getCurrent()).map(LoggerContext::getGlobalTraceId).orElse(UUID.randomUUID().toString());
        return enableConfigThreadPool.get(threadPoolName).submit(() -> {
            try {
                LoggerContext.newContext().withGlobalTraceId(traceId).init();
                logger.info("addBatchQueryDrvLeaves_udl", "udl:" + udl + ",driverIds:" + JsonUtil.toJson(driverIds));
                //设置UDL的线程池尽量单用，因为线程是共用的，以免被上个UDL影响.
                transportServiceClientUtil.setDrvUdl(udl);
                QueryDrvLeaveDetailForDspSOARequestType requestType = new QueryDrvLeaveDetailForDspSOARequestType();
                requestType.setDrvIds(Joiner.on(",").join(driverIds));
                QueryDrvLeaveDetailForDspSOAResponseType responseType = tmsTransportServiceProxy.queryDrvLeaveDetailForDsp(requestType);
                return CommonConverter.INSTANCE.toDriverLeave(Optional.ofNullable(responseType.getData()).orElse(Lists.newArrayList()));
            } catch (Exception ex) {
                logger.error("add_batchQueryDrvLeaves_error", "udl:" + udl + ",driverIds:" + JsonUtil.toJson(driverIds), ex, Maps.newHashMap());
                return Lists.newArrayList();
            }
        });
    }
    
    @Override
    public List<DriverScore> queryDriverScore(Set<String> driverIds) {
        try {
            return driverScoreServiceProxy.queryDriverScore(driverIds);
        } catch (Exception e) {
            logger.error("query driver score error!", e);
        }
        return Lists.newArrayList();
    }
    
    public Map<String, List<Long>> groupDrvUdl(Set<Long> drvIdList) {
        Map<Long, DriverUdlVO> drvUdlMap = getDrvUdlMap(drvIdList);
        return groupDrvUdl(drvUdlMap);
    }
    
    public Map<String, List<Long>> groupDrvUdl(Map<Long, DriverUdlVO> map) {
        List<Long> sgpDrvIds = Lists.newArrayList();
        List<Long> shaDrvIds = Lists.newArrayList();
        for (Map.Entry<Long,DriverUdlVO> entry : map.entrySet()) {
            String drvUdl = Optional.ofNullable(entry.getValue()).map(DriverUdlVO::getUdl).orElse(null);
            if (UdlEnum.isSgp(drvUdl)) {
                sgpDrvIds.add(entry.getKey());
            } else {
                shaDrvIds.add(entry.getKey());
            }
        }
        Map<String, List<Long>> result = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(shaDrvIds)) {
            result.put(UdlEnum.SHA.getValue(), shaDrvIds);
        }
        if (CollectionUtils.isNotEmpty(sgpDrvIds)) {
            result.put(UdlEnum.SGP.getValue(), sgpDrvIds);
        }
        logger.info("groupDrvUdl_result", "result:" + JsonUtil.toJson(result));
        return result;
    }
    
    
    public Map<Long, DriverUdlVO> getDrvUdlMap(Set<Long> drvIdList) {
        //先查司机的UDL, 下游服务要求司机id列表最多不超过100个
        CommonThreadContext threadContext = CommonThreadContext.builder().threadPoolName("queryDriverUdlThreadPool").driverIds(drvIdList).build();
        List<List<DriverUDL>> driverUDLs = ThreadlUtil.getData(this::batchQueryDrvUdls, threadContext, getTimeOut());
        //转换成map
        List<DriverUDL> totalDriverUdls = driverUDLs.stream().filter(CollectionUtils::isNotEmpty).flatMap(Collection::stream).collect(Collectors.toList());
        //如果没有对应的司机udl数据，赋值兜底值
        setDefaultDriverUdls(drvIdList, totalDriverUdls);
        return totalDriverUdls.stream().collect(Collectors.toMap(t -> Long.valueOf(t.getDriverid()), this::convert));
    }
    
    private void setDefaultDriverUdls(Set<Long> totalDrvIdList, List<DriverUDL> partDriverUdls) {
        Set<Long> partQueriedDrvIds = partDriverUdls.stream().map(t -> Long.valueOf(t.getDriverid())).collect(Collectors.toSet());
        List<Long> loseDrvIds = totalDrvIdList.stream().filter(t -> !partQueriedDrvIds.contains(t)).collect(Collectors.toList());
        List<DriverUDL> driverUDLS = loseDrvIds.stream().map(drvId -> initDriverUdlVO(String.valueOf(drvId))).collect(Collectors.toList());
        partDriverUdls.addAll(driverUDLS);
    }
    
    public DriverUdlVO convert(DriverUDL driverUDL) {
        DriverUdlVO udlVO = new DriverUdlVO();
        udlVO.setDriverid(driverUDL.getDriverid());
        udlVO.setUdl(driverUDL.getUdl());
        udlVO.setUid(driverUDL.getUid());
        udlVO.setOversea(driverUDL.isIsOversea());
        return udlVO;
    }
    
    private List<Future<List<DriverUDL>>> batchQueryDrvUdls(CommonThreadContext context) {
        List<List<Long>> partitions = Lists.partition(Lists.newArrayList(context.getDriverIds()), 100);
        List<Future<List<DriverUDL>>> futureTasks = Lists.newArrayListWithCapacity(partitions.size());
        partitions.forEach(partition -> {
            String traceId = Optional.ofNullable(LoggerContext.getCurrent()).map(LoggerContext::getGlobalTraceId).orElse(UUID.randomUUID().toString());
            Future<List<DriverUDL>> future = enableConfigThreadPool.get(context.getThreadPoolName()).submit(() -> {
                List<String> drvIds = partition.stream().map(String::valueOf).collect(Collectors.toList());
                LoggerContext.newContext().withGlobalTraceId(traceId).init();
                return getDrvUdl(drvIds);
            });
            futureTasks.add(future);
        });
        return futureTasks;
    }
    
    
    public List<DriverUDL> getDrvUdl(List<String> drvIds) {
        try {
            QueryUDLByDriverIdRequestType request = new QueryUDLByDriverIdRequestType();
            request.setDriverIds(drvIds);
            QueryUDLByDriverIdResponseType responseType = driverDomainServiceProxy.queryUDLByDriverId(request);
            if (!Objects.equals(responseType.getResponseResult().isSuccess(), Boolean.TRUE)) {
                logger.warn("query_drv_udl", "query drv udl is failed.");
                throw new BizException(responseType.getResponseResult().getReturnMessage());
            }
            if (CollectionUtils.isEmpty(responseType.getDriverUdL())) {
                throw new BizException("query drvUdl is empty");
            }
            //默认兜底
            setDefaultDriverUdls(drvIds.stream().map(Long::valueOf).collect(Collectors.toSet()), responseType.getDriverUdL());
            return responseType.getDriverUdL();
        } catch(Exception ex) {
            logger.error("query_drv_udl_error", ex);
            return getDefaultDriverUdls(drvIds);
        }
    }
    
    private List<DriverUDL> getDefaultDriverUdls(List<String> driverIds) {
        return driverIds.stream().map(this::initDriverUdlVO).collect(Collectors.toList());
    }
    
    public DriverUDL initDriverUdlVO(String driverId) {
        DriverUDL driverUdl = new DriverUDL();
        driverUdl.setDriverid(driverId);
        driverUdl.setUdl(UdlEnum.SHA.getValue());
        driverUdl.setIsOversea(Boolean.FALSE);
        return driverUdl;
    }
    
    
    public Integer getTimeOut() {
        Integer queryDriverTimeOut = 500;
        try {
            queryDriverTimeOut = delayDspCommonQConfig.getQueryDriverTimeOut();
        } catch (Exception ex) {
            logger.error("queryDriverTimeOut_error", ex);
        }
        return queryDriverTimeOut;
    }
}
