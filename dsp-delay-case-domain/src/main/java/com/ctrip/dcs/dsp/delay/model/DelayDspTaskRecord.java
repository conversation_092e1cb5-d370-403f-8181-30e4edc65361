package com.ctrip.dcs.dsp.delay.model;

import com.ctrip.dcs.dsp.delay.enums.OrderSource;
import com.ctrip.dcs.dsp.delay.enums.OrderTakenCode;
import com.ctrip.dcs.dsp.delay.enums.YesOrNo;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class DelayDspTaskRecord {

    private Long id;

    private Long taskId;

    private String orderId;

    private String driverId;

    private Double driverScore;

    private Integer orderSeq;

    private Integer takenCode;

    private Integer matchSuccess;

    private Double emptyDistance;

    private Double emptyDuration;

    private Integer isOut;

    private Integer preOut;

    private String driverOrderId;

    private Long matchRecordId;





    private String orderSource = OrderSource.QUNAR.name();

    public DelayDspTaskRecord() {
    }

    public DelayDspTaskRecord(Long taskId, String orderId, Integer out) {
        this.taskId = taskId;
        this.orderId = orderId;
        this.takenCode = OrderTakenCode.SUCCESS.getCode();
        this.matchSuccess = YesOrNo.NO.getCode();
        this.driverId = StringUtils.EMPTY;
        this.driverScore = NumberUtils.DOUBLE_ZERO;
        this.emptyDistance = NumberUtils.DOUBLE_ZERO;
        this.emptyDuration = NumberUtils.DOUBLE_ZERO;
        this.orderSeq = NumberUtils.INTEGER_ZERO;
        this.isOut = out;
    }

    public DelayDspTaskRecord(Long taskId, String orderId, Integer out, String orderSource) {
        this.taskId = taskId;
        this.orderId = orderId;
        this.takenCode = OrderTakenCode.SUCCESS.getCode();
        this.matchSuccess = YesOrNo.NO.getCode();
        this.driverId = StringUtils.EMPTY;
        this.driverScore = NumberUtils.DOUBLE_ZERO;
        this.emptyDistance = NumberUtils.DOUBLE_ZERO;
        this.emptyDuration = NumberUtils.DOUBLE_ZERO;
        this.orderSeq = NumberUtils.INTEGER_ZERO;
        this.isOut = out;
        this.orderSource = orderSource;
    }

    public DelayDspTaskRecord(Long taskId, String orderId, String driverId, Double driverScore, Integer orderSeq, Double emptyDistance, Double emptyDuration, Integer out) {
        this.taskId = taskId;
        this.orderId = orderId;
        this.takenCode = OrderTakenCode.SUCCESS.getCode();
        this.matchSuccess = YesOrNo.YES.getCode();
        this.driverId = driverId;
        this.driverScore = driverScore;
        this.emptyDistance = emptyDistance;
        this.emptyDuration = emptyDuration;
        this.orderSeq = orderSeq;
        this.isOut = out;
    }

    public DelayDspTaskRecord(Long taskId, String orderId, String driverId, Double driverScore, Integer orderSeq, Double emptyDistance, Double emptyDuration, Integer out, String driverOrderId, String orderSource) {
        this.taskId = taskId;
        this.orderId = orderId;
        this.takenCode = OrderTakenCode.SUCCESS.getCode();
        this.matchSuccess = YesOrNo.YES.getCode();
        this.driverId = driverId;
        this.driverScore = driverScore;
        this.emptyDistance = emptyDistance;
        this.emptyDuration = emptyDuration;
        this.orderSeq = orderSeq;
        this.isOut = out;
        this.driverOrderId = driverOrderId;
        this.orderSource = orderSource;
    }
}
