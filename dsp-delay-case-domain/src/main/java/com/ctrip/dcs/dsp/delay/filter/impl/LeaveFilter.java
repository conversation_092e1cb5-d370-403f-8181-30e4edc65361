package com.ctrip.dcs.dsp.delay.filter.impl;

import com.ctrip.dcs.dsp.delay.gateway.DriverGateway;
import com.ctrip.dcs.dsp.delay.model.DelayDspTask;
import com.ctrip.dcs.dsp.delay.model.Driver;
import com.ctrip.dcs.dsp.delay.model.DriverLeave;
import com.ctrip.dcs.dsp.delay.util.DateUtil;
import com.ctrip.dcs.dsp.delay.util.JsonUtil;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component("leaveFilter")
public class LeaveFilter extends CommonFilter {

    private static final Logger logger = LoggerFactory.getLogger(LeaveFilter.class);

    @Autowired
    private DriverGateway driverGateway;

    @Override
    public List<Driver> doFilter(List<Driver> drivers, DelayDspTask task) {
        List<String> driverIds = drivers.stream().map(Driver::getDriverId).collect(Collectors.toList());
        List<DriverLeave> leaves = driverGateway.queryLeave(driverIds);
        if (leaves == null) {
            logger.info("query driver leave return null! driver id :{}", JsonUtil.toJson(driverIds));
            return Lists.newArrayList();
        }
        Map<String, List<DriverLeave>> map = transfer(leaves);
        return drivers.stream()
                .filter(driver -> {
                    if (!map.containsKey(driver.getDriverId())) {
                        return true;
                    }
                    List<DriverLeave> list = map.get(driver.getDriverId());
                    return notOverLap(task, list);
                })
                .collect(Collectors.toList());
    }

    @Override
    String reason() {
        return "";
    }

    @Override
    boolean filter(Driver driver, DelayDspTask task) {
        return true;
    }

    private Map<String /*driverId*/, List<DriverLeave>> transfer(List<DriverLeave> leaves) {
        Map<String, List<DriverLeave>> map = Maps.newHashMap();
        for (DriverLeave leave : leaves) {
            String driverId = leave.getDrvId().toString();
            if (!map.containsKey(driverId)) {
                map.put(driverId, Lists.newArrayList());
            }
            map.get(driverId).add(leave);
        }
        return map;
    }

    private boolean notOverLap(DelayDspTask task, List<DriverLeave> list) {
        for (DriverLeave leave : list) {
            Date begin = DateUtil.parseDate(leave.getLeaveBeginTime());
            Date end = DateUtil.parseDate(leave.getLeaveEndTime());
            if (!DateUtil.notOverlap(begin, end, task.getBeginTime(), task.getEndTime())) {
                logger.info("filter", "driver id: {}, filter reason: {}", leave.getDrvId(), "driver leave");
                return false;
            }
        }
        return true;
    }

}
