package com.ctrip.dcs.dsp.delay.infrastructure.repository

import com.ctrip.dcs.dsp.delay.infrastructure.mapper.DelayDspTaskRecordMapper
import com.ctrip.dcs.dsp.delay.infrastructure.mapper.ck.ClickHouseAdapter
import com.ctrip.dcs.dsp.delay.model.DelayDspTaskRecord
import com.ctrip.dcs.dsp.delay.model.InsertPoolReasonRecord
import org.junit.Assert
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 * @since 2024/8/19 21:11
 */
class DelayDspTaskRepositoryImplTest extends Specification {

    def delayDspTaskRecordMapper = Mock(DelayDspTaskRecordMapper)

    def testObj = new DelayDspTaskRepositoryImpl()

    def setup() {
        testObj.delayDspTaskRecordMapper = delayDspTaskRecordMapper
    }

    @Unroll
    def "test QueryRecordByTaskId"() {
        given:
        DelayDspTaskRecord record = new DelayDspTaskRecord()
        delayDspTaskRecordMapper.queryByTaskId(_, _) >> [record]
        when:
        List<DelayDspTaskRecord> result = testObj.queryRecordByTaskId(1L, 1)

        then:
        result.size() == 1
    }
}
