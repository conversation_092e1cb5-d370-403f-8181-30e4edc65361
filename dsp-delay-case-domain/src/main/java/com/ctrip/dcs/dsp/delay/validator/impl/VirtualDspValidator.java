package com.ctrip.dcs.dsp.delay.validator.impl;

import com.ctrip.dcs.dsp.delay.cache.DistributedCache;
import com.ctrip.dcs.dsp.delay.carconfig.CarConfig;
import com.ctrip.dcs.dsp.delay.carconfig.CarConfigCode;
import com.ctrip.dcs.dsp.delay.carconfig.key.VirtualDspTakenFailLimitKey;
import com.ctrip.dcs.dsp.delay.carconfig.value.InventoryValidatorCityValue;
import com.ctrip.dcs.dsp.delay.carconfig.value.VirtualDspTakenFailLimitValue;
import com.ctrip.dcs.dsp.delay.consts.CommonConstant;
import com.ctrip.dcs.dsp.delay.enums.YesOrNo;
import com.ctrip.dcs.dsp.delay.gateway.CarConfigGateway;
import com.ctrip.dcs.dsp.delay.model.SupplyOrder;
import com.ctrip.dcs.dsp.delay.qconfig.DelayDspCommonQConfig;
import com.ctrip.dcs.dsp.delay.qconfig.InventoryValidatorCityValueConfig;
import com.ctrip.dcs.dsp.delay.qconfig.VirtualDspTakenFailLimitConfig;
import com.ctrip.dcs.dsp.delay.qconfig.key.VirtualDspTakenFailLimitKeyVO;
import com.ctrip.dcs.dsp.delay.qconfig.value.VirtualDspTakenFailLimitValueVO;
import com.ctrip.dcs.dsp.delay.util.DateUtil;
import com.ctrip.dcs.dsp.delay.util.JsonUtil;
import com.ctrip.dcs.dsp.delay.validator.Validator;
import com.ctrip.dcs.dsp.delay.validator.ValidatorCode;
import com.ctrip.dcs.dsp.delay.validator.ValidatorDTO;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.codehaus.jackson.type.TypeReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Component("virtualDspValidator")
public class VirtualDspValidator implements Validator {

    private static final Logger logger = LoggerFactory.getLogger(VirtualDspValidator.class);

    @Autowired
    private CarConfigGateway carConfigGateway;

    @Autowired
    private DistributedCache distributedCache;

    @Autowired
    private DelayDspCommonQConfig delayDspCommonQConfig;
    @Autowired
    private InventoryValidatorCityValueConfig inventoryValidatorCityValueConfig;

    @Autowired
    private VirtualDspTakenFailLimitConfig virtualDspTakenFailLimitConfig;

    @Override
    public ValidatorDTO validate(SupplyOrder order) {

        logger.info("VirtualDspValidator_SupplyOrder", JsonUtil.toJson(order));
        boolean res = inventoryValidatorCityValueConfig.getValue(order.getCityId());
        logger.info("VirtualDspValidator_getValue", JsonUtil.toJson(res));
        if(res){
            return new ValidatorDTO(ValidatorCode.OK);
        }
        if (Objects.equals(delayDspCommonQConfig.getVirtualDspSwitch(), YesOrNo.NO.getCode())) {
            return new ValidatorDTO(ValidatorCode.OK);
        }
        String key = toKey(order.getCityId(), order.getCarTypeId(), order.getSysExpectBookTime());
        Long count = distributedCache.scard(key);

        List<VirtualDspTakenFailLimitValueVO> values = virtualDspTakenFailLimitConfig.get(Lists.newArrayList(
                new VirtualDspTakenFailLimitKeyVO(order.getCityId(), order.getCarTypeId()),
                new VirtualDspTakenFailLimitKeyVO(order.getCityId(), VirtualDspTakenFailLimitKey.ALL_CAR_TYPE),
                new VirtualDspTakenFailLimitKeyVO(VirtualDspTakenFailLimitKey.ALL_CITY_NUMBER, order.getCarTypeId()),
                new VirtualDspTakenFailLimitKeyVO(VirtualDspTakenFailLimitKey.ALL_CITY_NUMBER, VirtualDspTakenFailLimitKey.ALL_CAR_TYPE)
        ));

        for (VirtualDspTakenFailLimitValueVO value : values) {
            if (value.match(order.getSysExpectBookTime())) {
                logger.info("VirtualDspValidator", "count is {}, value is {}", count, JsonUtil.toJson(value));
                return count < value.getLimitedValue() ? new ValidatorDTO(ValidatorCode.OK) : new ValidatorDTO(ValidatorCode.VIRTUAL_DSP_LIMIT);
            }
        }
        return new ValidatorDTO(ValidatorCode.OK);
    }

    public static String toKey(Integer cityId, Integer carTypeId, Date date) {
        return CommonConstant.APP_ID + CommonConstant.PLACEHOLDER + CommonConstant.VIRTUAL_DSP_NOT_TAKEN_PREFIX + cityId
                + CommonConstant.PLACEHOLDER + carTypeId + CommonConstant.PLACEHOLDER + DateUtil.formatDate(date, DateUtil.HOUR_FMT);
    }
}
