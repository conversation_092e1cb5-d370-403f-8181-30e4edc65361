package com.ctrip.dcs.dsp.delay.infrastructure.redis

import com.ctrip.igt.framework.common.clogging.Logger
import credis.java.client.CacheProvider
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

/**
 * <AUTHOR>
 */
class RedisProviderProxyTest extends Specification {
    @Mock
    Logger logger
    @Mock
    CacheProvider cacheProvider
    @InjectMocks
    RedisProviderProxy redisProviderProxy

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test try Acquire"() {
        given:
        when(cacheProvider.setnx(anyString(), anyString())).thenReturn(true).thenReturn(false)
        when:
        boolean result1 = redisProviderProxy.tryAcquire("key", "value", 0l)
        boolean result2 = redisProviderProxy.tryAcquire("key", "value", 0l)

        then:
        result1
        !result2
    }

    def "test release"() {
        when:
        redisProviderProxy.release("key")

        then:
        true//todo - validate something
    }
}

//Generated with love by <PERSON>Me :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme