package com.ctrip.dcs.dsp.delay.model;

import com.ctrip.dcs.dsp.delay.enums.DelayDspTaskStatus;
import com.ctrip.dcs.dsp.delay.enums.MatchTaskStatus;
import com.ctrip.dcs.dsp.delay.util.DateUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.codehaus.jackson.annotate.JsonIgnore;

import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class DelayDspTask {

    private Long taskId;

    private String cityCode;

    private Integer cityId;

    private Integer carTypeId;

    private Integer taskStatus;

    private Integer taskSeq;

    private Date beginTime;

    private Date endTime;

    private Date executeTime;

    private Date executeTimeDeadline;

    private String taskType;

    private Integer taskVersion;

    private Date matchTime;

    private Integer matchStatus;

    private String configBeginTime;

    private String configEndTime;

    @JsonIgnore
    public boolean outOfTime(Date datetime) {
        return datetime.before(beginTime) || datetime.after(endTime);
    }

    /**
     * 临近最晚出池时间
     * @return
     */
    @JsonIgnore
    public boolean isNearDeadline() {
        double time = DateUtil.minutesDiff(this.getExecuteTime(), this.getExecuteTimeDeadline());
        return time <= 60D;
    }

    @JsonIgnore
    public boolean isUnExecuted() {
        return Objects.equals(this.taskStatus, DelayDspTaskStatus.UN_EXECUTED.getCode())
                || Objects.equals(this.taskStatus, DelayDspTaskStatus.MATCH_EXECUTION_COMPLETE.getCode())
                || (Objects.equals(this.taskStatus, DelayDspTaskStatus.MATCH_EXECUTING.getCode()));
    }

    @JsonIgnore
    public boolean isExecuted() {
        return Objects.equals(this.taskStatus, DelayDspTaskStatus.EXECUTING.getCode())
                || Objects.equals(this.taskStatus, DelayDspTaskStatus.EXECUTION_COMPLETE.getCode());
    }


    @JsonIgnore
    public boolean isMatchExecuting() {
        return Objects.equals(this.taskStatus, DelayDspTaskStatus.MATCH_EXECUTION_COMPLETE.getCode())
                || (Objects.equals(this.taskStatus, DelayDspTaskStatus.MATCH_EXECUTING.getCode()));
    }

}
