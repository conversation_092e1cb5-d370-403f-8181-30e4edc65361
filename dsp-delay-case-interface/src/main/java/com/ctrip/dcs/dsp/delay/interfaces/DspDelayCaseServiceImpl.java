package com.ctrip.dcs.dsp.delay.interfaces;

import com.ctrip.dcs.dsp.delay.service.interfaces.DspDelayCaseService;
import com.ctrip.dcs.dsp.delay.service.interfaces.message.*;
import com.ctrip.igt.framework.soa.server.executor.ServiceExecutors;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.common.types.CheckHealthRequestType;
import com.ctriposs.baiji.rpc.common.types.CheckHealthResponseType;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
public class DspDelayCaseServiceImpl implements DspDelayCaseService {

    @Override
    public CheckHealthResponseType checkHealth(CheckHealthRequestType checkHealthRequestType) throws Exception {
        return ServiceResponseUtils.getDefaultCheckHealthResponse();
    }

    /**
     * 延后派订单入职（老流程）
     * @param insertDelayDspOrderRequestType
     * @return
     * @throws Exception
     */
    @Override
    public InsertDelayDspOrderResponseType insertDelayDspOrder(InsertDelayDspOrderRequestType insertDelayDspOrderRequestType) throws Exception {
        return ServiceExecutors.execute(insertDelayDspOrderRequestType, InsertDelayDspOrderResponseType.class);
    }

    /**
     * 虚拟派失败数量
     * @param increaseVirtualDspTakenFailCountRequestType
     * @return
     * @throws Exception
     */
    @Override
    public IncreaseVirtualDspTakenFailCountResponseType increaseVirtualDspTakenFailCount(IncreaseVirtualDspTakenFailCountRequestType increaseVirtualDspTakenFailCountRequestType) throws Exception {
        return ServiceExecutors.execute(increaseVirtualDspTakenFailCountRequestType, IncreaseVirtualDspTakenFailCountResponseType.class);
    }

    /**
     * 查询延后派订单批次
     * @param queryDelayDspOrderBatchRequestType
     * @return
     * @throws Exception
     */
    @Override
    public QueryDelayDspOrderBatchResponseType queryDelayDspOrderBatch(QueryDelayDspOrderBatchRequestType queryDelayDspOrderBatchRequestType) throws Exception {
        return ServiceExecutors.execute(queryDelayDspOrderBatchRequestType, QueryDelayDspOrderBatchResponseType.class);
    }

    /**
     * 延后派订单入池（新流程）
     * @param insertDelayDspPoolRequestType
     * @return
     * @throws Exception
     */
    @Override
    public InsertDelayDspPoolResponseType insertDelayDspPool(InsertDelayDspPoolRequestType insertDelayDspPoolRequestType) throws Exception {
        return ServiceExecutors.execute(insertDelayDspPoolRequestType, InsertDelayDspPoolResponseType.class);
    }

    /**
     * 执行延后派任务
     * @param executeDelayDspTaskRequestType
     * @return
     * @throws Exception
     */
    @Override
    public ExecuteDelayDspTaskResponseType executeDelayDspTask(ExecuteDelayDspTaskRequestType executeDelayDspTaskRequestType) throws Exception {
        return ServiceExecutors.execute(executeDelayDspTaskRequestType, ExecuteDelayDspTaskResponseType.class);
    }

    /**
     * 司机是否正在参与延后派匹配
     * @param isDriverMatchingRequestType
     * @return
     * @throws Exception
     */
    @Override
    public QueryIsDriverMatchingResponseType queryIsDriverMatching(QueryIsDriverMatchingRequestType isDriverMatchingRequestType) throws Exception {
        return ServiceExecutors.execute(isDriverMatchingRequestType, QueryIsDriverMatchingResponseType.class);
    }

    /**
     * 查询延后派订单
     * @param queryDelayDspOrderRequestType
     * @return
     * @throws Exception
     */
    @Override
    public QueryDelayDspOrderResponseType queryDelayDspOrder(QueryDelayDspOrderRequestType queryDelayDspOrderRequestType) throws Exception {
        return ServiceExecutors.execute(queryDelayDspOrderRequestType, QueryDelayDspOrderResponseType.class);
    }

    /**
     * 查询延后派配置
     * @param requestType
     * @return
     * @throws Exception
     */
    @Override
    public QueryDelayDspConfigResInfoResponseType queryDelayDspConfigList(QueryDelayDspConfigInfoRequestType requestType) throws Exception {
        return ServiceExecutors.execute(requestType, QueryDelayDspConfigResInfoResponseType.class);
    }
    
    /**
     * 校验特殊日子
     * @param requestType
     * @return
     * @throws Exception
     */
    @Override
    public CheckSpecialDateResponseType checkSpecialDate(CheckSpecialDateRequestType requestType) throws Exception {
        return ServiceExecutors.execute(requestType, CheckSpecialDateResponseType.class);
    }

    @Override
    public UpdateTaskInfoResponseType updateTaskInfo(UpdateTaskInfoRequestType requestType) throws Exception {
        return ServiceExecutors.execute(requestType, UpdateTaskInfoResponseType.class);
    }

    @Override
    public PreOutPoolResponseType preOutPool(PreOutPoolRequestType preOutPoolRequestType) throws Exception {
        return ServiceExecutors.execute(preOutPoolRequestType, PreOutPoolResponseType.class);
    }
}
