package com.ctrip.dcs.dsp.delay.mq;

import java.util.Map;

/**
 * <AUTHOR>
 */
public interface MessageProducer {

    /**
     * 发送消息
     * @param subject
     * @param data
     */
    void sendMessage(String subject, Map<String, Object> data);

    /**
     * 发送延迟消息
     * @param subject
     * @param data
     * @param delay 毫秒
     */
    void sendDelayMessage(String subject, Map<String, Object> data, long delay);

}
