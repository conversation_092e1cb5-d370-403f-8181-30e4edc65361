package com.ctrip.dcs.dsp.delay.carconfig.value

import com.ctrip.dcs.dsp.delay.util.DateUtil
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 */
class VirtualDspTakenFailLimitValueSpec extends Specification {

    def value = new VirtualDspTakenFailLimitValue()

    @Unroll
    def "match"() {

        given: "Mock数据"
        value.setStartTime(startTime)
        value.setEndTime(endTime)

        when: "执行校验方法"
        def res = value.match(date)

        then: "验证校验结果"
        res == ok

        where:
        date                                    | startTime | endTime || ok
        getDate("2022-05-07 12:00:00") | "08:00"   | "20:00" || true
        getDate("2022-05-07 07:00:00") | "08:00"   | "20:00" || false
        getDate("2022-05-07 21:00:00") | "08:00"   | "20:00" || false
        getDate("2022-05-07 21:00:00") | "20:00"   | "08:00" || true
        getDate("2022-05-07 19:00:00") | "20:00"   | "08:00" || false
        getDate("2022-05-07 09:00:00") | "20:00"   | "08:00" || false
    }

    def getDate(String datetime) {
        return DateUtil.parseDate(datetime)
    }
}
