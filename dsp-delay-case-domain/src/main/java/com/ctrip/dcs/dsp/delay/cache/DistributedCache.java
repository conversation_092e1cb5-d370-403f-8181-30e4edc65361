package com.ctrip.dcs.dsp.delay.cache;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface DistributedCache extends Cache {

    List<String> mget(List<String> keys);

    Boolean setex(String key, String value, int seconds);

    String mset(int seconds, String... keysvalues);

    Long incr(String key, long seconds);

    Boolean del(String key);

    Boolean exists(String key);

    Boolean expire(String key, long seconds);

    Long sadd(String key, long seconds, String... members);

    Long scard(String key);

}
