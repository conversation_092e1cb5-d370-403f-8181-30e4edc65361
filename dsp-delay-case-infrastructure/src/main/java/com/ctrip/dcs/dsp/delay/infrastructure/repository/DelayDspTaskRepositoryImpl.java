package com.ctrip.dcs.dsp.delay.infrastructure.repository;

import com.ctrip.dcs.dsp.delay.enums.OrderTakenCode;
import com.ctrip.dcs.dsp.delay.enums.OutReasonEnum;
import com.ctrip.dcs.dsp.delay.enums.YesOrNo;
import com.ctrip.dcs.dsp.delay.factory.ModelFactory;
import com.ctrip.dcs.dsp.delay.infrastructure.mapper.DelayDspMatchTaskRecordMapper;
import com.ctrip.dcs.dsp.delay.infrastructure.mapper.DelayDspTaskMapper;
import com.ctrip.dcs.dsp.delay.infrastructure.mapper.DelayDspTaskRecordMapper;
import com.ctrip.dcs.dsp.delay.model.DelayDspMatchTaskRecord;
import com.ctrip.dcs.dsp.delay.model.DelayDspTask;
import com.ctrip.dcs.dsp.delay.model.DelayDspTaskRecord;
import com.ctrip.dcs.dsp.delay.repository.DelayDspTaskRepository;
import com.ctrip.dcs.dsp.delay.util.JsonUtil;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Component
public class DelayDspTaskRepositoryImpl implements DelayDspTaskRepository {

    private static final Logger logger = LoggerFactory.getLogger(DelayDspTaskRepositoryImpl.class);


    @Autowired
    private DelayDspTaskMapper delayDspTaskMapper;

    @Autowired
    private DelayDspTaskRecordMapper delayDspTaskRecordMapper;
    @Autowired
    private DelayDspMatchTaskRecordMapper delayDspMatchTaskRecordMapper;

    @Override
    public DelayDspTask queryByTaskId(Long taskId) {
        return delayDspTaskMapper.queryByTaskId(taskId);
    }

    @Override
    public List<DelayDspTask> queryByTaskIds(Set<Long> taskIds) {
        if (CollectionUtils.isEmpty(taskIds)) {
            return Lists.newArrayList();
        }
        List<DelayDspTask> list = Lists.newArrayList();
        for (Long taskId : taskIds) {
            DelayDspTask task = queryByTaskId(taskId);
            if (Objects.nonNull(task)) {
                list.add(task);
            }
        }
        return list;
    }

    @Override
    public Long save(DelayDspTask task) {
        return delayDspTaskMapper.insert(task);
    }

    @Override
    public List<DelayDspTask> queryByTime(Date date) {
        return delayDspTaskMapper.queryByTime(date);
    }

    @Override
    public List<DelayDspTask> queryByMatchTime(Date date, Integer matchStatus,String taskType) {
        return delayDspTaskMapper.queryByMatchTime(date, matchStatus, taskType);
    }

    @Override
    public void update(Long taskId, Integer status) {
        delayDspTaskMapper.update(taskId, status);
    }

    @Override
    public void updateMatchInfo(Long taskId, Integer status, Integer taskVersion, Date matchTime,Integer matchStatus) {
        delayDspTaskMapper.updateMatchInfo(taskId, status, taskVersion, matchTime, matchStatus);
    }

    @Override
    public void updateVersion(Long taskId, Integer status, Integer taskVersion) {
        delayDspTaskMapper.updateVersion(taskId, status, taskVersion);
    }

    @Override
    public void updateOutPoolTime(Long taskId, Date outPoolTime) {
        delayDspTaskMapper.updateOutPoolTime(taskId, outPoolTime);

    }

    @Override
    public void save(List<DelayDspTaskRecord> records) {
        delayDspTaskRecordMapper.insert(records);
    }

    @Override
    @Transactional
    public void saveDelayDspTaskRecordAndUpdateMatchTaskRecord(List<DelayDspMatchTaskRecord> records, Integer outReason) {
        saveDelayDspTaskRecord(records);
        records.forEach(record -> {
            if(record == null || record.getId() == null){
                logger.error("DelayDspTaskRepositoryImpl_saveDelayDspTaskRecordAndUpdateMatchTaskRecord", JsonUtil.toJson(record));
                return;
            }
            DelayDspMatchTaskRecord matchTaskRecord = new DelayDspMatchTaskRecord();
            matchTaskRecord.setId(record.getId());
            matchTaskRecord.setOutReason(outReason);
            if(outReason.equals(OutReasonEnum.DRIVE_REACH_STANDARD.getCode())){
                matchTaskRecord.setDriverOrderId(record.getDriverOrderId());
            }
            delayDspMatchTaskRecordMapper.update(matchTaskRecord);
        });
    }


    private void saveDelayDspTaskRecord(List<DelayDspMatchTaskRecord> details) {
        List<DelayDspTaskRecord> records = ModelFactory.buildDelayDspTaskRecordsByMatchRecord(details);
        if(CollectionUtils.isEmpty(records)){
            return;
        }
        this.save(records);
    }

    @Override
    public void update(DelayDspTaskRecord record) {
        delayDspTaskRecordMapper.update(record);
    }


    @Override
    public DelayDspTask query(DelayDspTask task) {
        return delayDspTaskMapper.query(task.getCityId(), task.getCarTypeId(), task.getBeginTime(), task.getEndTime(), task.getExecuteTime(), task.getExecuteTimeDeadline());
    }

    @Override
    public List<DelayDspTask> query(Integer cityId, Integer carTypeId, Date date) {
        return delayDspTaskMapper.batchQuery(cityId, carTypeId, date);
    }

    @Override
    public List<DelayDspTask> batchQueryForChangeOutPoolTime(String cityId, String begin, String end, Integer status, Integer matchStatus) {
        return delayDspTaskMapper.batchQueryForChangeOutPoolTime(cityId, begin, end, status, matchStatus);
    }

    @Override
    public void updateRecordMatchStatus(Integer isOut, Integer matchSuccess, Long recordId) {
        delayDspTaskRecordMapper.updateMatchStatus(isOut, matchSuccess, recordId);
    }

    @Override
    public void updateTaskInfo(DelayDspTask delayDspTask) {
        delayDspTaskMapper.updateTaskInfo(delayDspTask.getTaskId(), delayDspTask.getTaskStatus(),delayDspTask.getMatchStatus(),delayDspTask.getExecuteTime(),delayDspTask.getMatchTime());
    }

    @Override
    public List<DelayDspTaskRecord> queryRecordByTaskId(Long taskId, Integer preOut) {
        return delayDspTaskRecordMapper.queryByTaskId(taskId, preOut);
    }

    @Override
    @Transactional
    public void updateTakenCode(Integer takenCode, DelayDspTaskRecord record) {
        //回填出池接单结果
        updateDelayDspMatchTaskRecord(takenCode, record.getMatchRecordId());
        if (Objects.equals(takenCode, OrderTakenCode.SUCCESS.getCode())) {
            //回填出池接单结果
            updateDelayDspTaskRecord(record.getId(), takenCode);
        }else{
            //非提前出池的订单，如果接单失败，改派
            if(record.getPreOut().equals(YesOrNo.NO.getCode())){
                updateRecordMatchStatus(YesOrNo.NO.getCode(), YesOrNo.NO.getCode(), record.getId());
            }
        }
    }

    @Override
    public List<DelayDspTask> queryCountByDeadTime(Date date) {
        return delayDspTaskMapper.queryCountByDeadTime(date);
    }

    private void updateDelayDspMatchTaskRecord(Integer takenCode, Long matchRecordId) {
        if(matchRecordId == null){
            logger.info("DelayDspTaskRepositoryImpl_updateDelayDspMatchTaskRecord", JsonUtil.toJson(takenCode));
            return;
        }
        DelayDspMatchTaskRecord record = new DelayDspMatchTaskRecord();
        record.setId(matchRecordId);
        record.setTakenCode(takenCode);
        record.setOutTime(new Date());
        delayDspMatchTaskRecordMapper.update(record);
    }

    private void updateDelayDspTaskRecord(Long recordId, Integer takenCode) {
        DelayDspTaskRecord record = new DelayDspTaskRecord();
        record.setId(recordId);
        record.setTakenCode(takenCode);
        this.update(record);
    }
}
