package com.ctrip.dcs.dsp.delay.context;

import com.ctrip.dcs.dsp.delay.enums.DelayDspTaskType;
import com.ctrip.dcs.dsp.delay.exception.IllegalTaskTypeException;
import com.ctrip.dcs.dsp.delay.service.DispatchTaskService;
import com.google.common.collect.Maps;

import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 */
public class DispatcherManager {

    private static final Map<DelayDspTaskType, DispatchTaskService> context = Maps.newHashMap();

    public static void register(DelayDspTaskType type, DispatchTaskService service) {
        context.put(type, service);
    }

    public static DispatchTaskService get(DelayDspTaskType type) {
        DispatchTaskService service = context.get(type);
        if (Objects.isNull(service)) {
            throw new IllegalTaskTypeException("no dispatcher service. type:" + type.name());
        }
        return service;
    }
}
