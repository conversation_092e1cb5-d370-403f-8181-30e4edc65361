package com.ctrip.dcs.dsp.delay.service;

import com.ctrip.dcs.dsp.delay.model.DelayDspTask;
import com.ctrip.dcs.dsp.delay.model.DriverAggregation;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface DispatcherDriverService {

    /**
     * 司机参与匹配
     * @param task
     * @param drivers
     */
    void join(DelayDspTask task, List<DriverAggregation> drivers);

    /**
     * 司机结束匹配
     * @param drivers
     */
    void exit(List<DriverAggregation> drivers);

    /**
     * 司机是否正在参与匹配
     * @param driverId
     */
    Boolean isMatch(String driverId, String date);
}
