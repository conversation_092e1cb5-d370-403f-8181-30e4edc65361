package com.ctrip.dcs.dsp.delay.core;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.modules.junit4.PowerMockRunner;

/**
 * <AUTHOR>
 */
@RunWith(PowerMockRunner.class)
public class KMTest {

    @Test
    public void test() {
        int[][] graph = new int[][]{
                {3,0,4},
                {2,1,3},
                {0,0,5}
        };
        KM km = new KM(graph);
        int[] result = km.execute();
        Assert.assertEquals(result[0], 0);
        Assert.assertEquals(result[1], 1);
        Assert.assertEquals(result[2], 2);
    }

    @Test
    public void test1() {
        int[][] graph = new int[][]{
                {9,6,0,0},
                {0,3,9,0},
                {5,9,0,0},
                {0,0,2,0}
        };
        KM km = new KM(graph);
        int[] result = km.execute();
        Assert.assertEquals(result[0], 0);
        Assert.assertEquals(result[1], 2);
        Assert.assertEquals(result[2], 1);
        Assert.assertEquals(result[3], 3);
    }
}
