package com.ctrip.dcs.dsp.delay.infrastructure.repository;

import com.ctrip.dcs.dsp.delay.infrastructure.mapper.DelayDspOrderMapper;
import com.ctrip.dcs.dsp.delay.model.DelayDspOrder;
import com.ctrip.dcs.dsp.delay.repository.DelayDspOrderRepository;
import com.ctrip.dcs.dsp.delay.util.JsonUtil;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Component
public class DelayDspOrderRepositoryImpl implements DelayDspOrderRepository {

    private static final Logger logger = LoggerFactory.getLogger(DelayDspOrderRepositoryImpl.class);


    @Autowired
    private DelayDspOrderMapper delayDspOrderMapper;

    @Override
    public void save(DelayDspOrder order) {
        if(order.getDriverId() == null){
            order.setDriverId("");
        }
        logger.info("DelayDspOrderRepositoryImpl_saveOrder", JsonUtil.toJson(order));
        delayDspOrderMapper.insertOrUpdate(order);
    }

    @Override
    public DelayDspOrder queryByOrderId(String orderId) {
        return delayDspOrderMapper.queryByOrderId(orderId);
    }

    @Override
    public DelayDspOrder queryByMainOrderId(String mainOrderId) {
        return delayDspOrderMapper.queryByMainOrderId(mainOrderId);
    }

    @Override
    public DelayDspOrder queryDelayOrderByMainOrderId(String mainOrderId) {
        return delayDspOrderMapper.queryDelayOrderByMainOrderId(mainOrderId);
    }

    @Override
    public List<DelayDspOrder> queryByTaskId(Long taskId) {
        return delayDspOrderMapper.queryByTaskId(taskId);
    }

    @Override
    public List<DelayDspOrder> queryByTaskId(Long taskId, Integer isDelay) {
        return delayDspOrderMapper.queryByType(taskId, isDelay);
    }

    @Override
    public void update(DelayDspOrder order) {
        delayDspOrderMapper.update(order);
    }

    @Override
    public void updateForCancel(DelayDspOrder order) {
        delayDspOrderMapper.updateForCancel(order);
    }


    @Override
    public List<DelayDspOrder> queryByOrderIds(List<String> orderIds) {
        List<DelayDspOrder> result = Lists.newArrayListWithCapacity(orderIds.size());
        List<List<String>> partition = Lists.partition(orderIds, 200);
        for (List<String> list : partition) {
            List<DelayDspOrder> orders = delayDspOrderMapper.queryByOrderIds(list);
            result.addAll(orders);
        }
        return result;
    }

    @Override
    public List<DelayDspOrder> queryByOrderIdsWithTaskId(Long taskId, List<String> collect, Integer limitNum) {
        return delayDspOrderMapper.queryByOrderIdsWithTaskId(taskId, collect, limitNum);
    }

    @Override
    public List<DelayDspOrder> query(List<String> driverIds, Date beginTime, Date endTime) {
        return delayDspOrderMapper.queryByDrivers(driverIds, beginTime, endTime);
    }
}
