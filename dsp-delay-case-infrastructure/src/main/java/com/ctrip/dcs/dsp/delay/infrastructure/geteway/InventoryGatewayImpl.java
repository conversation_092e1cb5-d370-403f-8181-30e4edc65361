package com.ctrip.dcs.dsp.delay.infrastructure.geteway;

import com.ctrip.dcs.dsp.delay.gateway.InventoryGateway;
import com.ctrip.dcs.dsp.delay.infrastructure.soa.TransportInventoryServiceProxy;
import com.ctrip.dcs.dsp.delay.model.SupplyOrder;
import com.ctrip.dcs.dsp.delay.util.DateUtil;
import com.ctrip.dcs.dsp.delay.util.MetricsUtil;
import com.ctrip.dcs.self.transport.inventory.api.UseVirtualInventoryRequestType;
import com.ctrip.dcs.self.transport.inventory.api.UseVirtualInventoryResponseType;
import com.ctrip.igt.ResponseResult;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Component
public class InventoryGatewayImpl implements InventoryGateway {

    private static final Logger logger = LoggerFactory.getLogger(InventoryGatewayImpl.class);

    @Autowired
    private TransportInventoryServiceProxy transportInventoryServiceProxy;

    @Override
    public String checkInventoryConflict(List<String> driverIdList, SupplyOrder order) {
        int executeTimes = 0;
        UseVirtualInventoryRequestType useVirtualInventoryRequestType = new UseVirtualInventoryRequestType();
        useVirtualInventoryRequestType.setStartTime(DateUtil.formatDate(order.getSysExpectBookTime(), DateUtil.DATE_FMT));
        useVirtualInventoryRequestType.setEndTime(DateUtil.formatDate(order.getPredicServiceStopTime(), DateUtil.DATE_FMT));
        useVirtualInventoryRequestType.setDspOrderId(order.getOrderId());
        List<List<String>> listMsgPartition = Lists.partition(driverIdList, 100);
        for (List<String> driverIds : listMsgPartition) {
            try {
                executeTimes = executeTimes + 1;
                useVirtualInventoryRequestType.setDriverIds(driverIds);
                UseVirtualInventoryResponseType response = transportInventoryServiceProxy.useVirtualInventory(useVirtualInventoryRequestType);
                Boolean aBoolean = Optional.ofNullable(response).map(UseVirtualInventoryResponseType::getResponseResult).map(ResponseResult::isSuccess).orElse(false);
                if (aBoolean && StringUtils.isNotBlank(response.getDriverId())) {
                    MetricsUtil.gaugeValue("insert_pool_checkInventoryConflict_execute_count", executeTimes);
                    return response.getDriverId();
                }
            } catch (Exception e) {
                logger.error("InventoryGatewayImpl_checkInventoryConflict", e);
            }
        }
        MetricsUtil.gaugeValue("insert_pool_checkInventoryConflict_execute_count", executeTimes);
        return null;
    }
}
