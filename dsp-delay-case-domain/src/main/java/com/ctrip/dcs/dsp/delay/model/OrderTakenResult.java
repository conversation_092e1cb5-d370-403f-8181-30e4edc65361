package com.ctrip.dcs.dsp.delay.model;

import lombok.Getter;
import lombok.Setter;

import java.util.Objects;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class OrderTakenResult {

    private static final int NOT_CHECK_PASS = 3;

    private boolean result = false;
    private Integer code;
    private String des;
    private Integer notTakenCode;

    public Integer getResultCode() {
        return Objects.equals(NOT_CHECK_PASS, this.code) ? notTakenCode : code;
    }

}
