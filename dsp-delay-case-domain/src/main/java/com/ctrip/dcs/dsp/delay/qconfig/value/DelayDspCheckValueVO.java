package com.ctrip.dcs.dsp.delay.qconfig.value;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * Created by xing'xing'yu on 2023/8/25
 */
@Getter
public class DelayDspCheckValueVO implements CarConfigValueVO {

    private static final Splitter DEFAULT_SPLITTER = Splitter.on(',').trimResults().omitEmptyStrings();

    private String driverToOrderChecks;

    private String orderToOrderChecks;

    private String orderToTakenChecks;

    @JsonCreator
    public DelayDspCheckValueVO(@JsonProperty("driverToOrderChecks") String driverToOrderChecks, @JsonProperty("orderToOrderChecks") String orderToOrderChecks, @JsonProperty("orderToTakenChecks") String orderToTakenChecks) {
        this.driverToOrderChecks = driverToOrderChecks;
        this.orderToOrderChecks = orderToOrderChecks;
        this.orderToTakenChecks = orderToTakenChecks;
    }

    public List<String> getDriverToOrderCheckList() {
        if (StringUtils.isBlank(driverToOrderChecks)) {
            return Lists.newArrayList();
        }
        return DEFAULT_SPLITTER.splitToList(driverToOrderChecks);
    }

    public List<String> getOrderToOrderCheckList() {
        if (StringUtils.isBlank(orderToOrderChecks)) {
            return Lists.newArrayList();
        }
        return DEFAULT_SPLITTER.splitToList(orderToOrderChecks);
    }

    public List<String> getOrderToTakenCheckList() {
        if (StringUtils.isBlank(orderToTakenChecks)) {
            return Lists.newArrayList();
        }
        return DEFAULT_SPLITTER.splitToList(orderToTakenChecks);
    }
}
