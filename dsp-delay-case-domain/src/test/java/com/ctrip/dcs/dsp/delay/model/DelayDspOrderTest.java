package com.ctrip.dcs.dsp.delay.model;

import com.ctrip.dcs.dsp.delay.qconfig.DelayDspCommonQConfig;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.modules.junit4.PowerMockRunner;

/**
 * <AUTHOR>
 */
@RunWith(PowerMockRunner.class)
public class DelayDspOrderTest {

    @Mock
    private Route route;

    @Mock
    private DelayDspCommonQConfig config;

    @Test
    public void testNull() {
        DelayDspOrder order = new DelayDspOrder();
        order.setKiloLength(100D);
        order.setDriverOrderFee(100D);
        double v = order.calculateProfit(null);
        Assert.assertTrue(v < 0);
    }

    @Test
    public void testCalculateProfit() {
        PowerMockito.when(route.getDistance()).thenReturn(50D);
        PowerMockito.when(config.isOrderProfitCity(1)).thenReturn(false);
        DelayDspOrder order = new DelayDspOrder();
        order.setCityId(1);
        order.setKiloLength(50D);
        order.setDriverOrderFee(100D);
        double v1 = order.calculateProfit(route, config);
        Assert.assertEquals(v1, 50.0, 0.0);

        PowerMockito.when(route.getDistance()).thenReturn(50D);
        PowerMockito.when(config.isOrderProfitCity(1)).thenReturn(true);
        double v2 = order.calculateProfit(route, config);
        Assert.assertEquals(v2, 100.0, 0.0);
    }
}
