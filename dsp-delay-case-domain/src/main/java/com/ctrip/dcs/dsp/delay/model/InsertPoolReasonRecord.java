package com.ctrip.dcs.dsp.delay.model;

import com.google.common.collect.ImmutableMap;
import org.apache.commons.lang.StringUtils;

import java.util.Map;

/**
 * 延后派入池记录原因表
 * <AUTHOR>
 * @since 2024/8/15 15:02
 */
public class InsertPoolReasonRecord {
    
    /**
     * 主单号
     */
    private String userOrderId;
    
    /**
     * 供应商单号
     */
    private String dspOrderId;
    
    /**
     * 结果类型
     */
    private String reasonCode;
    
    /**
     * 开始时间
     */
    private String startTime;
    
    /**
     * 结束时间
     */
    private String endTime;
    
    
    public String getUserOrderId() {
        return userOrderId;
    }
    
    public void setUserOrderId(String userOrderId) {
        this.userOrderId = userOrderId;
    }
    
    public String getDspOrderId() {
        return dspOrderId;
    }
    
    public void setDspOrderId(String dspOrderId) {
        this.dspOrderId = dspOrderId;
    }
    
    public String getReasonCode() {
        return reasonCode;
    }
    
    public void setReasonCode(String reasonCode) {
        this.reasonCode = reasonCode;
    }
    
    public String getStartTime() {
        return startTime;
    }
    
    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }
    
    public String getEndTime() {
        return endTime;
    }
    
    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }
    
    public Map<String, String> toMap() {
        ImmutableMap.Builder<String, String> builder = ImmutableMap.builder();
        if (StringUtils.isNotEmpty(this.userOrderId)) {
            builder.put("user_order_id", this.getUserOrderId());
        }
        if (StringUtils.isNotEmpty(this.dspOrderId)) {
            builder.put("dsp_order_id", this.getDspOrderId());
        }
        if (StringUtils.isNotEmpty(this.reasonCode)) {
            builder.put("reason_code", this.getReasonCode());
        }
        if (StringUtils.isNotEmpty(this.startTime)) {
            builder.put("start_time", this.getStartTime());
        }
        if (StringUtils.isNotEmpty(this.endTime)) {
            builder.put("end_time", this.getEndTime());
        }
        return builder.build();
    }
}
