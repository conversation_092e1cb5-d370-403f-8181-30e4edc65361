package com.ctrip.dcs.dsp.delay.validator.impl;

import com.ctrip.dcs.dsp.delay.model.SupplyOrder;
import com.ctrip.dcs.dsp.delay.qconfig.DelayDspCommonQConfig;
import com.ctrip.dcs.dsp.delay.validator.Validator;
import com.ctrip.dcs.dsp.delay.validator.ValidatorCode;
import com.ctrip.dcs.dsp.delay.validator.ValidatorDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 */
@Component("userValidator")
public class UserValidator implements Validator {

    @Autowired
    private DelayDspCommonQConfig delayDspCommonQConfig;

    @Override
    public ValidatorDTO validate(SupplyOrder order) {
        List<String> whiteList = delayDspCommonQConfig.getUserWhiteList();
        return CollectionUtils.isEmpty(whiteList) || whiteList.contains(order.getUid()) ? new ValidatorDTO(ValidatorCode.OK) : new ValidatorDTO(ValidatorCode.UID);
    }
}
