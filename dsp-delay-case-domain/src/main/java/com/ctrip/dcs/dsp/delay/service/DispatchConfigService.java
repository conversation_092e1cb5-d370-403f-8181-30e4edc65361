package com.ctrip.dcs.dsp.delay.service;

import com.ctrip.dcs.dsp.delay.carconfig.value.DelayDspSubSkuValue;
import com.ctrip.dcs.dsp.delay.model.DispatcherConfig;
import com.ctrip.dcs.dsp.delay.model.DelayDspTask;
import com.ctrip.dcs.dsp.delay.model.SupplyOrder;


/**
 * <AUTHOR>
 */
public interface DispatchConfigService {

    DelayDspSubSkuValue matchDelayDspSubSkuConfig(SupplyOrder order);
    
    DelayDspSubSkuValue matchDelayDspSubSkuConfig(String cityCode);

}
