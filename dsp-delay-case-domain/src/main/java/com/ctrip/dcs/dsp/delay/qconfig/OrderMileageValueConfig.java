package com.ctrip.dcs.dsp.delay.qconfig;

import com.ctrip.dcs.dsp.delay.qconfig.key.CarConfigKeyVO;
import com.ctrip.dcs.dsp.delay.qconfig.key.OrderMileageConfigKeyVO;
import com.ctrip.dcs.dsp.delay.qconfig.value.OrderMileageConfigValueVO;
import org.springframework.stereotype.Component;
import qunar.tc.qconfig.client.spring.QConfig;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Component
public class OrderMileageValueConfig extends BaseCarConfig<OrderMileageConfigKeyVO,OrderMileageConfigValueVO> {

    private static final OrderMileageConfigValueVO DEFAULT = new OrderMileageConfigValueVO("0", "9999", "00:00", "23:59", "0", "0", "0");

    @QConfig("visualConfig_orderMileageValue_1.json")
    private HashMap<String, Config<OrderMileageConfigKeyVO, OrderMileageConfigValueVO>> context;


    @Override
    Map<String, Config<OrderMileageConfigKeyVO, OrderMileageConfigValueVO>> getContext() {
        return this.context;
    }

    public OrderMileageConfigValueVO getValue(Integer cityId, Integer carTypeId) {
        OrderMileageConfigValueVO value = get(new OrderMileageConfigKeyVO(cityId, carTypeId));
        if (Objects.isNull(value)) {
            value = get(new OrderMileageConfigKeyVO(cityId, CarConfigKeyVO.ALL_CAR_TYPE));
        }
        if (Objects.isNull(value)) {
            value = get(new OrderMileageConfigKeyVO(CarConfigKeyVO.ALL_CITY_ID, CarConfigKeyVO.ALL_CITY_ID));
        }
        return value == null ? DEFAULT : value;
    }
}
