package com.ctrip.dcs.dsp.delay.infrastructure.mapper;

import com.ctrip.dcs.dsp.delay.model.DelayDspMatchTaskRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface DelayDspMatchTaskRecordMapper {

    void insert(@Param("items") List<DelayDspMatchTaskRecord> records);

    void update(DelayDspMatchTaskRecord record);

    List<DelayDspMatchTaskRecord> query(@Param("taskId") Long taskId, @Param("taskVersion") Integer taskVersion);


}
