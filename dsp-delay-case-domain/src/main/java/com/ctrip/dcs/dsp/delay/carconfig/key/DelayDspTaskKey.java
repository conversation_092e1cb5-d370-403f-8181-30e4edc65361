package com.ctrip.dcs.dsp.delay.carconfig.key;

import com.ctrip.dcs.dsp.delay.carconfig.Key;
import com.ctrip.dcs.dsp.delay.consts.CommonConstant;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class DelayDspTaskKey implements Key {

    public  static final  Integer ALL_CAR_TYPE =0;

    private Integer cityId;

    private Integer carTypeId;

    @Override
    public String toKey() {
        return cityId + CommonConstant.PLACEHOLDER + carTypeId;
    }
}
