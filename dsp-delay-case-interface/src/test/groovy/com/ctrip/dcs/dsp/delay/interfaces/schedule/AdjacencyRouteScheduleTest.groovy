package com.ctrip.dcs.dsp.delay.interfaces.schedule

import com.ctrip.dcs.dsp.delay.lock.DistributedLock
import com.ctrip.dcs.dsp.delay.model.DelayDspOrder
import com.ctrip.dcs.dsp.delay.model.DelayDspTask
import com.ctrip.dcs.dsp.delay.mq.MessageProducer
import com.ctrip.dcs.dsp.delay.repository.DelayDspOrderRepository
import com.ctrip.dcs.dsp.delay.repository.DelayDspTaskRepository
import com.ctrip.igt.framework.common.clogging.Logger
import qunar.tc.schedule.MockParameter
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

/**
 * <AUTHOR>
 */
class AdjacencyRouteScheduleTest extends Specification {
    @Mock
    Logger logger
    @Mock
    DelayDspTaskRepository delayDspTaskRepository
    @Mock
    DelayDspOrderRepository delayDspOrderRepository
    @Mock
    MessageProducer messageProducer
    @Mock
    DistributedLock distributedLockService
    @Mock
    DelayDspTask delayDspTask
    @Mock
    DelayDspOrder delayDspOrder
    @InjectMocks
    AdjacencyRouteSchedule adjacencyRouteSchedule

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test execute"() {
        given:
        when(delayDspTaskRepository.queryByTime(any())).thenReturn([new DelayDspTask()])
        when(delayDspOrderRepository.queryByTaskId(anyLong())).thenReturn([delayDspOrder])

        when:
        adjacencyRouteSchedule.execute(new MockParameter())

        then:
        verify(messageProducer, times(0)).sendDelayMessage(anyString(), anyMap(), anyLong())
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme