package com.ctrip.dcs.dsp.delay.model;

import com.ctrip.dcs.dsp.delay.consts.CommonConstant;
import com.ctrip.dcs.dsp.delay.util.GeoHashUtil;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class Position {

    private Double fromLongitude;
    private Double fromLatitude;
    private String fromCoordsys;
    private String fromHash;

    private Double toLongitude;
    private Double toLatitude;
    private String toCoordsys;
    private String toHash;

    private Date departureTime;

    public Position() {
    }

    public Position(Driver driver, DelayDspOrder order) {
        this.fromLongitude = driver.getAddressLongitude();
        this.fromLatitude = driver.getAddressLatitude();
        this.fromCoordsys = order.getFromCoordsys();
        this.fromHash = GeoHashUtil.buildGeoHash(driver.getAddressLongitude(), driver.getAddressLatitude());

        this.toLongitude = order.getFromLongitude();
        this.toLatitude = order.getFromLatitude();
        this.toCoordsys = order.getFromCoordsys();
        this.toHash = order.getFromHash();
    }

    public Position(DelayDspOrder order, Driver driver) {
        this.fromLongitude = order.getToLongitude();
        this.fromLatitude = order.getToLatitude();
        this.fromCoordsys = order.getToCoordsys();
        this.fromHash = order.getToHash();

        this.toLongitude = driver.getAddressLongitude();
        this.toLatitude = driver.getAddressLatitude();
        this.toCoordsys = order.getFromCoordsys();
        this.toHash = GeoHashUtil.buildGeoHash(driver.getAddressLongitude(), driver.getAddressLatitude());
    }

    public Position(DelayDspOrder order1, DelayDspOrder order2) {
        this.fromLongitude = order1.getToLongitude();
        this.fromLatitude = order1.getToLatitude();
        this.fromCoordsys = order1.getToCoordsys();
        this.fromHash = order1.getToHash();

        this.toLongitude = order2.getFromLongitude();
        this.toLatitude = order2.getFromLatitude();
        this.toCoordsys = order2.getFromCoordsys();
        this.toHash = order2.getFromHash();
    }

    public Position(DelayDspOrder order1, DelayDspOrder order2, Date departureTime) {
        this.fromLongitude = order1.getToLongitude();
        this.fromLatitude = order1.getToLatitude();
        this.fromCoordsys = order1.getToCoordsys();
        this.fromHash = order1.getToHash();

        this.toLongitude = order2.getFromLongitude();
        this.toLatitude = order2.getFromLatitude();
        this.toCoordsys = order2.getFromCoordsys();
        this.toHash = order2.getFromHash();
        this.departureTime = departureTime;
    }

    public Position(DelayDspOrder order, Driver driver, Date departureTime) {
        this.fromLongitude = order.getToLongitude();
        this.fromLatitude = order.getToLatitude();
        this.fromCoordsys = order.getToCoordsys();
        this.fromHash = order.getToHash();

        this.toLongitude = driver.getAddressLongitude();
        this.toLatitude = driver.getAddressLatitude();
        this.toCoordsys = order.getFromCoordsys();
        this.toHash = GeoHashUtil.buildGeoHash(driver.getAddressLongitude(), driver.getAddressLatitude());
        this.departureTime = departureTime;
    }

    public String hash() {
        return Position.hash(fromHash, toHash);
    }

    public static String hash(String fromHash, String toHash) {
        return fromHash + CommonConstant.PLACEHOLDER + toHash;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Position position = (Position) o;
        return Objects.equals(hash(), position.hash());
    }

    @Override
    public int hashCode() {
        return Objects.hash(hash());
    }
}
