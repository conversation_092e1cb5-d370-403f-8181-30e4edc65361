package com.ctrip.dcs.dsp.delay.interfaces.consumer;

import com.ctrip.arch.distlock.DLock;
import com.ctrip.arch.distlock.redis.RedisDistributedLockService;
import com.ctrip.dcs.dsp.application.service.DelayDspMatchTaskApplicationService;
import com.ctrip.dcs.dsp.delay.consts.CommonConstant;
import com.ctrip.dcs.dsp.delay.enums.DelayDspTaskStatus;
import com.ctrip.dcs.dsp.delay.repository.DelayDspTaskRepository;
import com.ctrip.dcs.dsp.delay.util.JsonUtil;
import com.ctrip.dcs.dsp.delay.util.MetricsUtil;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.qmq.annontation.QmqLogTag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;

import java.util.HashMap;
import java.util.Map;

/**
 * 延后派任务匹配完成消费者
 */
@Component
public class DelayDspTaskMatchCompleteConsumer {

    private static final Logger logger = LoggerFactory.getLogger(DelayDspTaskMatchCompleteConsumer.class);

    private static final String LOCK_KEY_PREFIX = "DELAY_DSP_TASK_MATCH_COMPLETE_%s_%s";



    @Autowired
    private DelayDspMatchTaskApplicationService delayDspMatchTaskApplicationService;
    @Autowired
    private DelayDspTaskRepository delayDspTaskRepository;
    @Autowired
    private RedisDistributedLockService distributedLockService;


    @QmqLogTag(tagKeys = {"taskId", "taskVersion"})
    @QmqConsumer(prefix = CommonConstant.DELAY_DSP_TASK_MATCH_COMPLETE, consumerGroup = CommonConstant.APP_ID)
    public void onMessage(Message message) {
        Long taskId = message.getLongProperty("taskId");
        Integer taskVersion = message.getIntProperty("taskVersion");
        Map<String, String> map = new HashMap<>();
        map.put("taskId", String.valueOf(taskId));
        map.put("taskVersion", String.valueOf(taskVersion));
        logger.info("DelayDspMatchTaskRunConsumer_mq_content", JsonUtil.toJson(map));
        if(taskId == 0L){
            logger.error("DelayDspMatchTaskRunConsumer_taskId_or_taskVersion_is_zero", JsonUtil.toJson(map));
            return;
        }
        DLock lock = distributedLockService.getLock(getLockKey(taskId));
        try {
            if (!lock.tryLock()) {
                logger.warn("DelayDspTaskMatchCompleteConsumer_is_executing", JsonUtil.toJson(map));
                return;
            }
            //定时配置后续处理逻辑
            delayDspMatchTaskApplicationService.dealTaskMatchComplete(taskId, taskVersion);
        } catch (Exception e) {
            logger.error("DelayDspMatchTaskRunConsumer_error", e);
            MetricsUtil.recordValue("DelayDspTaskMatchCompleteConsumer_onMessage_error", 1);
            delayDspTaskRepository.updateVersion(taskId, DelayDspTaskStatus.UN_EXECUTED.getCode(), taskVersion + 1);
        } finally {
            lock.unlock();
        }
    }


    public String getLockKey(Long taskId){
        return String.format(LOCK_KEY_PREFIX, CommonConstant.APP_ID, taskId);
    }
}
