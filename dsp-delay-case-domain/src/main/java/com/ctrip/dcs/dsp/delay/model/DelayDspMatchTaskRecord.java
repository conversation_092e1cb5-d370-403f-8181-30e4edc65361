package com.ctrip.dcs.dsp.delay.model;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class DelayDspMatchTaskRecord {

    private Long id;

    private Long taskId;

    private Integer taskVersion;

    private String orderId;

    private String userOrderId;

    private String driverId;

    private Integer isOut;

    private Integer isDelay;

    private Integer takenCode;

    private Integer outReason;

    private Date outTime;

    private Date executeTime;

    private String orderSource;

    private Integer matchSuccess;

    private String driverOrderId;

    private Integer orderSeq;
}
