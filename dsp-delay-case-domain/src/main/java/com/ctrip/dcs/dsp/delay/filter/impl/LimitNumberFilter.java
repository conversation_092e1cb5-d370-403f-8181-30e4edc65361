package com.ctrip.dcs.dsp.delay.filter.impl;

import com.ctrip.dcs.dsp.delay.enums.YesOrNo;
import com.ctrip.dcs.dsp.delay.gateway.TrafficControlGateway;
import com.ctrip.dcs.dsp.delay.model.DelayDspTask;
import com.ctrip.dcs.dsp.delay.model.Driver;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.google.common.base.Splitter;
import com.google.common.collect.Sets;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qconfig.client.spring.QConfig;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component("limitNumberFilter")
public class LimitNumberFilter extends CommonFilter {

    public static final String NEW_ENERGY_GRAY_CITY_IDS = "new_energy_gray_city_ids";

    private static final Logger logger = LoggerFactory.getLogger(LimitNumberFilter.class);

    @Autowired
    private TrafficControlGateway trafficControlGateway;

    @QConfig("common_conf.properties")
    private Map<String, String> commonConf;


    @Override
    public List<Driver> doFilter(List<Driver> list, DelayDspTask task) {

        Set<String> license = list.stream()
                .filter(driver -> Objects.equals(driver.getIsEnergy(), YesOrNo.NO.getCode()))
                .filter(driver -> StringUtils.isNotBlank(driver.getCarLicense()))
                .map(Driver::getCarLicense)
                .collect(Collectors.toSet());
        //灰度城市的新能源也走限行逻辑
        license.addAll(getNewEnergyLicense(task.getCityId(), list));

        Set<String> limit = Sets.newHashSet();

        if (CollectionUtils.isNotEmpty(license)) {
            Set<String> set = trafficControlGateway.queryLimitLicense(task.getCityId(), task.getBeginTime(), task.getEndTime(), license);
            limit.addAll(set);
        }

        return list.stream()
                // 车辆未被限号
                .filter(driver -> {
                    boolean flag = !limit.contains(driver.getCarLicense());
                    if (!flag) {
                        logger.info("filter", "driver id: {}, filter reason: {}", driver.getDriverId(), "traffic control");
                    }
                    return flag;
                })
                .collect(Collectors.toList());
    }

    @Override
    String reason() {
        return "";
    }

    @Override
    boolean filter(Driver driver, DelayDspTask task) {
        return true;
    }

    public Set<String> getNewEnergyLicense(Integer cityId, List<Driver> list) {
        if (isEnergyGrayCityId(cityId)) {
            return list.stream()
                    .filter(driver -> Objects.equals(driver.getIsEnergy(), YesOrNo.YES.getCode()))
                    .filter(driver -> StringUtils.isNotBlank(driver.getCarLicense()))
                    .map(Driver::getCarLicense)
                    .collect(Collectors.toSet());
        }
        return Sets.newHashSet();
    }


    public boolean isEnergyGrayCityId(Integer cityId) {
        String grayCityIds = commonConf.get(NEW_ENERGY_GRAY_CITY_IDS);
        if (StringUtils.isNotBlank(grayCityIds) && cityId != null) {
            List<String> list = Splitter.on(",").splitToList(grayCityIds);
            if (list.contains(cityId.toString()) || list.contains("0")) {
                logger.info("energy_gray_city_hit"+cityId,"hit grayCityIds="+grayCityIds +"cityId="+cityId);
                return true;
            }
        }
        logger.info("energy_gray_city_not_hit"+cityId,"not hit grayCityIds="+grayCityIds +"cityId="+cityId);
        return false;
    }
}
