package com.ctrip.dcs.dsp.delay.service.impl;

import com.ctrip.dcs.dsp.delay.carconfig.SpecialDateConfig;
import com.ctrip.dcs.dsp.delay.carconfig.value.SpecialDateValue;
import com.ctrip.dcs.dsp.delay.model.InsertPoolReasonRecord;
import com.ctrip.dcs.dsp.delay.model.SupplyOrder;
import com.ctrip.dcs.dsp.delay.repository.InsertPoolReasonRecordRepository;
import com.ctrip.dcs.dsp.delay.service.DelayDspOrderService;
import com.ctrip.dcs.dsp.delay.util.DateUtil;
import com.ctrip.dcs.dsp.delay.util.JsonUtil;
import com.ctrip.dcs.dsp.delay.util.MetricsUtil;
import com.ctrip.dcs.dsp.delay.validator.ValidatorDTO;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024/8/15 16:14
 */
@Service
public class DelayDspOrderServiceImpl implements DelayDspOrderService {
    private static final Integer ALL_CITY = 0;
    private static final Integer ALL_CAR_TYPE_ID = 0;
    private static final Logger logger = LoggerFactory.getLogger(DelayDspOrderServiceImpl.class);
    @Resource
    InsertPoolReasonRecordRepository insertDspPoolReasonRecordRepository;
    @Resource
    SpecialDateConfig specialDateConfig;
    
    @Override
    public void recordInsertDspPoolReasonRecord(ValidatorDTO code, SupplyOrder supplyOrder, long startTime, long endTime) {
        try {
            InsertPoolReasonRecord record = buildInsertDspPoolReasonRecord(code, supplyOrder, startTime, endTime);
            insertDspPoolReasonRecordRepository.record(record);
        } catch (Exception ex) {
            MetricsUtil.recordValue("insert.dsp.pool.reason.record.fail", 1);
            logger.error("recordInsertDspPoolReasonRecord", "insert dsp pool reason record is failed.", ex, Maps.newHashMap());
        }
    }
    
    @Override
    public Boolean checkSpecialDate(Integer cityId, Integer carTypeId, String sysExpectBookTime) {
        SpecialDateValue specialDateValue = null;
        try {
            Date date = DateUtil.parseDate(sysExpectBookTime);
            //指定城市-指定车型
            specialDateValue = specialDateConfig.get(cityId, carTypeId, date);
            if (Objects.nonNull(specialDateValue)) {
                return Boolean.TRUE;
            }
            //全部城市-指定车型
            specialDateValue = specialDateConfig.get(ALL_CITY, carTypeId, date);
            if (Objects.nonNull(specialDateValue)) {
                return Boolean.TRUE;
            }
            //指定城市-全部车型
            specialDateValue = specialDateConfig.get(cityId, ALL_CAR_TYPE_ID, date);
            if (Objects.nonNull(specialDateValue)) {
                return Boolean.TRUE;
            }
            //全部城市-全部车型
            specialDateValue = specialDateConfig.get(ALL_CITY, ALL_CAR_TYPE_ID, date);
            if (Objects.nonNull(specialDateValue)) {
                return Boolean.TRUE;
            }
            logger.info("special_data_config", "special data config is empty. cityId:" + cityId + ", carTypeId:" + carTypeId);
            return Boolean.FALSE;
        } finally {
            if (Objects.nonNull(specialDateValue)) {
                logger.info("checkSpecialDate_hit_config", "config:" + JsonUtil.toJson(specialDateValue));
            }
        }
    }
    
    
    private InsertPoolReasonRecord buildInsertDspPoolReasonRecord(ValidatorDTO code, SupplyOrder supplyOrder, long startTime, long endTime) {
        InsertPoolReasonRecord record = new InsertPoolReasonRecord();
        record.setUserOrderId(supplyOrder.getSourceOrderId());
        record.setDspOrderId(supplyOrder.getOrderId());
        record.setReasonCode(code.getCode().name());
        record.setStartTime(String.valueOf(startTime));
        record.setEndTime(String.valueOf(endTime));
        return record;
    }
}
