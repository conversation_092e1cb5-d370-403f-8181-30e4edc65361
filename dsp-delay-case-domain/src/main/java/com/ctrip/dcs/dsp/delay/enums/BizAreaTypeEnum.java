package com.ctrip.dcs.dsp.delay.enums;

public enum BizAreaTypeEnum {

    CHF(0, 32), //国内
    IGT(1, 33), // 国际
    UNKNOWN(999, 999); //位置

    private Integer qunarCode;

    private Integer ctripCode;

    BizAreaTypeEnum(Integer qunarCode, Integer ctripCode) {
        this.qunarCode = qunarCode;
        this.ctripCode = ctripCode;
    }

    public Integer getQunarCode() {
        return this.qunarCode;
    }


    public Integer getCtripCode() {
        return this.ctripCode;
    }

    public static Boolean isIGT(Integer code) {
        if (null == code) {
            return false;
        }
        return IGT.getCtripCode().equals(code) || IGT.getQunarCode().equals(code);
    }
}