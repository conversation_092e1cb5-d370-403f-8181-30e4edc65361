package com.ctrip.dcs.dsp.delay.enums;

import com.ctrip.igt.framework.common.exception.BizException;

/**
 * order withStatus enum
 */
public enum SupplyOrderStatusDetail {
    SERVICE_CONFIRM(201),
    DISPATCHER_CONFIRM(202),
    DRIVER_CONFIRM(203),
    ;

    private int code;

    public int getCode() {
        return code;
    }

    public static SupplyOrderStatusDetail get(int code) {
        for (SupplyOrderStatusDetail status : SupplyOrderStatusDetail.values()) {
            if (status.code == code) {
                return status;
            }
        }
        throw new BizException("unknown order withStatus code:" + code);
    }

    SupplyOrderStatusDetail(int code) {
        this.code = code;
    }
}
