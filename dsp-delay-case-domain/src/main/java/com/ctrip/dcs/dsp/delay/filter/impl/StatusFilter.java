package com.ctrip.dcs.dsp.delay.filter.impl;

import com.ctrip.dcs.dsp.delay.enums.DriverStatus;
import com.ctrip.dcs.dsp.delay.model.DelayDspTask;
import com.ctrip.dcs.dsp.delay.model.Driver;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 */
@Component("statusFilter")
public class StatusFilter extends CommonFilter {

    @Override
    public boolean filter(Driver driver, DelayDspTask task) {
        return Objects.equals(driver.getStatus(), DriverStatus.ONLINE.getCode());
    }

    @Override
    public String reason() {
        return "driver status not online";
    }
}
