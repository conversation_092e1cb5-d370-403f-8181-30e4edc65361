package com.ctrip.dcs.dsp.delay.service;

import com.ctrip.dcs.dsp.delay.enums.YesOrNo;
import com.ctrip.dcs.dsp.delay.gateway.SelfDispatcherOrderGateway;
import com.ctrip.dcs.dsp.delay.limit.OutPoolRateLimiter;
import com.ctrip.dcs.dsp.delay.model.DelayDspOrder;
import com.ctrip.dcs.dsp.delay.model.DelayDspTask;
import com.ctrip.dcs.dsp.delay.model.DelayDspTaskRecord;
import com.ctrip.dcs.dsp.delay.model.SupplyOrder;
import com.ctrip.dcs.dsp.delay.qconfig.DelayDspCommonQConfig;
import com.ctrip.dcs.dsp.delay.repository.DelayDspOrderRepository;
import com.ctrip.dcs.dsp.delay.repository.DelayDspTaskRepository;
import com.ctrip.dcs.dsp.delay.service.impl.RedispatchOrderServiceImpl;
import com.ctrip.dcs.dsp.delay.util.DateUtil;
import com.ctrip.dcs.dsp.delay.util.MetricsUtil;
import com.ctrip.igt.framework.common.metrics.MetricsFactory;
import io.dropwizard.metrics5.MetricRegistry;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;

/**
 * <AUTHOR>
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest(value = {MetricsUtil.class, MetricsFactory.class})
public class RedispatchOrderServiceImplTest {

    @InjectMocks
    private RedispatchOrderServiceImpl redispatchOrderService;

    @Mock
    private MetricRegistry metricRegistry;

    @Mock
    private DelayDspTaskRepository delayDspTaskRepository;

    @Mock
    private DelayDspOrderRepository dspOrderRepository;

    @Mock
    private SelfDispatcherOrderGateway selfDispatcherOrderGateway;

    @Mock
    private OutPoolRateLimiter outPoolRateLimiter;

    @Mock
    private DelayDspTaskRecord delayDspTaskRecord;

    @Mock
    private DelayDspTask delayDspTask;

    @Mock
    private DelayDspOrder delayDspOrder;

    @Mock
    private SupplyOrder mainOrder;

    @Mock
    private PreOutPoolService preOutPoolService;
    @Mock
    private DelayDspCommonQConfig delayDspCommonQConfig;


    @Test
    public void test1() {
        PowerMockito.mockStatic(MetricsFactory.class );
        PowerMockito.when(MetricsFactory.getMetricRegistry()).thenReturn(metricRegistry);
        PowerMockito.mockStatic(MetricsUtil.class );
        redispatchOrderService.redispatch(delayDspTaskRecord);
        Mockito.verify(selfDispatcherOrderGateway, Mockito.times(0)).redispatch(Mockito.any());
    }

    @Test
    public void test2() {
        PowerMockito.mockStatic(MetricsFactory.class );
        PowerMockito.when(MetricsFactory.getMetricRegistry()).thenReturn(metricRegistry);
        PowerMockito.mockStatic(MetricsUtil.class );

        PowerMockito.when(delayDspTaskRepository.queryByTaskId(Mockito.any())).thenReturn(new DelayDspTask());
        PowerMockito.when(dspOrderRepository.queryByOrderId(Mockito.any())).thenReturn(null);
        redispatchOrderService.redispatch(delayDspTaskRecord);
        Mockito.verify(selfDispatcherOrderGateway, Mockito.times(0)).redispatch(Mockito.any());
    }

    @Test
    public void test3() {
        PowerMockito.mockStatic(MetricsFactory.class );
        PowerMockito.when(MetricsFactory.getMetricRegistry()).thenReturn(metricRegistry);
        PowerMockito.mockStatic(MetricsUtil.class );

        PowerMockito.when(delayDspTaskRepository.queryByTaskId(Mockito.any())).thenReturn(null);
        PowerMockito.when(dspOrderRepository.queryByOrderId(Mockito.any())).thenReturn(new DelayDspOrder());
        redispatchOrderService.redispatch(delayDspTaskRecord);
        Mockito.verify(selfDispatcherOrderGateway, Mockito.times(0)).redispatch(Mockito.any());
    }

    @Test
    public void test4() {
        PowerMockito.mockStatic(MetricsFactory.class );
        PowerMockito.when(MetricsFactory.getMetricRegistry()).thenReturn(metricRegistry);
        PowerMockito.mockStatic(MetricsUtil.class );

        DelayDspTask delayDspTask1 = new DelayDspTask();
        delayDspTask1.setExecuteTimeDeadline(DateUtil.addMinutes(new Date(), -500));
        PowerMockito.when(delayDspTaskRepository.queryByTaskId(Mockito.any())).thenReturn(delayDspTask1);
        PowerMockito.when(dspOrderRepository.queryByOrderId(Mockito.any())).thenReturn(new DelayDspOrder());
        PowerMockito.when(delayDspOrder.getIsDelay()).thenReturn(YesOrNo.NO.getCode());
        redispatchOrderService.redispatch(delayDspTaskRecord);
        Mockito.verify(selfDispatcherOrderGateway, Mockito.times(1)).redispatch(Mockito.any());
    }

    @Test
    public void test5() {
        PowerMockito.mockStatic(MetricsFactory.class );
        PowerMockito.when(MetricsFactory.getMetricRegistry()).thenReturn(metricRegistry);
        PowerMockito.mockStatic(MetricsUtil.class );

        PowerMockito.when(delayDspTaskRepository.queryByTaskId(Mockito.any())).thenReturn(new DelayDspTask());
        PowerMockito.when(dspOrderRepository.queryByOrderId(Mockito.any())).thenReturn(new DelayDspOrder());
        PowerMockito.when(delayDspOrder.getIsDelay()).thenReturn(YesOrNo.YES.getCode());
        redispatchOrderService.redispatch(delayDspTaskRecord);
        Mockito.verify(selfDispatcherOrderGateway, Mockito.times(1)).redispatch(Mockito.any());
    }

    @Test
    public void test6() {
        PowerMockito.mockStatic(MetricsFactory.class );
        PowerMockito.when(MetricsFactory.getMetricRegistry()).thenReturn(metricRegistry);
        PowerMockito.mockStatic(MetricsUtil.class );

        PowerMockito.when(delayDspTaskRepository.queryByTaskId(Mockito.any())).thenReturn(new DelayDspTask());
        PowerMockito.when(dspOrderRepository.queryByOrderId(Mockito.any())).thenReturn(new DelayDspOrder());
        PowerMockito.when(delayDspOrder.getIsDelay()).thenReturn(YesOrNo.YES.getCode());
        PowerMockito.when(delayDspTask.getExecuteTimeDeadline()).thenReturn(DateUtil.addMinutes(new Date(), 500));
        redispatchOrderService.redispatch(delayDspTaskRecord);
        Mockito.verify(selfDispatcherOrderGateway, Mockito.times(1)).redispatch(Mockito.any());
    }

    @Test
    public void test7() {
        PowerMockito.mockStatic(MetricsFactory.class );
        PowerMockito.when(MetricsFactory.getMetricRegistry()).thenReturn(metricRegistry);
        PowerMockito.mockStatic(MetricsUtil.class );

        PowerMockito.when(delayDspTaskRepository.queryByTaskId(Mockito.any())).thenReturn(new DelayDspTask());
        PowerMockito.when(dspOrderRepository.queryByOrderId(Mockito.any())).thenReturn(new DelayDspOrder());
        PowerMockito.when(delayDspOrder.getIsDelay()).thenReturn(YesOrNo.YES.getCode());
        PowerMockito.when(delayDspTask.getExecuteTimeDeadline()).thenReturn(DateUtil.addMinutes(new Date(), 500));
        PowerMockito.when(selfDispatcherOrderGateway.queryByUserOrderId(Mockito.any())).thenReturn(mainOrder);
        redispatchOrderService.redispatch(delayDspTaskRecord);
        Mockito.verify(selfDispatcherOrderGateway, Mockito.times(1)).redispatch(Mockito.any());
    }

    @Test
    public void test8() {
        PowerMockito.mockStatic(MetricsFactory.class );
        PowerMockito.when(MetricsFactory.getMetricRegistry()).thenReturn(metricRegistry);
        PowerMockito.mockStatic(MetricsUtil.class );

        PowerMockito.when(delayDspTaskRepository.queryByTaskId(Mockito.any())).thenReturn(new DelayDspTask());
        PowerMockito.when(dspOrderRepository.queryByOrderId(Mockito.any())).thenReturn(new DelayDspOrder());
        PowerMockito.when(delayDspOrder.getIsDelay()).thenReturn(YesOrNo.YES.getCode());
        PowerMockito.when(delayDspTask.getExecuteTimeDeadline()).thenReturn(DateUtil.addMinutes(new Date(), 500));
        PowerMockito.when(selfDispatcherOrderGateway.query(Mockito.any())).thenReturn(mainOrder);
        PowerMockito.when(mainOrder.isServiceProviderConfirm()).thenReturn(false);
        redispatchOrderService.redispatch(delayDspTaskRecord);
        Mockito.verify(selfDispatcherOrderGateway, Mockito.times(1)).redispatch(Mockito.any());
    }

    @Test
    public void test9() {
        PowerMockito.mockStatic(MetricsFactory.class );
        PowerMockito.when(dspOrderRepository.queryByOrderId(Mockito.any())).thenReturn(null);
        PowerMockito.mockStatic(MetricsUtil.class );
        redispatchOrderService.redispatch(delayDspTaskRecord);
        Mockito.verify(selfDispatcherOrderGateway, Mockito.times(0)).redispatch(Mockito.any());
    }

    @Test
    public void test10() {
        PowerMockito.mockStatic(MetricsFactory.class );
        DelayDspOrder delayDspOrder1 = new DelayDspOrder();
        delayDspOrder1.setIsDelay(YesOrNo.NO.getCode());
        PowerMockito.when(dspOrderRepository.queryByOrderId(Mockito.any())).thenReturn(delayDspOrder1);
        PowerMockito.mockStatic(MetricsUtil.class );
        redispatchOrderService.redispatch(delayDspTaskRecord);
        Mockito.verify(selfDispatcherOrderGateway, Mockito.times(0)).redispatch(Mockito.any());
    }
}
