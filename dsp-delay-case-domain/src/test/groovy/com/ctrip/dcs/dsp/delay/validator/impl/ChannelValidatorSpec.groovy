package com.ctrip.dcs.dsp.delay.validator.impl

import com.ctrip.dcs.dsp.delay.gateway.ChannelGateway
import com.ctrip.dcs.dsp.delay.model.ChannelNumber
import com.ctrip.dcs.dsp.delay.model.SupplyOrder
import com.ctrip.dcs.dsp.delay.qconfig.DelayDspCommonQConfig
import com.ctrip.dcs.dsp.delay.validator.ValidatorCode
import com.google.common.collect.Sets
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 */
class ChannelValidatorSpec extends Specification {

    def channelGateway = Mock(ChannelGateway)

    def delayDspCommonQConfig = Mock(DelayDspCommonQConfig)

    def validator = new ChannelValidator(channelGateway: channelGateway, delayDspCommonQConfig: delayDspCommonQConfig)

    @Unroll
    def "validate"() {

        given: "Mock数据"
        channelGateway.queryChannelNumber(1L) >> channel
        delayDspCommonQConfig.getNotDelayDspChannelSet() >> notDelayDspChannelSet
        delayDspCommonQConfig.getDelayDspChannelSet() >> delayDspChannelSet


        when: "执行校验方法"
        def res = validator.validate(order)

        then: "验证校验结果"
        res.getCode() == code

        where:
        order            | channel            | notDelayDspChannelSet | delayDspChannelSet || code
        getSupplyOrder() | null               | null                  | null               || ValidatorCode.ERROR
        getSupplyOrder() | getChannelNumber() | newHashSet(1L)        | null               || ValidatorCode.CHANNEL
        getSupplyOrder() | getChannelNumber() | newHashSet(5L)        | newHashSet(1L)     || ValidatorCode.CHANNEL
        getSupplyOrder() | getChannelNumber() | newHashSet(5L)        | newHashSet(2L)     || ValidatorCode.OK
    }

    def getSupplyOrder() {
        return new SupplyOrder(distributionChannel: 1L)
    }

    def getChannelNumber() {
        return new ChannelNumber(
                channelNumberId: 1L,
                primaryChannelGroupId: 2L,
                secondaryChannelGroupId: 3L,
                tertiaryChannelGroupId: 4L
        )
    }

    def newHashSet(long i) {
        return Sets.newHashSet(i)
    }

}
