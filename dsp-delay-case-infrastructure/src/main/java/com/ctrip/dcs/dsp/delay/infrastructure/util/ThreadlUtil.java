package com.ctrip.dcs.dsp.delay.infrastructure.util;

import com.ctrip.dcs.dsp.delay.infrastructure.dto.CommonThreadContext;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.google.common.collect.Lists;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @since 2025/4/8 14:33
 */
public class ThreadlUtil {
    
    private static Logger logger = LoggerFactory.getLogger(ThreadlUtil.class);
    
    public static <R> List<R> getData(Function<CommonThreadContext, List<Future<R>>> function, CommonThreadContext context, Integer timeout) {
        List<R> result = Lists.newArrayList();
        List<Future<R>> futureList = function.apply(context);
        for (Future<R> future : futureList) {
            try {
                R res = future.get(timeout, TimeUnit.MILLISECONDS);
                if (Objects.isNull(res)) {
                    continue;
                }
                result.add(res);
            } catch (Exception ex) {
                logger.error("threadUtil_getData_" + context.getThreadPoolName(), ex);
            }
        }
        return result;
    }
}
