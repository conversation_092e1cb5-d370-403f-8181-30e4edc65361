package com.ctrip.dcs.dsp.delay.validator.impl;

import com.ctrip.dcs.dsp.delay.enums.YesOrNo;
import com.ctrip.dcs.dsp.delay.model.SupplyOrder;
import com.ctrip.dcs.dsp.delay.validator.Validator;
import com.ctrip.dcs.dsp.delay.validator.ValidatorCode;
import com.ctrip.dcs.dsp.delay.validator.ValidatorDTO;
import org.springframework.stereotype.Component;

@Component("highGradeOrderValidator")
public class HighGradeOrderValidator implements Validator {

    @Override
    public ValidatorDTO validate(SupplyOrder order) {
        if (order.getHighGradeOrder() == null) {
            // 代表是调的老接口，不用处理，直接通过
            return new ValidatorDTO(ValidatorCode.OK);
        }
        if (order.getHighGradeOrder() == YesOrNo.NO.getCode()) {
            return new ValidatorDTO(ValidatorCode.OK);
        } else {
            return new ValidatorDTO(ValidatorCode.HIGH_LEVEL_ORDER_NOT_GOLD_DRIVER_LIMIT);
        }
    }

}
