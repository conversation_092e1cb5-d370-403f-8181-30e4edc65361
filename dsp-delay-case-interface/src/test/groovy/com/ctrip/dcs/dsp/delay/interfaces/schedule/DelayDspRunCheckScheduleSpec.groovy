package com.ctrip.dcs.dsp.delay.interfaces.schedule

import com.ctrip.dcs.dsp.delay.infrastructure.mapper.DelayDspTaskMapper
import com.ctrip.dcs.dsp.delay.infrastructure.mapper.DelayDspTaskRecordMapper
import com.ctrip.dcs.dsp.delay.model.DelayDspTask
import com.ctrip.dcs.dsp.delay.model.DelayDspTaskRecord
import com.ctrip.dcs.dsp.delay.mq.MessageProducer
import com.ctrip.dcs.dsp.delay.qconfig.DelayDspCommonQConfig
import com.ctrip.dcs.dsp.delay.repository.DelayDspTaskRepository
import com.google.common.collect.Lists
import org.springframework.beans.factory.annotation.Autowired
import qunar.tc.schedule.MockParameter
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 */
class DelayDspRunCheckScheduleSpec extends Specification {

    def delayDspCommonQConfig = Mock(DelayDspCommonQConfig)

    def delayDspTaskRepository = Mock(DelayDspTaskRepository)

    def schedule = new DelayDspRunCheckSchedule(delayDspTaskRepository: delayDspTaskRepository, delayDspCommonQConfig: delayDspCommonQConfig)

    @Unroll
    def "test execute"() {

        given: "Mock数据"
        delayDspCommonQConfig.getWarnDeadTime() >> 60
        delayDspTaskRepository.queryCountByDeadTime(_) >> taskCount

        when: "执行校验方法"
        def res = schedule.execute(new MockParameter())

        then: "验证校验结果"
        res == result

        where:
        taskCount                              || result
        Lists.newArrayList()                   || null
        Lists.newArrayList(new DelayDspTask()) || null

    }
}
