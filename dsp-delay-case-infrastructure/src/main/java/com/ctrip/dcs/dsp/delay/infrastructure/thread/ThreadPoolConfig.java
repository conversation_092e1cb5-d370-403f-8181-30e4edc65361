package com.ctrip.dcs.dsp.delay.infrastructure.thread;

import com.ctrip.dcs.dsp.delay.qconfig.DelayDspCommonQConfig;
import com.ctrip.igt.framework.common.concurrent.threadpool.ThreadPoolBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 */
@Configuration
public class ThreadPoolConfig {

    @Autowired
    private DelayDspCommonQConfig delayDspCommonQConfig;

    @Bean("driverThreadPool")
    public ExecutorService getDriverThreadPool() {
        return new ThreadPoolBuilder.FixedThreadPoolBuilder()
                .setPoolSize(4)
                .setDaemon(true)
                .setQueueSize(9999999)
                .setThreadNamePrefix("dcs-delay-driver-thread-")
                .build();
    }

    @Bean("takenThreadPool")
    public ExecutorService getTakenThreadPool() {
        return new ThreadPoolBuilder.FixedThreadPoolBuilder()
                .setPoolSize(1)
                .setDaemon(true)
                .setQueueSize(9999999)
                .setThreadNamePrefix("dcs-delay-driver-taken-thread-")
                .build();
    }

    @Bean("redispatchThreadPool")
    public ExecutorService getRedispatchThreadPool() {
        return new ThreadPoolBuilder.FixedThreadPoolBuilder()
                .setPoolSize(1)
                .setDaemon(true)
                .setQueueSize(9999999)
                .setThreadNamePrefix("dcs-delay-driver-redispatch-thread-")
                .build();
    }


    @Bean("gaoDeFutureThreadPool")
    public ExecutorService gaoDeFutureThreadPool() {
        return new ThreadPoolExecutor(
                delayDspCommonQConfig.getGaoDeFutureThreadCorePoolSize(), // 核心线程数
                delayDspCommonQConfig.getGaoDeFutureThreadMaxPoolSize(), // 最大线程数
                delayDspCommonQConfig.getKeepAliveTime(), // 空闲线程存活时间
                TimeUnit.MILLISECONDS, // 时间单位
                new LinkedBlockingQueue<>(delayDspCommonQConfig.getDelayQueueSize()), // 阻塞队列
                new NamedThreadFactory("delayGaoDeFutureThreadPool"), // 线程工厂
                new ThreadPoolExecutor.CallerRunsPolicy() // 拒绝策略
        );
    }

    @Bean("delayGaoDeFutureThreadPool")
    public ExecutorService delayGaoDeFutureThreadPool() {
        return new ThreadPoolExecutor(
                delayDspCommonQConfig.getDelayGaoDeFutureThreadCorePoolSize(), // 核心线程数
                delayDspCommonQConfig.getDelayGaoDeFutureThreadMaxPoolSize(), // 最大线程数
                delayDspCommonQConfig.getKeepAliveTime(), // 空闲线程存活时间
                TimeUnit.MILLISECONDS, // 时间单位
                new LinkedBlockingQueue<>(delayDspCommonQConfig.getDelayQueueSize()), // 阻塞队列
                new NamedThreadFactory("delayGaoDeFutureThreadPool"), // 线程工厂
                new ThreadPoolExecutor.CallerRunsPolicy() // 拒绝策略
        );
    }


    // 自定义线程工厂（给线程命名，方便定位问题）
    private static class NamedThreadFactory implements ThreadFactory {
        private final String namePrefix;
        private final AtomicInteger threadNumber = new AtomicInteger(1);

        NamedThreadFactory(String namePrefix) {
            this.namePrefix = namePrefix;
        }

        @Override
        public Thread newThread(Runnable r) {
            Thread thread = new Thread(r, namePrefix + "-" + threadNumber.getAndIncrement());
            if (thread.isDaemon()) {
                thread.setDaemon(false); // 用户线程
            }
            if (thread.getPriority() != Thread.NORM_PRIORITY) {
                thread.setPriority(Thread.NORM_PRIORITY);
            }
            return thread;
        }
    }
}
