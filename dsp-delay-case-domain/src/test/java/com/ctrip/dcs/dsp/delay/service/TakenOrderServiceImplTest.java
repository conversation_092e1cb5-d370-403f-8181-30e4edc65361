package com.ctrip.dcs.dsp.delay.service;

import com.ctrip.dcs.dsp.delay.enums.OrderTakenCode;
import com.ctrip.dcs.dsp.delay.enums.YesOrNo;
import com.ctrip.dcs.dsp.delay.gateway.DriverGateway;
import com.ctrip.dcs.dsp.delay.gateway.SelfDispatcherOrderGateway;
import com.ctrip.dcs.dsp.delay.limit.OutPoolRateLimiter;
import com.ctrip.dcs.dsp.delay.model.DelayDspOrder;
import com.ctrip.dcs.dsp.delay.model.DelayDspTask;
import com.ctrip.dcs.dsp.delay.model.DelayDspTaskRecord;
import com.ctrip.dcs.dsp.delay.model.Driver;
import com.ctrip.dcs.dsp.delay.mq.MessageProducer;
import com.ctrip.dcs.dsp.delay.repository.DelayDspOrderRepository;
import com.ctrip.dcs.dsp.delay.repository.DelayDspTaskRepository;
import com.ctrip.dcs.dsp.delay.service.impl.TakenOrderServiceImpl;
import com.ctrip.dcs.dsp.delay.util.DateUtil;
import com.ctrip.dcs.dsp.delay.util.MetricsUtil;
import com.ctrip.igt.framework.common.metrics.MetricsFactory;
import io.dropwizard.metrics5.MetricRegistry;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;

/**
 * <AUTHOR>
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest(value = {MetricsUtil.class, MetricsFactory.class})
public class TakenOrderServiceImplTest {

    @InjectMocks
    private TakenOrderServiceImpl takenOrderService;

    @Mock
    private MetricRegistry metricRegistry;

    @Mock
    private DelayDspTaskRepository delayDspTaskRepository;

    @Mock
    private DelayDspOrderRepository dspOrderRepository;

    @Mock
    private DriverGateway driverGateway;

    @Mock
    private RedispatchOrderService redispatchOrderService;

    @Mock
    private OutPoolRateLimiter outPoolRateLimiter;

    @Mock
    private SelfDispatcherOrderGateway selfDispatcherOrderGateway;

    @Mock
    private DelayDspTaskRecord delayDspTaskRecord;

    @Mock
    private DelayDspTask delayDspTask;

    @Mock
    private DelayDspOrder delayDspOrder;

    @Mock
    private Driver driver;

    @Mock
    private MessageProducer messageProducer;

    @Test
    public void test1() {
        PowerMockito.mockStatic(MetricsFactory.class );
        PowerMockito.when(MetricsFactory.getMetricRegistry()).thenReturn(metricRegistry);
        PowerMockito.mockStatic(MetricsUtil.class );

        PowerMockito.when(delayDspTaskRepository.queryByTaskId(Mockito.any())).thenReturn(null);
        PowerMockito.when(dspOrderRepository.queryByOrderId(Mockito.any())).thenReturn(delayDspOrder);
        takenOrderService.taken(delayDspTaskRecord);
        Mockito.verify(selfDispatcherOrderGateway, Mockito.times(0)).taken(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any());
    }

    @Test
    public void test2() {
        PowerMockito.mockStatic(MetricsFactory.class );
        PowerMockito.when(MetricsFactory.getMetricRegistry()).thenReturn(metricRegistry);
        PowerMockito.mockStatic(MetricsUtil.class );

        PowerMockito.when(delayDspTaskRepository.queryByTaskId(Mockito.any())).thenReturn(delayDspTask);
        PowerMockito.when(dspOrderRepository.queryByOrderId(Mockito.any())).thenReturn(null);
        takenOrderService.taken(delayDspTaskRecord);
        Mockito.verify(selfDispatcherOrderGateway, Mockito.times(0)).taken(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any());
    }

    @Test
    public void test3() {
        PowerMockito.mockStatic(MetricsFactory.class );
        PowerMockito.when(MetricsFactory.getMetricRegistry()).thenReturn(metricRegistry);
        PowerMockito.mockStatic(MetricsUtil.class );

        PowerMockito.when(delayDspTaskRepository.queryByTaskId(Mockito.any())).thenReturn(delayDspTask);
        PowerMockito.when(dspOrderRepository.queryByOrderId(Mockito.any())).thenReturn(delayDspOrder);
        PowerMockito.when(delayDspOrder.getIsDelay()).thenReturn(YesOrNo.NO.getCode());
        takenOrderService.taken(delayDspTaskRecord);
        Mockito.verify(selfDispatcherOrderGateway, Mockito.times(0)).taken(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any());
    }

    @Test
    public void test4() {
        PowerMockito.mockStatic(MetricsFactory.class );
        PowerMockito.when(MetricsFactory.getMetricRegistry()).thenReturn(metricRegistry);
        PowerMockito.mockStatic(MetricsUtil.class );

        PowerMockito.when(delayDspTaskRepository.queryByTaskId(Mockito.any())).thenReturn(delayDspTask);
        PowerMockito.when(dspOrderRepository.queryByOrderId(Mockito.any())).thenReturn(delayDspOrder);
        PowerMockito.when(delayDspOrder.getIsDelay()).thenReturn(YesOrNo.YES.getCode());
        takenOrderService.taken(delayDspTaskRecord);
        Mockito.verify(selfDispatcherOrderGateway, Mockito.times(0)).taken(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any());
    }

    @Test
    public void test5() {
        PowerMockito.mockStatic(MetricsFactory.class );
        PowerMockito.when(MetricsFactory.getMetricRegistry()).thenReturn(metricRegistry);
        PowerMockito.mockStatic(MetricsUtil.class );

        PowerMockito.when(delayDspTaskRepository.queryByTaskId(Mockito.any())).thenReturn(delayDspTask);
        PowerMockito.when(dspOrderRepository.queryByOrderId(Mockito.any())).thenReturn(delayDspOrder);
        PowerMockito.when(delayDspOrder.getIsDelay()).thenReturn(YesOrNo.YES.getCode());
        PowerMockito.when(delayDspTaskRecord.getDriverId()).thenReturn("0");
        takenOrderService.taken(delayDspTaskRecord);
        Mockito.verify(selfDispatcherOrderGateway, Mockito.times(0)).taken(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any());
    }

    @Test
    public void test6() {
        PowerMockito.mockStatic(MetricsFactory.class );
        PowerMockito.when(MetricsFactory.getMetricRegistry()).thenReturn(metricRegistry);
        PowerMockito.mockStatic(MetricsUtil.class );

        PowerMockito.when(delayDspTaskRepository.queryByTaskId(Mockito.any())).thenReturn(delayDspTask);
        PowerMockito.when(dspOrderRepository.queryByOrderId(Mockito.any())).thenReturn(delayDspOrder);
        PowerMockito.when(delayDspOrder.getIsDelay()).thenReturn(YesOrNo.YES.getCode());
        PowerMockito.when(delayDspTaskRecord.getDriverId()).thenReturn("1");
        takenOrderService.taken(delayDspTaskRecord);
        Mockito.verify(selfDispatcherOrderGateway, Mockito.times(0)).taken(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any());
    }

    @Test
    public void test7() {
        PowerMockito.mockStatic(MetricsFactory.class );
        PowerMockito.when(MetricsFactory.getMetricRegistry()).thenReturn(metricRegistry);
        PowerMockito.mockStatic(MetricsUtil.class );

        PowerMockito.when(delayDspTaskRepository.queryByTaskId(Mockito.any())).thenReturn(delayDspTask);
        PowerMockito.when(dspOrderRepository.queryByOrderId(Mockito.any())).thenReturn(delayDspOrder);
        PowerMockito.when(delayDspOrder.getIsDelay()).thenReturn(YesOrNo.YES.getCode());
        PowerMockito.when(delayDspTaskRecord.getDriverId()).thenReturn("1");
        PowerMockito.when(delayDspTaskRecord.getDriverOrderId()).thenReturn("1");
        takenOrderService.taken(delayDspTaskRecord);
        Mockito.verify(selfDispatcherOrderGateway, Mockito.times(0)).taken(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any());
    }

    @Test
    public void test8() {
        PowerMockito.mockStatic(MetricsFactory.class );
        PowerMockito.when(MetricsFactory.getMetricRegistry()).thenReturn(metricRegistry);
        PowerMockito.mockStatic(MetricsUtil.class );

        PowerMockito.when(delayDspTaskRepository.queryByTaskId(Mockito.any())).thenReturn(delayDspTask);
        PowerMockito.when(dspOrderRepository.queryByOrderId(Mockito.any())).thenReturn(delayDspOrder);
        PowerMockito.when(delayDspOrder.getIsDelay()).thenReturn(YesOrNo.YES.getCode());
        PowerMockito.when(delayDspTaskRecord.getDriverId()).thenReturn("1");
        PowerMockito.when(delayDspTaskRecord.getDriverOrderId()).thenReturn("1");
        PowerMockito.when(driverGateway.query(Mockito.anyString())).thenReturn(driver);
        PowerMockito.when(delayDspTask.getExecuteTimeDeadline()).thenReturn(DateUtil.addMinutes(new Date(), 500));
        PowerMockito.when(selfDispatcherOrderGateway.taken(Mockito.any(),Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(OrderTakenCode.ERROR.getCode());
        takenOrderService.taken(delayDspTaskRecord);
        Mockito.verify(selfDispatcherOrderGateway, Mockito.times(1)).taken(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any());
    }
}
