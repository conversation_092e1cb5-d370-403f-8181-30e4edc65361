package com.ctrip.dcs.dsp.delay.infrastructure.mapper;

import com.ctrip.dcs.dsp.delay.model.DelayDspOrder;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface DelayDspOrderMapper {

    Long insertOrUpdate(DelayDspOrder order);

    DelayDspOrder queryByOrderId(@Param("orderId") String orderId);

    void update(DelayDspOrder order);

    void updateForCancel(DelayDspOrder order);


    List<DelayDspOrder> queryByTaskId(@Param("taskId") Long taskId);

    List<DelayDspOrder> queryByType(@Param("taskId") Long taskId, @Param("isDelay") Integer isDelay);

    DelayDspOrder queryByMainOrderId(@Param("mainOrderId") String mainOrderId);

    DelayDspOrder queryDelayOrderByMainOrderId(@Param("mainOrderId") String mainOrderId);

    List<DelayDspOrder> queryByOrderIds(@Param("orderIds") List<String> orderIds);

    List<DelayDspOrder> queryByDrivers(@Param("driverIds") List<String> driverIds, @Param("beginTime") Date beginTime, @Param("endTime") Date endTime);

    List<DelayDspOrder> queryByOrderIdsWithTaskId(@Param("taskId")Long taskId, @Param("orderIds")List<String> orderIds, @Param("limitNum")Integer limitNum);
}
