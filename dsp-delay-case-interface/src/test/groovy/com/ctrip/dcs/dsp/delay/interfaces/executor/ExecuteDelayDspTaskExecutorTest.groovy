package com.ctrip.dcs.dsp.delay.interfaces.executor

import com.ctrip.dcs.dsp.delay.enums.DelayDspTaskStatus
import com.ctrip.dcs.dsp.delay.model.DelayDspTask
import com.ctrip.dcs.dsp.delay.mq.MessageProducer
import com.ctrip.dcs.dsp.delay.repository.DelayDspTaskRepository
import com.ctrip.dcs.dsp.delay.service.interfaces.message.ExecuteDelayDspTaskRequestType
import com.ctrip.dcs.dsp.delay.service.interfaces.message.ExecuteDelayDspTaskResponseType
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

/**
 * <AUTHOR>
 */
class ExecuteDelayDspTaskExecutorTest extends Specification {
    @Mock
    DelayDspTaskRepository delayDspTaskRepository
    @Mock
    MessageProducer messageProducer
    @InjectMocks
    ExecuteDelayDspTaskExecutor executeDelayDspTaskExecutor

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test execute"() {
        given:
        DelayDspTask task = new DelayDspTask()
        task.setTaskId(1L)
        task.setTaskStatus(DelayDspTaskStatus.UN_EXECUTED.getCode())
        when(delayDspTaskRepository.queryByTaskId(anyLong())).thenReturn(null).thenReturn(task)
        ExecuteDelayDspTaskRequestType requestType = new ExecuteDelayDspTaskRequestType()
        requestType.setTaskId(1L)

        when:
        ExecuteDelayDspTaskResponseType result1 = executeDelayDspTaskExecutor.execute(requestType)
        ExecuteDelayDspTaskResponseType result2 = executeDelayDspTaskExecutor.execute(requestType)

        then:
        result1.getResponseResult().getReturnCode() == "500"
        result2.getResponseResult().getReturnCode() == "200"
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme