<?xml version="1.0" encoding="utf-8" ?>
<profile>
    <environments>
        <!--所有属性及XML节点名称均需要区分大小写 -->
        <!--name属性的命名可以随意进行，只要符合Windows的文件夹名规范即可，下文中的类似prd_iis7_net4的节点名称就是依据此属性而得来的，很多
            同事会以target来命名，这是不太理想的 -->
        <!--target属性可以是dev,test等属性，是用来区分当前环境的 -->
        <!--iis属性可以是IIS6,IIS7或者ALL -->
        <!--net属性可以是NET2，NET4或者ALL -->
        <!--dataCenter属性，可以是如下值: sh,nt,sh2或者ALL，也可以是sh,nt,sh2中任意多个值的并集，以逗号分隔 -->
        <!--以上5个属性共同组成了一个元数据，可以唯一的标识一个环境 -->
        <add name="dev" target="dev" iis="ALL" net="ALL" dataCenter="sh" />
        <add name="fws" target="fws" iis="ALL" net="ALL" dataCenter="ALL" />
        <add name="fat" target="fat" iis="ALL" net="ALL" dataCenter="ALL" />
        <add name="lpt" target="lpt" iis="ALL" net="ALL" dataCenter="ALL" />
        <add name="uat" target="uat" iis="ALL" net="ALL" dataCenter="ALL" />
        <add name="prd" target="prd" iis="ALL" net="ALL" dataCenter="ALL" />
    </environments>
    <!--environments节点有多少个子节点，则下文中必须出现同样数量的节点，且名称必须与上文中的name属性一致 -->
    <dev>
        <logging.server>collector.logging.fws.qa.nt.ctripcorp.com</logging.server>
        <logging.port>63100</logging.port>
        <logging.level>DEBUG</logging.level>

        <web.fxConfigServiceUrl>http://ws.config.framework.fws.qa.nt.ctripcorp.com/configws/</web.fxConfigServiceUrl>
        <web.appId>*********</web.appId>
        <web.subEnv>fat</web.subEnv>

        <db.serviceUrl>https://ws.titan.fws.qa.nt.ctripcorp.com/titanservice/query</db.serviceUrl>
    </dev>
    <fat>
        <logging.server>collector.logging.fws.qa.nt.ctripcorp.com</logging.server>
        <logging.port>63100</logging.port>
        <logging.level>DEBUG</logging.level>

        <web.fxConfigServiceUrl>http://ws.config.framework.fws.qa.nt.ctripcorp.com/configws/</web.fxConfigServiceUrl>
        <web.appId>*********</web.appId>
        <web.subEnv>fat</web.subEnv>

        <db.serviceUrl>https://ws.titan.fws.qa.nt.ctripcorp.com/titanservice/query</db.serviceUrl>
    </fat>
    <lpt>
        <logging.server>collector.logging.fws.qa.nt.ctripcorp.com</logging.server>
        <logging.port>63100</logging.port>
        <logging.level>DEBUG</logging.level>

        <web.fxConfigServiceUrl>http://ws.config.framework.fws.qa.nt.ctripcorp.com/configws/</web.fxConfigServiceUrl>
        <web.appId>*********</web.appId>
        <web.subEnv>fat</web.subEnv>
        <db.serviceUrl>https://ws.titan.fws.qa.nt.ctripcorp.com/titanservice/query</db.serviceUrl>
    </lpt>
    <fws>
        <logging.server>collector.logging.fws.qa.nt.ctripcorp.com</logging.server>
        <logging.port>63100</logging.port>
        <logging.level>DEBUG</logging.level>

        <web.fxConfigServiceUrl>http://ws.config.framework.fws.qa.nt.ctripcorp.com/configws/</web.fxConfigServiceUrl>
        <web.appId>*********</web.appId>
        <web.subEnv>fat</web.subEnv>
        <db.serviceUrl>https://ws.titan.fws.qa.nt.ctripcorp.com/titanservice/query</db.serviceUrl>
    </fws>
    <uat>
        <logging.server>collector.logging.fws.qa.nt.ctripcorp.com</logging.server>
        <logging.port>63100</logging.port>
        <logging.level>ERROR</logging.level>

        <web.fxConfigServiceUrl>http://ws.config.framework.fws.qa.nt.ctripcorp.com/configws/</web.fxConfigServiceUrl>
        <web.appId>*********</web.appId>
        <web.subEnv>fat</web.subEnv>
        <db.serviceUrl>https://ws.titan.fws.qa.nt.ctripcorp.com/titanservice/query</db.serviceUrl>
    </uat>
    <prd>
        <logging.server>collector.logging.sh.ctriptravel.com</logging.server>
        <logging.port>63100</logging.port>
        <logging.level>ERROR</logging.level>

        <web.fxConfigServiceUrl>http://ws.config.framework.ctripcorp.com/configws</web.fxConfigServiceUrl>
        <web.appId>*********</web.appId>
        <web.subEnv>fat</web.subEnv>
        <db.serviceUrl>https://ws.titan.ctripcorp.com/titanservice/query</db.serviceUrl>
    </prd>
</profile>
