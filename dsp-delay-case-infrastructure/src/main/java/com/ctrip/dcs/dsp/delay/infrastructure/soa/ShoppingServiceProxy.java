package com.ctrip.dcs.dsp.delay.infrastructure.soa;

import com.ctrip.dcs.price.interfaces.api.ShoppingSOAServiceClient;
import com.ctrip.dcs.price.interfaces.dto.trafficcontrol.CheckDriverTrafficControlRequestType;
import com.ctrip.dcs.price.interfaces.dto.trafficcontrol.CheckDriverTrafficControlResponseType;
import com.ctrip.igt.framework.soa.client.ServiceClient;

/**
 * <AUTHOR>
 */
@ServiceClient(ShoppingSOAServiceClient.class)
public interface ShoppingServiceProxy {

    CheckDriverTrafficControlResponseType checkDriverTrafficControl(CheckDriverTrafficControlRequestType request);
}
