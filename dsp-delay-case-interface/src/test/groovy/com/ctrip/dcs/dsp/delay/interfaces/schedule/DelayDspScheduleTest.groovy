package com.ctrip.dcs.dsp.delay.interfaces.schedule

import com.ctrip.dcs.dsp.delay.model.DelayDspTask
import com.ctrip.dcs.dsp.delay.mq.MessageProducer
import com.ctrip.dcs.dsp.delay.qconfig.DelayDspCommonQConfig
import com.ctrip.dcs.dsp.delay.repository.DelayDspTaskRepository
import com.ctrip.igt.framework.common.clogging.Logger
import qunar.tc.schedule.Parameter
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

/**
 * <AUTHOR>
 */
class DelayDspScheduleTest extends Specification {
    @Mock
    Logger logger
    @Mock
    DelayDspTaskRepository delayDspTaskRepository
    @Mock
    MessageProducer messageProducer
    @Mock
    DelayDspCommonQConfig delayDspCommonQConfig
    @Mock
    Parameter parameter
    @InjectMocks
    DelayDspSchedule delayDspSchedule

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test execute"() {
        given:
        when(delayDspTaskRepository.queryByTime(any())).thenReturn([new DelayDspTask(taskId: 1L, executeTimeDeadline: new Date())])
        when(delayDspCommonQConfig.getCheckTaskRunMillisecond()).thenReturn(1l)

        when:
        delayDspSchedule.execute(parameter)

        then:
        verify(delayDspTaskRepository).update(any(), any())
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme