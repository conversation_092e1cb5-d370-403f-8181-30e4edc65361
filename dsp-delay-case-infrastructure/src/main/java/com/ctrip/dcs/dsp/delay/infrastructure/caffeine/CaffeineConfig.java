package com.ctrip.dcs.dsp.delay.infrastructure.caffeine;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Configuration
public class CaffeineConfig {

    @Bean("caffeineCache")
    public Cache<String,String> caffeineCache() {
        return Caffeine.newBuilder()
                .initialCapacity(10_000)//初始大小
                .maximumSize(1_000_000)//最大数量
                .expireAfterWrite(60, TimeUnit.MINUTES)//过期时间
                .build();
    }
}
