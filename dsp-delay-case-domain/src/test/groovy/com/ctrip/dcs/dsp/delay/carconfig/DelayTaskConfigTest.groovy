package com.ctrip.dcs.dsp.delay.carconfig


import com.ctrip.dcs.dsp.delay.carconfig.key.DelayDspTaskKey
import com.ctrip.dcs.dsp.delay.carconfig.value.DelayDspTaskValue
import com.ctrip.dcs.dsp.delay.qconfig.DelayDspCommonQConfig
import com.ctrip.dcs.dsp.delay.util.DateUtil
import com.google.common.collect.ImmutableMap
import com.google.common.collect.Lists
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 */

class DelayTaskConfigTest extends Specification {




    def delayTaskConfig = new DelayTaskConfig()
    def delayTaskConfigV2 = Mock(DelayTaskConfigV2)
    def delayDspCommonQConfig = Mock(DelayDspCommonQConfig)

    def setup() {
        DelayTaskConfig.Config conf = new DelayTaskConfig.Config();
        conf.setKey(new DelayDspTaskKey(1, 117))
        conf.setValues(Lists.newArrayList(new DelayDspTaskValue(begin: "12:00", end: "14:00", hour: 10, type: "KM")))
        def map1 = ImmutableMap.builder()
                .put("1#0", conf)
                .build();
        delayTaskConfig.delayTaskConfigV2 = delayTaskConfigV2
        delayTaskConfig.delayDspCommonQConfig = delayDspCommonQConfig
        delayTaskConfig.map = map1
    }


    @Unroll
    def "test"() {

        given: "Mock数据"

        delayDspCommonQConfig.getDelayTaskConfigVersion() >> 0
        when: "执行校验方法"
        def res = delayTaskConfig.get(cityId, carTypeId, date,0)

        then: "验证校验结果"
        if (result == null) {
            res == null
        } else {
            res.getHour() == result
        }

        where:
        cityId | carTypeId | date                        || result
        1      | 117       | DateUtil.parseTime("13:00") || 10
        1      | 117       | DateUtil.parseTime("10:00") || null
        1      | 118       | DateUtil.parseTime("13:00") || null
        2      | 118       | DateUtil.parseTime("13:00") || null
    }

    @Unroll
    def "test getAnyType"() {

        given: "Mock数据"

        delayDspCommonQConfig.getDelayTaskConfigVersion() >> 0

        when: "执行校验方法"
        def res = delayTaskConfig.getAnyType(1, 117, DateUtil.parseTime("13:00"), type)

        then: "验证校验结果"
        if (result == null) {
            res == null
        } else {
            res.getHour() == result
        }

        where:
        type         || result
        []           || null
        ["KM"]       || 10
        ["DP"]       || null
        ["DP", "KM"] || null
    }



    @Unroll
    def "test2"() {

        given: "Mock数据"

        delayDspCommonQConfig.getDelayTaskConfigVersion() >> 1
        when: "执行校验方法"
        def res = delayTaskConfig.get(cityId, carTypeId, date,0)

        then: "验证校验结果"
        if (result == null) {
            res == null
        } else {
            res.getHour() == result
        }

        where:
        cityId | carTypeId | date                        || result
        1      | 117       | DateUtil.parseTime("13:00") || null
        1      | 117       | DateUtil.parseTime("10:00") || null
        1      | 118       | DateUtil.parseTime("13:00") || null
        2      | 118       | DateUtil.parseTime("13:00") || null
    }
}
