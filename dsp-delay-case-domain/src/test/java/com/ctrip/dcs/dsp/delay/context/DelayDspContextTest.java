package com.ctrip.dcs.dsp.delay.context;

import com.ctrip.dcs.dsp.delay.carconfig.value.DriverProfitDayBaselineValue;
import com.ctrip.dcs.dsp.delay.carconfig.value.HeadTailLimitValue;
import com.ctrip.dcs.dsp.delay.model.DelayDspOrder;
import com.ctrip.dcs.dsp.delay.model.DelayDspTask;
import com.ctrip.dcs.dsp.delay.model.DispatcherConfig;
import com.ctrip.dcs.dsp.delay.model.Driver;
import com.ctrip.dcs.dsp.delay.model.DriverAggregation;
import com.ctrip.dcs.dsp.delay.model.DriverScore;
import com.ctrip.dcs.dsp.delay.model.SupplyOrder;
import com.google.common.collect.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.List;

/**
 * <AUTHOR>
 */
@RunWith(PowerMockRunner.class)
public class DelayDspContextTest {

    @Test
    public void test() {
        DelayDspTask task = new DelayDspTask();
        task.setTaskId(1L);
        DelayDspOrder order = new DelayDspOrder();
        order.setOrderId("1");
        List<DelayDspOrder > orders = Lists.newArrayList(order);
        Driver driver = new Driver();
        driver.setDriverId("a");
        DriverScore score = new DriverScore();
        score.setTotalScore(100D);
        DelayDspOrder taken = new DelayDspOrder();
        taken.setOrderId("2");
        List<DelayDspOrder> takens = Lists.newArrayList(taken);
        List<SupplyOrder> supplyOrders = Lists.newArrayList();
        List< DriverAggregation > driverAggregations = Lists.newArrayList(new DriverAggregation(driver, score, takens, supplyOrders));
        DriverProfitDayBaselineValue driverProfitDayBaselineValue = new DriverProfitDayBaselineValue();
        driverProfitDayBaselineValue.setHighProfit(100);
        DispatcherConfig config = new DispatcherConfig(null, DriverProfitDayBaselineValue.DEFAULT, null, HeadTailLimitValue.DEFAULT);
        DelayDspContext context = new DelayDspContext(task, orders, driverAggregations, config);
        String s = context.toString();
        Assert.assertNotNull(s);
    }
}
