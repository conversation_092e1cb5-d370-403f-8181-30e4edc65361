package com.ctrip.dcs.dsp.delay.model;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class ConflictInfo {

    private DelayDspOrder prev;

    private DelayDspOrder next;

    private boolean conflict;

    private Route route;

    public ConflictInfo(DelayDspOrder prev, DelayDspOrder next) {
        this.prev = prev;
        this.next = next;
        this.conflict = true;
    }
}
