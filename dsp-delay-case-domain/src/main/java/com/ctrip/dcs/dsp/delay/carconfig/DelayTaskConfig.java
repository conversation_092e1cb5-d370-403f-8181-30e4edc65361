package com.ctrip.dcs.dsp.delay.carconfig;

import com.ctrip.dcs.dsp.delay.carconfig.key.DelayDspTaskKey;
import com.ctrip.dcs.dsp.delay.carconfig.value.DelayDspTaskValue;
import com.ctrip.dcs.dsp.delay.enums.DelayDspTaskType;
import com.ctrip.dcs.dsp.delay.enums.YesOrNo;
import com.ctrip.dcs.dsp.delay.qconfig.DelayDspCommonQConfig;
import com.ctrip.dcs.dsp.delay.util.JsonUtil;
import com.ctrip.dcs.dsp.delay.util.MetricsUtil;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.google.common.collect.Lists;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import qunar.tc.qconfig.client.spring.QConfig;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Component
public class DelayTaskConfig {

    private static final Logger logger = LoggerFactory.getLogger(DelayTaskConfig.class);

    public static final String DELAY_TASK_CONFIG_TYPE_DP = "DP";

    @QConfig("visualConfig_delayTaskConf_0.json")
    private HashMap<String, Config> map;
    @Autowired
    private DelayTaskConfigV2 delayTaskConfigV2;
    @Autowired
    private DelayDspCommonQConfig delayDspCommonQConfig;

    public DelayDspTaskValue get(Integer cityId, Integer carTypeId, Date date,Integer shortDisOrder) {
        List<String> types = Lists.newArrayList();
        if(Objects.equals(shortDisOrder, YesOrNo.YES.getCode())){
            types.add(DelayDspTaskType.SD.name());
        }else {
            types.add(DelayDspTaskType.KM.name());
            types.add(DelayDspTaskType.DP.name());
        }
        return get(cityId,carTypeId,date,types);
    }

    public DelayDspTaskValue get(Integer cityId, Integer carTypeId, Date date, List<String> types) {
        Integer delayTaskConfigVersion = delayDspCommonQConfig.getDelayTaskConfigVersion();
        logger.info("DelayTaskConfig_delayTaskConfigVersion", JsonUtil.toJson(delayTaskConfigVersion));
        if(delayTaskConfigVersion.equals(0)){
            return getOldDelayDspTaskValue(cityId, carTypeId, date, types);
        }else{
            return delayTaskConfigV2.get(cityId, date, types);
        }
    }

    public DelayDspTaskValue getAnyType(Integer cityId, Integer carTypeId, Date date, List<String> types) {
        DelayDspTaskValue value = get(cityId, carTypeId, date, types);
        return value;
    }

    private DelayDspTaskValue getOldDelayDspTaskValue(Integer cityId, Integer carTypeId, Date date, List<String> types) {
        DelayDspTaskKey key = new DelayDspTaskKey(cityId, carTypeId);
        Config config = map.get(key.toKey());
        if (config == null) {
            key = new DelayDspTaskKey(cityId, DelayDspTaskKey.ALL_CAR_TYPE);
            config = map.get(key.toKey());
        }
        if (config == null || config.getValues() == null) {
            MetricsUtil.recordValue("DelayTaskConfig.config.null", 1);
            return null;
        }
        for (DelayDspTaskValue value : config.getValues()) {
            //增加判断type 不为空，需要判断type
            if (!CollectionUtils.isEmpty(types)) {
                if (!types.contains(value.getType())) {
                    continue;
                }
            }
            if (value.match(date)) {
                return value;
            }
        }
        MetricsUtil.recordValue("DelayTaskConfig.config.null", 1);
        return null;
    }

    @Getter
    @Setter
    public static class Config {

        private DelayDspTaskKey key;

        private List<DelayDspTaskValue> values;
    }


}
