package com.ctrip.dcs.dsp.delay.infrastructure.soa;

import com.ctrip.dcs.shopping.common.interfaces.ShoppingCommonServiceClient;
import com.ctrip.dcs.shopping.common.interfaces.message.QueryChannelNumbersRequestType;
import com.ctrip.dcs.shopping.common.interfaces.message.QueryChannelNumbersResponseType;
import com.ctrip.igt.framework.soa.client.ServiceClient;

/**
 * <AUTHOR>
 */
@ServiceClient(ShoppingCommonServiceClient.class)
public interface ShoppingCommonServiceProxy {

    QueryChannelNumbersResponseType queryChannelNumbers(QueryChannelNumbersRequestType request);
}
