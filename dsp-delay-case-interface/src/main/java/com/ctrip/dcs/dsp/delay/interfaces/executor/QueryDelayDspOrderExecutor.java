package com.ctrip.dcs.dsp.delay.interfaces.executor;

import com.ctrip.dcs.dsp.application.service.DelayDspOrderApplicationService;
import com.ctrip.dcs.dsp.delay.service.interfaces.message.QueryDelayDspOrderRequestType;
import com.ctrip.dcs.dsp.delay.service.interfaces.message.QueryDelayDspOrderResponseType;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.annontation.ServiceLogTag;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@ServiceLogTag(tagKeys = {"orderId", "mainOrderId"})
public class QueryDelayDspOrderExecutor extends AbstractRpcExecutor<QueryDelayDspOrderRequestType, QueryDelayDspOrderResponseType> implements Validator<QueryDelayDspOrderRequestType> {

    @Autowired
    private DelayDspOrderApplicationService delayDspApplicationService;

    @Override
    public QueryDelayDspOrderResponseType execute(QueryDelayDspOrderRequestType request) {
        QueryDelayDspOrderResponseType response = delayDspApplicationService.queryDelayDspOrder(request);
        return ServiceResponseUtils.success(response);
    }
}
