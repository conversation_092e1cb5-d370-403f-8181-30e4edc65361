package com.ctrip.dcs.dsp.delay.util;

import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.metrics.MetricsFactory;
import io.dropwizard.metrics5.Gauge;
import io.dropwizard.metrics5.MetricName;
import io.dropwizard.metrics5.MetricRegistry;
import io.dropwizard.metrics5.ResetCounter;
import io.dropwizard.metrics5.ResetTimer;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
public class MetricsUtil {

    private static final Logger logger = LoggerFactory.getLogger(MetricsUtil.class);

    private static final String APP_ID = "100036007";

    private static final String METRICS_NAME_PREFIX = "dcs.dsp.delay.case.";

    private static final MetricRegistry METRIC_REGISTRY = MetricsFactory.getMetricRegistry();

    public static void recordValue(String name, long value) {
        String key = METRICS_NAME_PREFIX + name;
        try{
            Map<String,String> tags = new HashMap<>();
            tags.put("appId", APP_ID);
            MetricName metricName = new MetricName(key, tags);
            ResetCounter resetCounter = METRIC_REGISTRY.resetCounter(metricName);
            resetCounter.inc(value);
            resetCounter.setZero(true);
        }catch (Exception e){
            logger.warn("metric resetCount error",e);
        }
    }

    public static void recordValueWithTag(String name, long value, String tagKey, String tagValue) {
        String key = METRICS_NAME_PREFIX + name;
        try{
            Map<String,String> tags = new HashMap<>();
            tags.put(tagKey, tagValue);
            MetricName metricName = new MetricName(key, tags);
            ResetCounter resetCounter = METRIC_REGISTRY.resetCounter(metricName);
            resetCounter.inc(value);
            resetCounter.setZero(true);
        }catch (Exception e){
            logger.warn("metric resetCount error",e);
        }
    }

    public static void recordTimeWithTag(String name, long duration, String tagKey, String tagValue) {
        String key = METRICS_NAME_PREFIX + name;
        try{
            Map<String,String> tags = new HashMap<>();
            tags.put(tagKey, tagValue);
            MetricName metricName = new MetricName(key, tags);
            ResetTimer restTimer = METRIC_REGISTRY.resetTimer(metricName);
            restTimer.update(duration, TimeUnit.MILLISECONDS);
        }catch (Exception e){
            logger.warn("metric recordTimeWithTag error",e);
        }
    }

    public static void recordTime(String name, long duration) {
        String key = METRICS_NAME_PREFIX + name;
        try{
            Map<String,String> tags = new HashMap<>();
            tags.put("appId", APP_ID);
            MetricName metricName = new MetricName(key, tags);
            ResetTimer restTimer = METRIC_REGISTRY.resetTimer(metricName);
            restTimer.update(duration, TimeUnit.MILLISECONDS);
        }catch (Exception e){
            logger.warn("metric resetTime error",e);
        }
    }

    public static void gaugeValue(String name, Integer value) {
        String key = METRICS_NAME_PREFIX + name;
        try{
            Map<String,String> tags = new HashMap<>();
            tags.put("appId", APP_ID);
            MetricName metricName = new MetricName(key, tags);

            Gauge<Integer> guage = () -> value;
            METRIC_REGISTRY.gauge(metricName,() -> guage);
        }catch (Exception e){
            logger.warn("metric gaugeValue error",e);
        }
    }
}
