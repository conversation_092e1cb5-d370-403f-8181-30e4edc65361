package com.ctrip.dcs.dsp.delay.carconfig.value

import com.ctrip.dcs.dsp.delay.util.DateUtil
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 * @since 2024/8/19 11:50
 */
class SpecialDateValueTest extends Specification {


    @Unroll
    def "test match"() {
        given:
        when:
        def result = specialDateValue.match(date)

        then:
        result == expectedResult

        where:
        specialDateValue                                                                                                          | date                                      || expectedResult
        new SpecialDateValue(startTime: "10:00:00", endTime: "11:00:00", startDate: "2024-10-01", endDate: "2024-10-07", type: "once")  | DateUtil.parseDate("2024-08-19 10:10:10") || false
        new SpecialDateValue(startTime: "10:00:00", endTime: "11:00:00", startDate: "2024-10-01", endDate: "2024-10-07", type: "once")  | DateUtil.parseDate("2024-10-01 08:10:10") || false
        new SpecialDateValue(startTime: "10:00:00", endTime: "11:00:00", startDate: "2024-10-01", endDate: "2024-10-07", type: "once")  | DateUtil.parseDate("2024-10-01 10:10:10") || true
        new SpecialDateValue(startTime: "10:00:00", endTime: "11:00:00", startDate: "2024-10-01", endDate: "2024-10-07", type: "once")  | DateUtil.parseDate("2024-10-07 10:10:10") || true
        new SpecialDateValue(startTime: "10:00:00", endTime: "11:00:00", startDate: "2024-10-01", endDate: "2024-10-07", type: "once")  | DateUtil.parseDate("2024-10-07 12:10:10") || false
        new SpecialDateValue(startTime: "10:00:00", endTime: "11:00:00", startDate: "2024-10-01", endDate: "2024-10-07", type: "once")  | DateUtil.parseDate("2024-10-01 10:00:00") || true
        new SpecialDateValue(startTime: "10:00:00", endTime: "11:00:00", startDate: "2024-10-01", endDate: "2024-10-07", type: "once")  | DateUtil.parseDate("2024-10-01 11:00:00") || true
        new SpecialDateValue(startTime: "10:00:00", endTime: "11:00:00", startDate: "2024-10-01", endDate: "2024-10-07", type: "once")  | DateUtil.parseDate("2024-10-01 11:00:00") || true
        new SpecialDateValue(startTime: "00:00:00", endTime: "23:59:59", startDate: "2024-09-30", endDate: "2024-10-08", type: "once")  | DateUtil.parseDate("2024-09-30 00:00:00") || true
        new SpecialDateValue(startTime: "00:00:00", endTime: "23:59:59", startDate: "2024-09-30", endDate: "2024-10-08", type: "once")  | DateUtil.parseDate("2024-09-30 23:59:59") || true
        new SpecialDateValue(startTime: "00:00:00", endTime: "23:59:59", startDate: "2024-09-30", endDate: "2024-10-08", type: "once")  | DateUtil.parseDate("2024-10-08 00:00:00") || true
        new SpecialDateValue(startTime: "00:00:00", endTime: "23:59:59", startDate: "2024-09-30", endDate: "2024-10-08", type: "once")  | DateUtil.parseDate("2024-10-08 23:59:59") || true
        new SpecialDateValue(startTime: "00:00:00", endTime: "23:59:59", startDate: "2024-09-30", endDate: "2024-10-08", type: "once")  | DateUtil.parseDate("2024-10-09 23:59:59") || false
        new SpecialDateValue(startTime: "10:00:00", endTime: "11:00:00", startDate: "2024-10-01", endDate: "2024-10-07", type: "once")  | DateUtil.parseDate("2023-10-01 10:10:10") || false

        new SpecialDateValue(startTime: "10:00:00", endTime: "11:00:00", startDate: "2024-10-01", endDate: "2024-10-07", type: "years") | DateUtil.parseDate("2024-08-01 08:11:11") || false
        new SpecialDateValue(startTime: "10:00:00", endTime: "11:00:00", startDate: "2024-10-01", endDate: "2024-10-07", type: "years") | DateUtil.parseDate("2024-10-01 08:11:11") || false
        new SpecialDateValue(startTime: "10:00:00", endTime: "11:00:00", startDate: "2024-10-01", endDate: "2024-10-07", type: "years") | DateUtil.parseDate("2024-10-01 10:11:11") || true
        new SpecialDateValue(startTime: "10:00:00", endTime: "11:00:00", startDate: "2024-10-01", endDate: "2024-10-07", type: "years") | DateUtil.parseDate("2024-10-01 11:11:11") || false
        new SpecialDateValue(startTime: "10:00:00", endTime: "11:00:00", startDate: "2024-10-01", endDate: "2024-10-07", type: "years") | DateUtil.parseDate("2024-10-06 10:11:11") || true
        new SpecialDateValue(startTime: "00:00:00", endTime: "23:59:59", startDate: "2024-09-30", endDate: "2024-10-08", type: "years") | DateUtil.parseDate("2024-09-30 00:00:00") || true
        new SpecialDateValue(startTime: "00:00:00", endTime: "23:59:59", startDate: "2024-09-30", endDate: "2024-10-08", type: "years") | DateUtil.parseDate("2024-09-30 23:59:59") || true
        new SpecialDateValue(startTime: "00:00:00", endTime: "23:59:59", startDate: "2024-09-30", endDate: "2024-10-08", type: "years") | DateUtil.parseDate("2024-10-08 00:00:00") || true
        new SpecialDateValue(startTime: "00:00:00", endTime: "23:59:59", startDate: "2024-09-30", endDate: "2024-10-08", type: "years") | DateUtil.parseDate("2024-10-08 23:59:59") || true
        new SpecialDateValue(startTime: "00:00:00", endTime: "23:59:59", startDate: "2024-09-30", endDate: "2024-10-08", type: "years") | DateUtil.parseDate("2024-10-09 23:59:59") || false
        new SpecialDateValue(startTime: "10:00:00", endTime: "11:00:00", startDate: "2024-10-01", endDate: "2024-10-07", type: "years") | DateUtil.parseDate("2023-10-06 10:11:11") || true
    }
}
