package com.ctrip.dcs.dsp.application.service;

import com.ctrip.dcs.dsp.application.request.UpdateDelayDspOrderRequest;
import com.ctrip.dcs.dsp.delay.model.DelayDspTask;
import com.ctrip.dcs.dsp.delay.service.interfaces.message.*;

/**
 * <AUTHOR>
 */
public interface DelayDspOrderApplicationService {
    void insertTakenDelayOrder(String orderId, String orderSource);

    QueryDelayDspOrderResponseType queryDelayDspOrder(QueryDelayDspOrderRequestType request);

    void updateDelayDspOrder(UpdateDelayDspOrderRequest request);

    void updateDelayDspOrderForCancel(UpdateDelayDspOrderRequest request);


    Long increaseVirtualDspTakenFailCount(IncreaseVirtualDspTakenFailCountRequestType request);

    QueryDelayDspOrderBatchResponseType queryDelayDspOrderBatch(QueryDelayDspOrderBatchRequestType request);

    Boolean checkSpecialDate(Integer cityId, Integer carTypeId, String sysExpectBookTime);

    void updateTaskInfo(DelayDspTask delayDspTask);

}
