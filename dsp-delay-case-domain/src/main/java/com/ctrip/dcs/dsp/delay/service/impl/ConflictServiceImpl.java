package com.ctrip.dcs.dsp.delay.service.impl;

import com.ctrip.dcs.dsp.delay.conflict.CheckChain;
import com.ctrip.dcs.dsp.delay.conflict.CheckCode;
import com.ctrip.dcs.dsp.delay.conflict.CheckType;
import com.ctrip.dcs.dsp.delay.conflict.ConflictContext;
import com.ctrip.dcs.dsp.delay.context.DelayDspContext;
import com.ctrip.dcs.dsp.delay.model.DelayDspOrder;
import com.ctrip.dcs.dsp.delay.model.DriverAggregation;
import com.ctrip.dcs.dsp.delay.service.ConflictService;
import com.ctrip.dcs.dsp.delay.util.MetricsUtil;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;


/**
 * <AUTHOR>
 */
@Component
public class ConflictServiceImpl implements ConflictService {

    private static final Logger logger = LoggerFactory.getLogger(ConflictServiceImpl.class);

    @Autowired
    private CheckChain checkChain;

    @Override
    public CheckCode checkConflict(DelayDspContext dspContext, DriverAggregation driverAggregation, DelayDspOrder prev, DelayDspOrder next) {
        ConflictContext context = new ConflictContext.Builder()
                .withDelayDspTask(dspContext.getDelayDspTask())
                .withConfig(dspContext.getConfig())
                .withDriverAggregation(driverAggregation)
                .withPrev(prev)
                .withNext(next)
                .withCheckType(CheckType.PAIR)
                .build();
        CheckCode check = checkChain.check(context);
        MetricsUtil.recordValue("conflict.check.match." + check.name(), 1); // 订单匹配检查埋点
        logger.info("check conflict", "driver:{}, prev:{}, next:{}, checkCode:{}",
                driverAggregation.getDriver().getDriverId(), prev.getOrderId(), next.getOrderId(), check.name());
        return check;
    }

    @Override
    public CheckCode checkConflict(DelayDspContext dspContext, DriverAggregation driverAggregation, DelayDspOrder next) {
        ConflictContext context = new ConflictContext.Builder()
                .withDelayDspTask(dspContext.getDelayDspTask())
                .withConfig(dspContext.getConfig())
                .withDriverAggregation(driverAggregation)
                .withNext(next)
                .withCheckType(CheckType.SINGLE)
                .build();
        CheckCode check = checkChain.check(context);
        MetricsUtil.recordValue("conflict.check.init." + check.name(), 1); // 初始化收益检查
        logger.info("check conflict", "driver:{}, next:{}, checkCode:{}",
                driverAggregation.getDriver().getDriverId(), next.getOrderId(), check.name());
        return check;
    }

    @Override
    public CheckCode checkConflict(DelayDspContext dspContext, DriverAggregation driverAggregation, List<DelayDspOrder> list) {
        ConflictContext context = new ConflictContext.Builder()
                .withDelayDspTask(dspContext.getDelayDspTask())
                .withConfig(dspContext.getConfig())
                .withDriverAggregation(driverAggregation)
                .withList(list)
                .withCheckType(CheckType.LIST)
                .build();
        CheckCode check = checkChain.check(context);
        MetricsUtil.recordValue("conflict.check.taken" + check.name(), 1);   // 派单结果检查埋点
        return check;
    }
}
