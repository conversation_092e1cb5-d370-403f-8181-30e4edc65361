package com.ctrip.dcs.dsp.delay.interfaces.executor

import com.ctrip.dcs.dsp.delay.model.DelayDspConfigResInfoDTO
import com.ctrip.dcs.dsp.delay.service.DelayTaskService
import com.ctrip.dcs.dsp.delay.service.interfaces.message.QueryDelayDspConfigInfoRequestType
import com.ctrip.dcs.dsp.delay.service.interfaces.message.QueryDelayDspConfigResInfoResponseType
import com.ctrip.dcs.dsp.transport.inventory.service.api.dto.DelayDspConfigInfo
import com.google.common.collect.Lists
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

class QueryDelayDspConfigExecutorTest extends Specification {
    @Mock
    DelayTaskService delayTaskService
    @InjectMocks
    QueryDelayDspConfigExecutor queryDelayDspConfigExecutor

    def setup() {
        MockitoAnnotations.initMocks(this)
    }


    def "test execute"() {
        given:
        when(delayTaskService.queryDelayDspConfigList(any())).thenReturn([new DelayDspConfigResInfoDTO(cityId: 1l, carTypeId: 0, sysExpectBookTime: "sysExpectBookTime", taskDeadTime: "taskDeadTime", taskEndTime: "taskEndTime")])

        when:
        QueryDelayDspConfigInfoRequestType requestType = new QueryDelayDspConfigInfoRequestType();
        List<DelayDspConfigInfo> delayDspConfigList = Lists.newArrayList();
        DelayDspConfigInfo delayDspConfigInfo = new DelayDspConfigInfo();
        delayDspConfigInfo.setCityId(1L);
        delayDspConfigInfo.setCarTypeId(117);
        delayDspConfigInfo.setSysExpectBookTime("2023-12-11 10:00:00");
        delayDspConfigList.add(delayDspConfigInfo);
        requestType.setDelayDspConfigList(delayDspConfigList);
        QueryDelayDspConfigResInfoResponseType result = queryDelayDspConfigExecutor.execute(requestType)

        then:
        result != null
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme