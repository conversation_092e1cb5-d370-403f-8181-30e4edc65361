package com.ctrip.dcs.dsp.delay.core;

import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;

/**
 * <AUTHOR>
 */
public class KM {

    private static final Logger logger = LoggerFactory.getLogger(KM.class);

    private static final int INFINITE = 0x6fffffff;

    //假设graph的行是顶点X集合（其中的顶点简称X顶点），列是顶点Y集合（其中的顶点简称Y顶点）
    private int[][] graph;
    //在每次循环中每个X顶点是否访问过
    private boolean[] xUsed;
    //在每次循环中每个Y顶点是否访问过
    private boolean[] yUsed;
    //每个Y顶点匹配的X顶点，即第i个Y顶点匹配的是第match[i]个X顶点
    private int[] match;
    //图的大小为len*len
    private int len;
    //与顶标变化相关
    private int[] less;
    //每个X顶点的顶标
    private int[] X;
    //每个Y顶点的顶标，初始化为0
    private int[] Y;

    public KM(int[][] graph) {
        this.graph = graph;
        this.len = graph.length;
        this.match = new int[len];
        this.xUsed = new boolean[len];
        this.yUsed = new boolean[len];
        this.less = new int[len];
        this.X = new int[len];
        this.Y = new int[len];
        for (int i = 0; i < len; i++) {
            match[i] = -1;
        }
        //初始化每个X顶点的顶标为与之相连的边中最大的权
        for (int k = 0; k < len; k++) {
            X[k] = graph[k][0];
            for (int l = 0; l < len; l++) {
                X[k] = Math.max(X[k], graph[k][l]);
            }
        }
    }

    public int[] execute() {
        //遍历每个X顶点
        for (int i = 0; i < len; i++) {
            for (int j = 0; j < len; j++) {
                less[j] = INFINITE;
            }
            //寻找能与X顶点匹配的Y顶点，如果找不到就降低X顶点的顶标继续寻找
            while (true) {
                for (int j = 0; j < len; j++) {
                    xUsed[j] = false;
                    yUsed[j] = false;
                }
                if (dfs(i)) {
                    break;  //寻找到匹配的Y顶点，退出
                }
                //如果没有找到能够匹配的Y顶点，则降低X顶点的顶标，提升Y顶点的顶标，再次循环
                //diff是顶标变化的数值
                int diff = INFINITE;
                for (int j = 0; j < len; j++) {
                    if (!yUsed[j]) {
                        diff = Math.min(diff, less[j]);
                    }
                }
                //diff等于为了使该顶点X能够匹配到一个Y顶点，其X的顶标所需要降低的最小值
                //更新顶标
                for (int j = 0; j < len; j++) {
                    if (xUsed[j]) {
                        X[j] -= diff;
                    }
                    if (yUsed[j]) {
                        Y[j] += diff;
                    } else {
                        less[j] -= diff;
                    }
                }
            }
        }

        //匹配完成，可以输出结果
        int res = 0;
        for (int i = 0; i < len; i++) {
            logger.info("km match", "i:{}, matched:{}, value:{}", i, match[i], graph[match[i]][i]);
            res += graph[match[i]][i];
        }
        logger.info("km match result", "max match:{}", res);
        return match;
    }

    private boolean dfs(int i) {
        //设置这个X顶点在此轮循环中被访问过
        xUsed[i] = true;

        //对于这个X顶点，遍历每个Y顶点
        for (int j = 0; j < len; j++) {
            if (yUsed[j]) {
                //每轮循环中每个Y顶点只访问一次
                continue;
            }
            //KM算法的顶标变化公式
            int gap = X[i] + Y[j] - graph[i][j];

            //只有X顶点的顶标加上Y顶点的顶标等于graph中它们之间的边的权时才能匹配成功
            if (gap == 0) {
                yUsed[j] = true;
                if (match[j] == -1 || dfs(match[j])) {
                    match[j] = i;
                    return true;
                }
            } else {
                less[j] = Math.min(less[j], gap);
            }
        }

        return false;
    }
}
