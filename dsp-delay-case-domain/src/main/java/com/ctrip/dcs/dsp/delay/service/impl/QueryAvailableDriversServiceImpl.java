package com.ctrip.dcs.dsp.delay.service.impl;

import com.ctrip.dcs.dsp.delay.gateway.DriverGateway;
import com.ctrip.dcs.dsp.delay.gateway.SelfDispatcherOrderGateway;
import com.ctrip.dcs.dsp.delay.model.AvailableDriver;
import com.ctrip.dcs.dsp.delay.model.AvailableDriverDetail;
import com.ctrip.dcs.dsp.delay.model.DelayDspOrder;
import com.ctrip.dcs.dsp.delay.model.Driver;
import com.ctrip.dcs.dsp.delay.service.QueryAvailableDriversService;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.google.common.collect.Lists;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
public class QueryAvailableDriversServiceImpl implements QueryAvailableDriversService {

    private static final Logger logger = LoggerFactory.getLogger(QueryAvailableDriversServiceImpl.class);

    @Autowired
    private SelfDispatcherOrderGateway dispatcherOrderGateway;

    @Autowired
    private DriverGateway driverGateway;

    @Override
    public AvailableDriver query(List<DelayDspOrder> orders) {
        List<AvailableDriverDetail> details = Lists.newArrayList();
        for (DelayDspOrder order : orders) {
            try {
                Map<String, String> map = dispatcherOrderGateway.queryAvailableDrivers(order.getOrderId(), order.getDuid());
                if (MapUtils.isEmpty(map)) {
                    continue;
                }
                List<Driver> drivers = driverGateway.query(map.keySet());
                List<AvailableDriverDetail> list = drivers.stream().map(d -> {
                    AvailableDriverDetail detail = new AvailableDriverDetail();
                    detail.setDriver(d);
                    detail.setOrder(order);
                    detail.setValue(map.get(d.getDriverId()));
                    return detail;
                }).collect(Collectors.toList());
                details.addAll(list);
            } catch (Exception e) {
                logger.error(e);
            }
        }
        return new AvailableDriver(details);
    }
}
