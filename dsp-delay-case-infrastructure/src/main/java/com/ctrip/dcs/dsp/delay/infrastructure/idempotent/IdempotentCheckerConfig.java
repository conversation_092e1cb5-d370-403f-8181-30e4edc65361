package com.ctrip.dcs.dsp.delay.infrastructure.idempotent;

import com.ctrip.arch.distlock.redis.RedisDistributedLockService;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import qunar.tc.qmq.consumer.idempotent.CRedisIdempotentChecker;

/**
 * <AUTHOR>
 */
@Configuration
public class IdempotentCheckerConfig {

    @Bean(name = "redisIdempotentChecker")
    public CRedisIdempotentChecker redisIdempotentChecker() {
        return new CRedisIdempotentChecker("DCSDelayDispatchtrocks");
    }

    @Bean("redisDistributedLock")
    public RedisDistributedLockService getDistributedLock() {
        return new RedisDistributedLockService("dcs.self.dsp.order");
    }
}
