package com.ctrip.dcs.dsp.delay.validator;

import com.ctrip.dcs.dsp.delay.model.DriverAggregation;

public class ValidatorDTO {

    private ValidatorCode code = ValidatorCode.ERROR;


    private DriverAggregation driverAggregations;

    public ValidatorDTO(ValidatorCode code) {
        this.code = code;
    }

    public ValidatorDTO(ValidatorCode code, DriverAggregation driverAggregations) {
        this.code = code;
        this.driverAggregations = driverAggregations;
    }

    public ValidatorDTO() {
    }

    public ValidatorCode getCode() {
        return code;
    }

    public void setCode(ValidatorCode code) {
        this.code = code;
    }

    public DriverAggregation getDriverAggregations() {
        return driverAggregations;
    }

    public void setDriverAggregations(DriverAggregation driverAggregations) {
        this.driverAggregations = driverAggregations;
    }
}
