package com.ctrip.dcs.dsp.delay.infrastructure.geteway;

import com.ctrip.dcs.dsp.delay.enums.SupplyOrderStatus;
import com.ctrip.dcs.dsp.delay.enums.UdlEnum;
import com.ctrip.dcs.dsp.delay.gateway.SelfDriverOrderGateway;
import com.ctrip.dcs.dsp.delay.infrastructure.converter.DriverOrderConverter;
import com.ctrip.dcs.dsp.delay.infrastructure.dto.DriverUdlVO;
import com.ctrip.dcs.dsp.delay.infrastructure.soa.SelfDriverOrderServiceProxy;
import com.ctrip.dcs.dsp.delay.infrastructure.soa.SelfOrderQueryServiceProxy;
import com.ctrip.dcs.dsp.delay.infrastructure.soa.TmsTransportServiceProxy;
import com.ctrip.dcs.dsp.delay.infrastructure.util.TmsTransportServiceClientUtil;
import com.ctrip.dcs.dsp.delay.model.DelayDspOrder;
import com.ctrip.dcs.dsp.delay.model.Driver;
import com.ctrip.dcs.dsp.delay.qconfig.DelayDspCommonQConfig;
import com.ctrip.dcs.dsp.delay.util.MetricsUtil;
import com.ctrip.dcs.oms.driver.order.domain.api.drvOrder.CreateDriverOrderRequestType;
import com.ctrip.dcs.oms.driver.order.domain.api.drvOrder.CreateDriverOrderResponseType;
import com.ctrip.dcs.self.order.query.api.QueryOrderDetailRequestType;
import com.ctrip.dcs.self.order.query.api.QueryOrderDetailResponseType;
import com.ctrip.dcs.self.order.query.dto.BaseDetail;
import com.ctrip.dcs.self.order.query.dto.DataSwitch;
import com.ctrip.dcs.self.order.query.dto.OrderDetail;
import com.ctrip.dcs.tms.transport.api.model.DriverInfo;
import com.ctrip.dcs.tms.transport.api.model.DriverInfoSOARequestType;
import com.ctrip.dcs.tms.transport.api.model.DriverInfoSOAResponseType;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.google.common.base.Joiner;
import com.google.common.collect.Sets;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Component
public class SelfDriverOrderGatewayImpl implements SelfDriverOrderGateway {
    private static Logger logger = LoggerFactory.getLogger(SelfDriverOrderGatewayImpl.class);

    @Autowired
    private SelfDriverOrderServiceProxy driverOrderServiceProxy;

    @Autowired
    private DriverOrderConverter driverOrderConverter;

    @Autowired
    private SelfOrderQueryServiceProxy orderQueryServiceProxy;

    @Autowired
    private TmsTransportServiceProxy tmsTransportServiceProxy;
    
    @Resource
    DriverGatewayImpl driverGateway;
    @Resource
    DelayDspCommonQConfig delayDspCommonQConfig;
    @Resource
    TmsTransportServiceClientUtil tmsTransportServiceClientUtil;

    @Override
    public String create(DelayDspOrder order, Driver driver) {
        OrderDetail orderDetail = queryOrder(order.getOrderId());
        DriverInfo driverInfo = queryDriver(driver.getDriverId());
        if (Objects.isNull(orderDetail) || Objects.isNull(driverInfo)) {
            MetricsUtil.recordValue("createDriverOrder.pare.failed", 1);
            return StringUtils.EMPTY;
        }
        Integer orderStatus = Optional.of(orderDetail).map(OrderDetail::getBaseDetail).map(BaseDetail::getOrderStatus).orElse(900);
        if (!Objects.equals(orderStatus, 210)) {
            return StringUtils.EMPTY;
        }
        CreateDriverOrderRequestType requestType = driverOrderConverter.toCreateDriverOrderRequestType(orderDetail, driverInfo);
        CreateDriverOrderResponseType responseType = driverOrderServiceProxy.createDriverOrder(requestType);
        return Optional.ofNullable(responseType).map(CreateDriverOrderResponseType::getDrvOrderId).orElse(StringUtils.EMPTY);
    }

    @Override
    public String createById(String dspOrderId, String driverId) {
        OrderDetail orderDetail = queryOrder(dspOrderId);
        DriverInfo driverInfo = queryDriver(driverId);
        if (Objects.isNull(orderDetail) || Objects.isNull(driverInfo)) {
            MetricsUtil.recordValue("createDriverOrder.pare.failed", 1);
            return StringUtils.EMPTY;
        }
        CreateDriverOrderRequestType requestType = driverOrderConverter.toCreateDriverOrderRequestType(orderDetail, driverInfo);
        CreateDriverOrderResponseType responseType = driverOrderServiceProxy.createDriverOrder(requestType);
        return Optional.ofNullable(responseType).map(CreateDriverOrderResponseType::getDrvOrderId).orElse(StringUtils.EMPTY);
    }

    private OrderDetail queryOrder(String dspOrderId) {
        QueryOrderDetailRequestType requestType = new QueryOrderDetailRequestType();
        requestType.setDspOrderId(dspOrderId);
        DataSwitch dataSwitch = new DataSwitch();
        dataSwitch.setBaseDetailSwitch(true);
        dataSwitch.setConfirmRecordSwitch(true);
        dataSwitch.setWayPointsSwitch(true);
        dataSwitch.setEstimatedCostInfoSwitch(true);
        dataSwitch.setOrderPackageServiceSwitch(true);
        requestType.setDataSwitch(dataSwitch);
        QueryOrderDetailResponseType responseType = orderQueryServiceProxy.queryOrderDetail(requestType);
        return Optional.ofNullable(responseType).map(QueryOrderDetailResponseType::getOrderDetail).orElse(null);
    }

    public DriverInfo queryDriver(String driverId) {
        //上云开关开启，则传入udl
        if (delayDspCommonQConfig.isSelfCloudSwitch()) {
            Map<Long, DriverUdlVO> udlMap = driverGateway.getDrvUdlMap(Sets.newHashSet(Long.valueOf(driverId)));
            String udl = Optional.ofNullable(udlMap.get(Long.valueOf(driverId))).map(DriverUdlVO::getUdl).orElse(UdlEnum.SHA.getValue());
            logger.info("queryDriver_udl", "udl:" + udl);
            tmsTransportServiceClientUtil.setDrvUdl(udl);
        }
        DriverInfoSOARequestType request = new DriverInfoSOARequestType();
        request.setDriverIds(driverId);
        DriverInfoSOAResponseType response = tmsTransportServiceProxy.queryDriver(request);
        return Optional.ofNullable(response).map(DriverInfoSOAResponseType::getDriverList).orElse(Collections.emptyList()).stream().findFirst().orElse(null);
    }
}
