package com.ctrip.dcs.dsp.delay.infrastructure.converter;
import java.math.BigDecimal;

import com.ctrip.dcs.self.order.query.api.QueryOrderDetailForScheduleResponseType;
import com.ctrip.dcs.self.order.query.dto.*;
import java.util.List;

import com.ctrip.dcs.self.order.query.dto.OrderStandardRecord;
import com.ctrip.dcs.self.order.query.dto.CancelRecord;
import com.ctrip.igt.framework.common.jackson.JacksonUtil;
import com.google.common.collect.Lists;
import com.ctriposs.baiji.rpc.common.types.ResponseStatusType;
import com.ctrip.igt.ResponseResult;

import com.ctrip.dcs.dsp.delay.model.SupplyOrder;
import com.ctrip.dcs.self.order.query.api.QueryOrderDetailResponseType;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.powermock.modules.junit4.PowerMockRunner;

/**
 * <AUTHOR>
 */
@RunWith(PowerMockRunner.class)
public class SupplyOrderConverterTest {

    @InjectMocks
    private SupplyOrderConverter converter;

    @Test
    public void test() {

        String s = "{\"ResponseStatus\":{\"Timestamp\":\"/Date(1725431042615+0800)/\",\"Ack\":\"Success\",\"Errors\":[],\"Build\":null,\"Version\":null,\"Extension\":[]},\"responseResult\":{\"success\":true,\"returnCode\":\"200\",\"returnMessage\":\"服务执行成功\"},\"orderDetail\":{\"baseDetail\":{\"userOrderId\":\"***********\",\"supplyOrderId\":\"191630232049483849\",\"dspOrderId\":\"*****************\",\"driverOrderId\":null,\"orderVersion\":5,\"confirmRecordId\":3094739390609834165,\"orderStatus\":240,\"oldOrderStatus\":3,\"oldOrderStatusDetail\":0,\"orderSource\":1,\"productType\":0,\"productCode\":\"\",\"productName\":\"\",\"categoryCode\":\"airport_dropoff\",\"serviceType\":1,\"cityId\":2,\"fromCityId\":2,\"toCityId\":2,\"vehicleGroupId\":118,\"estimatedUseTime\":\"2024-09-16 16:20:00\",\"estimatedUseTimeBj\":\"2024-09-16 16:20:00\",\"predicServiceStopTimeBj\":\"2024-09-16 16:35:00\",\"lastConfirmTime\":\"2024-09-15 16:20:00\",\"lastConfirmTimeBj\":\"2024-09-15 16:20:00\",\"lastConfirmCarTime\":\"2024-09-16 14:20:00\",\"lastConfirmCarTimeBj\":\"2024-09-16 14:20:00\",\"connectMode\":1,\"salesMode\":5,\"useDays\":1.00,\"estimatedKm\":7.00,\"estimatedMin\":15.00,\"actualKm\":0.00,\"actualMin\":0.00,\"priceMark\":\"103412\",\"serviceProviderId\":1,\"supplierId\":30804,\"countryId\":1,\"locale\":\"zh-cn\",\"skuId\":49737,\"cancelRule\":\"{\\\"timeType\\\":3,\\\"freeCancel\\\":true,\\\"timeZone\\\":8,\\\"timeInterval\\\":0,\\\"timeIntervalUnit\\\":\\\"MINUTES\\\",\\\"timeIntervalUnitDesc\\\":\\\"分钟\\\",\\\"cancelFeeRate\\\":50,\\\"freeCancelTime\\\":\\\"2024-09-16 16:20:00\\\",\\\"freeCancelTimeBJ\\\":\\\"2024-09-16 16:20:00\\\"}\",\"waitingRule\":\"[{\\\"referenceTimeType\\\":1,\\\"type\\\":0,\\\"waitingTime\\\":10,\\\"waitingTimeUnit\\\":\\\"MINUTES\\\",\\\"waitingTimeUnitDesc\\\":\\\"分钟\\\",\\\"amount\\\":1.00,\\\"currency\\\":\\\"CNY\\\",\\\"overWaitingTime\\\":1,\\\"overWaitingTimeUnit\\\":\\\"MINUTES\\\",\\\"overWaitingTimeUnitDesc\\\":\\\"分钟\\\",\\\"active\\\":true,\\\"freeWait\\\":false}]\",\"priceResultCode\":\"34vk_8iat_0_9df4,9dfi,9dfp,9dfb_0_0_1_0_0_3\",\"detailSnapShotid\":\"\",\"xproductInfo\":\"[{\\\"categoryCode\\\":\\\"pick_up_card\\\"}]\",\"spContractInfo\":\"{\\\"contact\\\":{\\\"id\\\":782282,\\\"supplierId\\\":30804,\\\"serviceProviderId\\\":1,\\\"code\\\":\\\"SHCT2022072100057778\\\",\\\"name\\\":\\\"用车平台协议模板 随县统计局555）\\\",\\\"status\\\":8,\\\"statusDesc\\\":\\\"已签署\\\",\\\"salesMode\\\":2,\\\"salesModeDesc\\\":\\\"零售\\\",\\\"settlementMethod\\\":2,\\\"priceTerms\\\":2,\\\"tocSettlementPeriodType\\\":\\\"E\\\",\\\"tobSettlementPeriodType\\\":\\\"\\\",\\\"partA\\\":\\\"上海华程西南国际旅行社有限公司\\\",\\\"type\\\":1,\\\"tocSettlementDateType\\\":1,\\\"tobSettlementDateType\\\":0,\\\"paySiteId\\\":2,\\\"partCPaySiteId\\\":0,\\\"invoicePattern\\\":2},\\\"supplier\\\":{\\\"id\\\":30804,\\\"name\\\":\\\"随县统计局555）\\\"},\\\"serviceProviderBaseInfo\\\":{\\\"id\\\":1,\\\"carrierLocalName\\\":\\\"天津海豚出游科技有限公司\\\",\\\"salesMode\\\":5}}\",\"dspStrategyStr\":\"{\\\"dspStrategyId\\\":1003,\\\"skuType\\\":1,\\\"dspData\\\":\\\"{\\\\\\\"skuType\\\\\\\":\\\\\\\"1\\\\\\\",\\\\\\\"beginSeconds\\\\\\\":\\\\\\\"0\\\\\\\",\\\\\\\"useSeconds\\\\\\\":\\\\\\\"0\\\\\\\",\\\\\\\"addPriceRatio\\\\\\\":\\\\\\\"0\\\\\\\",\\\\\\\"addPriceMoney\\\\\\\":\\\\\\\"0\\\\\\\",\\\\\\\"maxAddPriceRatioConf\\\\\\\":\\\\\\\"0\\\\\\\",\\\\\\\"maxAddPriceMoneyConf\\\\\\\":\\\\\\\"0\\\\\\\",\\\\\\\"addPriceRatioConf\\\\\\\":\\\\\\\"0\\\\\\\",\\\\\\\"addPriceMoneyConf\\\\\\\":\\\\\\\"0\\\\\\\",\\\\\\\"reason\\\\\\\":99,\\\\\\\"sceneId\\\\\\\":\\\\\\\"2022\\\\\\\",\\\\\\\"canDown\\\\\\\":false,\\\\\\\"humanDsp\\\\\\\":false,\\\\\\\"bookFirstFlag\\\\\\\":true,\\\\\\\"dspStrategyId\\\\\\\":1003,\\\\\\\"retryNum\\\\\\\":0,\\\\\\\"grade\\\\\\\":\\\\\\\"1_1\\\\\\\"}\\\",\\\"bookFirstFlag\\\":true}\",\"orderCarTypeId\":118,\"drvId\":null,\"drvName\":null,\"drvPhone\":null,\"drvPhoneAreaCode\":null,\"drvCoopMode\":null,\"drvLanguageCodeList\":null,\"carId\":null,\"carTypeId\":null,\"carLicense\":null,\"carColorId\":null,\"carBrandId\":null,\"carSeriesId\":null,\"transportGroupId\":null,\"transportGroupMode\":null,\"transportGroupName\":null,\"confirmMoneyTimeLocal\":null,\"confirmMoneyTimeBj\":null,\"finishTimeBj\":null,\"cancelTimeLocal\":null,\"cancelTimeBj\":null,\"bizAreaType\":32,\"adultCount\":2,\"childCount\":0,\"bagCount\":0,\"maxBagCount\":null,\"coordType\":\"GCJ02\",\"fromLongitude\":121.352141,\"fromLatitude\":31.222065,\"toLongitude\":121.347566,\"toLatitude\":31.194807,\"takenTimeBj\":null,\"targetId\":\"SHA\",\"terminalId\":\"10002\",\"fixedLocationType\":2,\"fixedPosition\":\"SHA\",\"premiumOrderFlag\":0,\"dspOrderUpdateTime\":\"2024-09-04 14:23:57\",\"dspOrderCreateTime\":\"2024-09-04 14:23:32\",\"drvOrderUpdateTime\":null,\"drvOrderCreateTime\":null,\"newPayProcess\":0,\"uid\":\"*********\",\"distributionChannelId\":5,\"giveFreeTime\":0,\"highUser\":1,\"dispatchLimitType\":null,\"allotDriverTimeBj\":null,\"fromPoiDTO\":{\"depPoiCode\":\"bb57cf24e13ece3e43afe640\",\"depPoiSourceType\":\"Baidu\",\"actualFromLongitude\":121.352141,\"actualFromLatitude\":31.222065,\"actualFromCoordsys\":\"GCJ02\",\"fromAddress\":\"长宁区 金钟路968号\",\"fromName\":\"凌空SOHO\"},\"toPoiDTO\":{\"arrivePoiCode\":\"10002\",\"arrivePoiSourceType\":\"basic_tml\",\"actualToLongitude\":121.347566,\"actualToLatitude\":31.194807,\"actualToCoordsys\":\"GCJ02\",\"toAddress\":\"虹桥国际机场 T1\",\"toName\":\"虹桥国际机场 T1\"},\"highGradeOrder\":1,\"timeZone\":8.00,\"orderDetailExtendInfo\":\"{\\\"useTime\\\":\\\"2024-09-16 16:20:00\\\",\\\"orderSysType\\\":1,\\\"crossBorderType\\\":1}\",\"rushOrder\":0,\"driverOfficialStatus\":null,\"vehicleOfficialStatus\":null,\"orderSysType\":1},\"estimatedCostInfo\":{\"salesAmount\":146.000000,\"costAmount\":136.000000,\"tollFee\":0.000000,\"parkingFee\":20.000000,\"noCommisionAmount\":20.000000,\"supplierBornePrice\":\"null\",\"premiumPrice\":\"[{\\\"strategyId\\\":\\\"3\\\",\\\"feeAmount\\\":10.00},{\\\"strategyId\\\":\\\"6\\\",\\\"feeAmount\\\":0}]\",\"userCurrency\":\"CNY\",\"supplierCurrency\":\"CNY\",\"sc2cnyExchangeRate\":1.000000,\"cny2ucExchangeRate\":1.000000,\"sc2ucExchangeRate\":1.000000,\"dspAddPrice\":0.000000,\"waypointAmount\":0.000000,\"emptyFareFee\":0.00,\"driverCurrency\":\"CNY\",\"sc2driverExchangeRate\":1.000000,\"uc2driverExchangeRate\":1.000000,\"settleToDriver\":null,\"cancelFineRate\":0.00},\"actualCostInfo\":{\"id\":515544,\"drvOrderId\":\"D94863437939146802\",\"dspOrderId\":\"*****************\",\"userOrderId\":\"***********\",\"supplyOrderId\":\"191630232049483849\",\"drvId\":3452141,\"categoryCode\":\"airport_dropoff\",\"overDistanceLength\":0,\"rewardAmount\":1.00,\"waitFee\":0.00,\"overDistanceFee\":0.00,\"overTimeLength\":0,\"overTimeFee\":0.00,\"parkingFee\":0.00,\"accommodationFee\":0.00,\"tollFee\":0,\"auditStatus\":-1,\"auditEffectiveTimeBj\":null,\"payStatus\":-1,\"extraFeeStatus\":3,\"currentExchangeRate\":null,\"userExchangeRate\":null,\"vendorExchangeRate\":null,\"userNeedPayInfo\":\"\",\"confirmTimeLocal\":null,\"confirmTimeBj\":null,\"feeExpirationDateBj\":null,\"feeExpirationDateLocal\":null,\"operator\":\"SYSTEM\",\"welfareMoney\":0.00,\"datachangeCreatetime\":null,\"datachangeLasttime\":null,\"extendRule\":\"{\\\"vendorOverDurationFee\\\":0,\\\"vendorOverKilometreFee\\\":0}\",\"commissionRule\":\"\"},\"sopRecords\":null,\"orderStandardRecord\":null,\"orderPackageServices\":null,\"cancelRecord\":null,\"breakAppointmentRecord\":null,\"orderConfirmRecord\":{\"id\":255354,\"recordId\":3094739390609834165,\"dspOrderId\":\"*****************\",\"supplyOrderId\":\"191630232049483849\",\"userOrderId\":\"***********\",\"categoryCode\":\"airport_dropoff\",\"driverOrderId\":\"D94863437939146802\",\"groupId\":0,\"confirmType\":\"40\",\"confirmTime\":null,\"confirmEvent\":10,\"duid\":\"0-0-0-0-0-0-0-0-0-0\",\"dspRewardAmount\":\"\",\"takenType\":0,\"serviceProviderId\":1,\"serviceProviderPhone\":\"\",\"serviceProviderLanguage\":\"\",\"supplierId\":30804,\"transportGroupId\":186,\"dispatcherPhone\":\"189Pi6i0264\",\"dispatcherPhoneCode\":\"86\",\"dispatcherLanguage\":\"hk\",\"dispatcherName\":\"范德萨\",\"dispatcherEmail\":\"\",\"dispatcherContactType\":\"\",\"dispatcherContactAccount\":\"wechat\",\"overseaDispatcherPhone\":\"\",\"overseaDispatcherPhoneCode\":\"\",\"driverId\":3452141,\"driverName\":\"测试个\",\"driverPic\":\"https://dimg04.c-ctrip.com/images/0412t1200096r7p09AC35.jpg\",\"driverPhone\":\"13298y59687\",\"driverPhoneIntlCode\":\"86\",\"driverVirtualPhone\":\"\",\"driverVirtualPhoneIntlCode\":\"\",\"driverEmail\":\"z.5=<EMAIL>#\",\"driverContactType\":\"wechat\",\"driverContactAccount\":\"\",\"driverLanguage\":\"en\",\"driverSex\":null,\"nucleicAcidTestingResult\":null,\"vaccinationStatus\":null,\"carId\":\"1000129\",\"carLicense\":\"沪G89812\",\"carBrandId\":373,\"carBrandName\":\"荣威\",\"carSeriesId\":3846,\"carSeriesName\":\"e950\",\"vehicleGroupId\":118,\"vehicleGroupName\":\"舒适5座\",\"carColorId\":38,\"carColor\":\"白色\",\"energyType\":63,\"driverProvidePackageServices\":\"[{\\\"categoryCode\\\":\\\"pick_up_card\\\",\\\"driverCanProvide\\\":true},{\\\"categoryCode\\\":\\\"childseat\\\",\\\"driverCanProvide\\\":true}]\",\"isDel\":0,\"flightNo\":\"\",\"flightDate\":\"\",\"plannedDepartureDate\":\"\",\"plannedArrivalDate\":\"\",\"estimateDepartureDate\":\"\",\"estimateArriveDate\":\"\",\"actualDepartureDate\":\"\",\"actualArrivalDate\":\"\",\"departureCityId\":\"\",\"arrivalCityId\":\"\",\"departureAirportCode\":\"\",\"arrivalAirportCode\":\"\",\"departureTerminalId\":\"\",\"arrivalTerminalId\":\"\",\"flightStatus\":\"\",\"overDays\":\"\",\"operateUserAccount\":\"system\",\"operateUserType\":\"systemUser\",\"operateUserName\":\"system\",\"oldOrderStatus\":200,\"newOrderStatus\":240,\"datachangeCreatetime\":null,\"datachangeLasttime\":null,\"dispatcherConfirmType\":1,\"confirmSnapShotInfo\":null,\"goldDriver\":0},\"platformPriceStrategyList\":[{\"id\":682074,\"userOrderId\":\"***********\",\"dspOrderId\":\"*****************\",\"driverOrderId\":\"\",\"countryId\":1,\"countryName\":\"\",\"cityId\":2,\"cityName\":\"\",\"categoryCode\":\"airport_dropoff\",\"carTypeId\":118,\"settleType\":1,\"source\":0,\"strategyJson\":\"{\\\"settleSceneType\\\":1,\\\"countryId\\\":1,\\\"cityId\\\":2,\\\"categoryCode\\\":\\\"airport_dropoff\\\",\\\"settleDetailList\\\":[{\\\"vendorType\\\":1,\\\"billTime\\\":\\\"2024-09-04 14:23:32\\\",\\\"carTypeId\\\":118,\\\"feeType\\\":1,\\\"totalAmount\\\":136.000000,\\\"noCommissionAmount\\\":20.000000,\\\"serviceProviderId\\\":1,\\\"preferential\\\":0},{\\\"vendorType\\\":1,\\\"billTime\\\":\\\"2024-09-04 14:23:32\\\",\\\"carTypeId\\\":118,\\\"feeType\\\":3,\\\"totalAmount\\\":10.00,\\\"noCommissionAmount\\\":0,\\\"serviceProviderId\\\":1,\\\"strategyId\\\":\\\"3\\\",\\\"preferential\\\":0},{\\\"vendorType\\\":1,\\\"billTime\\\":\\\"2024-09-04 14:23:32\\\",\\\"carTypeId\\\":118,\\\"feeType\\\":3,\\\"totalAmount\\\":0,\\\"noCommissionAmount\\\":0,\\\"serviceProviderId\\\":1,\\\"strategyId\\\":\\\"6\\\",\\\"preferential\\\":0},{\\\"vendorType\\\":1,\\\"billTime\\\":\\\"2024-09-04 14:23:32\\\",\\\"carTypeId\\\":118,\\\"feeType\\\":4,\\\"totalAmount\\\":0.000000,\\\"serviceProviderId\\\":1,\\\"preferential\\\":0},{\\\"vendorType\\\":1,\\\"billTime\\\":\\\"2024-09-04 14:23:32\\\",\\\"carTypeId\\\":118,\\\"feeType\\\":5,\\\"totalAmount\\\":20.000000,\\\"serviceProviderId\\\":1,\\\"preferential\\\":0}]}\",\"expectSettlementResult\":\"{\\\"totalSettleAmount\\\":165.000000,\\\"totalCommissionAmount\\\":1.000000,\\\"settleResultDetailList\\\":[{\\\"vendorType\\\":1,\\\"feeType\\\":1,\\\"feeTypeName\\\":\\\"basePrice\\\",\\\"feeAmount\\\":136.000000,\\\"settleAmount\\\":136.00,\\\"commissionAmount\\\":0.00,\\\"shareRatio\\\":1.0000,\\\"shareRuleId\\\":51505,\\\"categoryCode\\\":\\\"airport_dropoff\\\",\\\"carTypeId\\\":118,\\\"serviceProviderId\\\":1,\\\"settleSubjectType\\\":3},{\\\"vendorType\\\":1,\\\"feeType\\\":3,\\\"feeTypeName\\\":\\\"platformAddPriceStrategy\\\",\\\"feeAmount\\\":10.00,\\\"settleAmount\\\":9.00,\\\"commissionAmount\\\":1.00,\\\"shareRatio\\\":0.9000,\\\"shareRuleId\\\":17978,\\\"categoryCode\\\":\\\"airport_dropoff\\\",\\\"strategyId\\\":\\\"3\\\",\\\"carTypeId\\\":118,\\\"serviceProviderId\\\":1,\\\"settleSubjectType\\\":3},{\\\"vendorType\\\":1,\\\"feeType\\\":3,\\\"feeTypeName\\\":\\\"platformAddPriceStrategy\\\",\\\"feeAmount\\\":0,\\\"settleAmount\\\":0.00,\\\"commissionAmount\\\":0.00,\\\"shareRatio\\\":0,\\\"shareRuleId\\\":0,\\\"categoryCode\\\":\\\"airport_dropoff\\\",\\\"strategyId\\\":\\\"6\\\",\\\"comment\\\":\\\"no match any rule\\\",\\\"carTypeId\\\":118,\\\"serviceProviderId\\\":1},{\\\"vendorType\\\":1,\\\"feeType\\\":4,\\\"feeTypeName\\\":\\\"highwayFee\\\",\\\"feeAmount\\\":0.000000,\\\"settleAmount\\\":0.00,\\\"commissionAmount\\\":0.00,\\\"shareRatio\\\":1,\\\"shareRuleId\\\":0,\\\"categoryCode\\\":\\\"airport_dropoff\\\",\\\"carTypeId\\\":118,\\\"serviceProviderId\\\":1,\\\"settleSubjectType\\\":3},{\\\"vendorType\\\":1,\\\"feeType\\\":5,\\\"feeTypeName\\\":\\\"parkingFee\\\",\\\"feeAmount\\\":20.000000,\\\"settleAmount\\\":20.00,\\\"commissionAmount\\\":0.00,\\\"shareRatio\\\":1,\\\"shareRuleId\\\":0,\\\"categoryCode\\\":\\\"airport_dropoff\\\",\\\"carTypeId\\\":118,\\\"serviceProviderId\\\":1,\\\"settleSubjectType\\\":3}]}\",\"actualSettlementResult\":\"{\\\"totalSettleAmount\\\":165.000000,\\\"totalCommissionAmount\\\":1.000000,\\\"settleResultDetailList\\\":[{\\\"vendorType\\\":1,\\\"feeType\\\":1,\\\"feeTypeName\\\":\\\"basePrice\\\",\\\"feeAmount\\\":136.000000,\\\"settleAmount\\\":136.00,\\\"commissionAmount\\\":0.00,\\\"shareRatio\\\":1.0000,\\\"shareRuleId\\\":51505,\\\"categoryCode\\\":\\\"airport_dropoff\\\",\\\"carTypeId\\\":118,\\\"serviceProviderId\\\":1,\\\"settleSubjectType\\\":3},{\\\"vendorType\\\":1,\\\"feeType\\\":3,\\\"feeTypeName\\\":\\\"platformAddPriceStrategy\\\",\\\"feeAmount\\\":10.00,\\\"settleAmount\\\":9.00,\\\"commissionAmount\\\":1.00,\\\"shareRatio\\\":0.9000,\\\"shareRuleId\\\":17978,\\\"categoryCode\\\":\\\"airport_dropoff\\\",\\\"strategyId\\\":\\\"3\\\",\\\"carTypeId\\\":118,\\\"serviceProviderId\\\":1,\\\"settleSubjectType\\\":3},{\\\"vendorType\\\":1,\\\"feeType\\\":3,\\\"feeTypeName\\\":\\\"platformAddPriceStrategy\\\",\\\"feeAmount\\\":0,\\\"settleAmount\\\":0.00,\\\"commissionAmount\\\":0.00,\\\"shareRatio\\\":0,\\\"shareRuleId\\\":0,\\\"categoryCode\\\":\\\"airport_dropoff\\\",\\\"strategyId\\\":\\\"6\\\",\\\"comment\\\":\\\"no match any rule\\\",\\\"carTypeId\\\":118,\\\"serviceProviderId\\\":1},{\\\"vendorType\\\":1,\\\"feeType\\\":4,\\\"feeTypeName\\\":\\\"highwayFee\\\",\\\"feeAmount\\\":0.000000,\\\"settleAmount\\\":0.00,\\\"commissionAmount\\\":0.00,\\\"shareRatio\\\":1,\\\"shareRuleId\\\":0,\\\"categoryCode\\\":\\\"airport_dropoff\\\",\\\"carTypeId\\\":118,\\\"serviceProviderId\\\":1,\\\"settleSubjectType\\\":3},{\\\"vendorType\\\":1,\\\"feeType\\\":5,\\\"feeTypeName\\\":\\\"parkingFee\\\",\\\"feeAmount\\\":20.000000,\\\"settleAmount\\\":20.00,\\\"commissionAmount\\\":0.00,\\\"shareRatio\\\":1,\\\"shareRuleId\\\":0,\\\"categoryCode\\\":\\\"airport_dropoff\\\",\\\"carTypeId\\\":118,\\\"serviceProviderId\\\":1,\\\"settleSubjectType\\\":3}]}\",\"datachangeCreatetime\":null,\"datachangeLasttime\":null}],\"wayPointInfoList\":[]}}";
        QueryOrderDetailResponseType responseType = JacksonUtil.deserialize(s, QueryOrderDetailResponseType.class);

        SupplyOrder supplyOrder = converter.toSupplyOrder(responseType);
        Assert.assertNotNull(supplyOrder);
        Assert.assertEquals("***********", supplyOrder.getSourceOrderId());
        Assert.assertEquals("*****************", supplyOrder.getOrderId());
        Assert.assertEquals("2", supplyOrder.getCityCode());
        Assert.assertEquals(2, supplyOrder.getCityId().intValue());
        Assert.assertEquals(118, supplyOrder.getCarTypeId().intValue());
        Assert.assertEquals(1, supplyOrder.getServiceType().intValue());
        Assert.assertEquals(3, supplyOrder.getOrderStatus().intValue());
        Assert.assertEquals(0, supplyOrder.getOrderStatusDetail().intValue());
        Assert.assertEquals(0, supplyOrder.getIsOtaBookOrder().intValue());
        Assert.assertEquals(49737, supplyOrder.getSkuId().longValue());
        Assert.assertEquals("airport_dropoff", supplyOrder.getCategoryCode());
        Assert.assertEquals(30804, supplyOrder.getSupplierId().longValue());
        Assert.assertEquals(136.0, supplyOrder.getPredictPriceInfo().getDrivTotalFee(), 0.01);
        Assert.assertEquals(7.00, supplyOrder.getPredictPriceInfo().getKiloLength(), 0.01);
        Assert.assertEquals(15.00, supplyOrder.getPredictPriceInfo().getTimeLength(), 0.01);
        Assert.assertEquals(136.0, supplyOrder.getDriverOrderFee(), 0.01);
        Assert.assertTrue(supplyOrder.getOrderPackageServiceCodes().contains("pick_up_card"));
    }


    @Test
    public void test1() {

        OrderDetail orderDetail = new OrderDetail();
        BaseDetail baseDetail = new BaseDetail();
        baseDetail.setEstimatedUseTime("2024-09-16 16:20:00");
        baseDetail.setPredicServiceStopTimeBj("2024-09-16 16:20:00");
        baseDetail.setCityId(1);
        orderDetail.setBaseDetail(baseDetail);
        EstimatedCostInfo estimatedCostInfo = new EstimatedCostInfo();
        estimatedCostInfo.setCostAmount(new BigDecimal("1"));
        orderDetail.setEstimatedCostInfo(estimatedCostInfo);
        List<SupplyOrder> supplyOrderList = converter.toSupplyOrderList(Lists.newArrayList(orderDetail));
        Assert.assertEquals(supplyOrderList.size(), 1);
        orderDetail.setEstimatedCostInfo(new EstimatedCostInfo());
        orderDetail.setActualCostInfo(new ActualCostInfo());
        orderDetail.setSopRecords(Lists.newArrayList());
        orderDetail.setOrderStandardRecord(new OrderStandardRecord());
        orderDetail.setOrderPackageServices(Lists.newArrayList());
        orderDetail.setCancelRecord(new CancelRecord());
        orderDetail.setBreakAppointmentRecord(new BreakAppointmentRecord());
        orderDetail.setOrderConfirmRecord(new OrderConfirmRecord());
//        orderDetail.setPlatformPriceStrategy(new PlatformPriceStrategy());
        orderDetail.setOrderExtendAttributeInfo(Lists.newArrayList(new OrderExtendAttributeDTO("user_level_code", "a")));

    }

    @Test
    public void test2() {

        String s = "{\"ResponseStatus\":{\"Timestamp\":\"/Date(1725431042615+0800)/\",\"Ack\":\"Success\",\"Errors\":[],\"Build\":null,\"Version\":null,\"Extension\":[]},\"responseResult\":{\"success\":true,\"returnCode\":\"200\",\"returnMessage\":\"服务执行成功\"},\"orderDetail\":{\"baseDetail\":{\"userOrderId\":\"***********\",\"supplyOrderId\":\"191630232049483849\",\"dspOrderId\":\"*****************\",\"driverOrderId\":null,\"orderVersion\":5,\"confirmRecordId\":3094739390609834165,\"orderStatus\":240,\"oldOrderStatus\":3,\"oldOrderStatusDetail\":0,\"orderSource\":1,\"productType\":0,\"productCode\":\"\",\"productName\":\"\",\"categoryCode\":\"airport_dropoff\",\"serviceType\":1,\"cityId\":2,\"fromCityId\":2,\"toCityId\":2,\"vehicleGroupId\":118,\"estimatedUseTime\":\"2024-09-16 16:20:00\",\"estimatedUseTimeBj\":\"2024-09-16 16:20:00\",\"predicServiceStopTimeBj\":\"2024-09-16 16:35:00\",\"lastConfirmTime\":\"2024-09-15 16:20:00\",\"lastConfirmTimeBj\":\"2024-09-15 16:20:00\",\"lastConfirmCarTime\":\"2024-09-16 14:20:00\",\"lastConfirmCarTimeBj\":\"2024-09-16 14:20:00\",\"connectMode\":1,\"salesMode\":5,\"useDays\":1.00,\"estimatedKm\":7.00,\"estimatedMin\":15.00,\"actualKm\":0.00,\"actualMin\":0.00,\"priceMark\":\"103412\",\"serviceProviderId\":1,\"supplierId\":30804,\"countryId\":1,\"locale\":\"zh-cn\",\"skuId\":49737,\"cancelRule\":\"{\\\"timeType\\\":3,\\\"freeCancel\\\":true,\\\"timeZone\\\":8,\\\"timeInterval\\\":0,\\\"timeIntervalUnit\\\":\\\"MINUTES\\\",\\\"timeIntervalUnitDesc\\\":\\\"分钟\\\",\\\"cancelFeeRate\\\":50,\\\"freeCancelTime\\\":\\\"2024-09-16 16:20:00\\\",\\\"freeCancelTimeBJ\\\":\\\"2024-09-16 16:20:00\\\"}\",\"waitingRule\":\"[{\\\"referenceTimeType\\\":1,\\\"type\\\":0,\\\"waitingTime\\\":10,\\\"waitingTimeUnit\\\":\\\"MINUTES\\\",\\\"waitingTimeUnitDesc\\\":\\\"分钟\\\",\\\"amount\\\":1.00,\\\"currency\\\":\\\"CNY\\\",\\\"overWaitingTime\\\":1,\\\"overWaitingTimeUnit\\\":\\\"MINUTES\\\",\\\"overWaitingTimeUnitDesc\\\":\\\"分钟\\\",\\\"active\\\":true,\\\"freeWait\\\":false}]\",\"priceResultCode\":\"34vk_8iat_0_9df4,9dfi,9dfp,9dfb_0_0_1_0_0_3\",\"detailSnapShotid\":\"\",\"xproductInfo\":\"null\",\"spContractInfo\":\"{\\\"contact\\\":{\\\"id\\\":782282,\\\"supplierId\\\":30804,\\\"serviceProviderId\\\":1,\\\"code\\\":\\\"SHCT2022072100057778\\\",\\\"name\\\":\\\"用车平台协议模板 随县统计局555）\\\",\\\"status\\\":8,\\\"statusDesc\\\":\\\"已签署\\\",\\\"salesMode\\\":2,\\\"salesModeDesc\\\":\\\"零售\\\",\\\"settlementMethod\\\":2,\\\"priceTerms\\\":2,\\\"tocSettlementPeriodType\\\":\\\"E\\\",\\\"tobSettlementPeriodType\\\":\\\"\\\",\\\"partA\\\":\\\"上海华程西南国际旅行社有限公司\\\",\\\"type\\\":1,\\\"tocSettlementDateType\\\":1,\\\"tobSettlementDateType\\\":0,\\\"paySiteId\\\":2,\\\"partCPaySiteId\\\":0,\\\"invoicePattern\\\":2},\\\"supplier\\\":{\\\"id\\\":30804,\\\"name\\\":\\\"随县统计局555）\\\"},\\\"serviceProviderBaseInfo\\\":{\\\"id\\\":1,\\\"carrierLocalName\\\":\\\"天津海豚出游科技有限公司\\\",\\\"salesMode\\\":5}}\",\"dspStrategyStr\":\"{\\\"dspStrategyId\\\":1003,\\\"skuType\\\":1,\\\"dspData\\\":\\\"{\\\\\\\"skuType\\\\\\\":\\\\\\\"1\\\\\\\",\\\\\\\"beginSeconds\\\\\\\":\\\\\\\"0\\\\\\\",\\\\\\\"useSeconds\\\\\\\":\\\\\\\"0\\\\\\\",\\\\\\\"addPriceRatio\\\\\\\":\\\\\\\"0\\\\\\\",\\\\\\\"addPriceMoney\\\\\\\":\\\\\\\"0\\\\\\\",\\\\\\\"maxAddPriceRatioConf\\\\\\\":\\\\\\\"0\\\\\\\",\\\\\\\"maxAddPriceMoneyConf\\\\\\\":\\\\\\\"0\\\\\\\",\\\\\\\"addPriceRatioConf\\\\\\\":\\\\\\\"0\\\\\\\",\\\\\\\"addPriceMoneyConf\\\\\\\":\\\\\\\"0\\\\\\\",\\\\\\\"reason\\\\\\\":99,\\\\\\\"sceneId\\\\\\\":\\\\\\\"2022\\\\\\\",\\\\\\\"canDown\\\\\\\":false,\\\\\\\"humanDsp\\\\\\\":false,\\\\\\\"bookFirstFlag\\\\\\\":true,\\\\\\\"dspStrategyId\\\\\\\":1003,\\\\\\\"retryNum\\\\\\\":0,\\\\\\\"grade\\\\\\\":\\\\\\\"1_1\\\\\\\"}\\\",\\\"bookFirstFlag\\\":true}\",\"orderCarTypeId\":118,\"drvId\":null,\"drvName\":null,\"drvPhone\":null,\"drvPhoneAreaCode\":null,\"drvCoopMode\":null,\"drvLanguageCodeList\":null,\"carId\":null,\"carTypeId\":null,\"carLicense\":null,\"carColorId\":null,\"carBrandId\":null,\"carSeriesId\":null,\"transportGroupId\":null,\"transportGroupMode\":null,\"transportGroupName\":null,\"confirmMoneyTimeLocal\":null,\"confirmMoneyTimeBj\":null,\"finishTimeBj\":null,\"cancelTimeLocal\":null,\"cancelTimeBj\":null,\"bizAreaType\":32,\"adultCount\":2,\"childCount\":0,\"bagCount\":0,\"maxBagCount\":null,\"coordType\":\"GCJ02\",\"fromLongitude\":121.352141,\"fromLatitude\":31.222065,\"toLongitude\":121.347566,\"toLatitude\":31.194807,\"takenTimeBj\":null,\"targetId\":\"SHA\",\"terminalId\":\"10002\",\"fixedLocationType\":2,\"fixedPosition\":\"SHA\",\"premiumOrderFlag\":0,\"dspOrderUpdateTime\":\"2024-09-04 14:23:57\",\"dspOrderCreateTime\":\"2024-09-04 14:23:32\",\"drvOrderUpdateTime\":null,\"drvOrderCreateTime\":null,\"newPayProcess\":0,\"uid\":\"*********\",\"distributionChannelId\":5,\"giveFreeTime\":0,\"highUser\":1,\"dispatchLimitType\":null,\"allotDriverTimeBj\":null,\"fromPoiDTO\":{\"depPoiCode\":\"bb57cf24e13ece3e43afe640\",\"depPoiSourceType\":\"Baidu\",\"actualFromLongitude\":121.352141,\"actualFromLatitude\":31.222065,\"actualFromCoordsys\":\"GCJ02\",\"fromAddress\":\"长宁区 金钟路968号\",\"fromName\":\"凌空SOHO\"},\"toPoiDTO\":{\"arrivePoiCode\":\"10002\",\"arrivePoiSourceType\":\"basic_tml\",\"actualToLongitude\":121.347566,\"actualToLatitude\":31.194807,\"actualToCoordsys\":\"GCJ02\",\"toAddress\":\"虹桥国际机场 T1\",\"toName\":\"虹桥国际机场 T1\"},\"highGradeOrder\":1,\"timeZone\":8.00,\"orderDetailExtendInfo\":\"{\\\"useTime\\\":\\\"2024-09-16 16:20:00\\\",\\\"orderSysType\\\":1,\\\"crossBorderType\\\":1}\",\"rushOrder\":0,\"driverOfficialStatus\":null,\"vehicleOfficialStatus\":null,\"orderSysType\":1},\"estimatedCostInfo\":{\"salesAmount\":146.000000,\"costAmount\":136.000000,\"tollFee\":0.000000,\"parkingFee\":20.000000,\"noCommisionAmount\":20.000000,\"supplierBornePrice\":\"null\",\"premiumPrice\":\"[{\\\"strategyId\\\":\\\"3\\\",\\\"feeAmount\\\":10.00},{\\\"strategyId\\\":\\\"6\\\",\\\"feeAmount\\\":0}]\",\"userCurrency\":\"CNY\",\"supplierCurrency\":\"CNY\",\"sc2cnyExchangeRate\":1.000000,\"cny2ucExchangeRate\":1.000000,\"sc2ucExchangeRate\":1.000000,\"dspAddPrice\":0.000000,\"waypointAmount\":0.000000,\"emptyFareFee\":0.00,\"driverCurrency\":\"CNY\",\"sc2driverExchangeRate\":1.000000,\"uc2driverExchangeRate\":1.000000,\"settleToDriver\":null,\"cancelFineRate\":0.00},\"actualCostInfo\":{\"id\":515544,\"drvOrderId\":\"D94863437939146802\",\"dspOrderId\":\"*****************\",\"userOrderId\":\"***********\",\"supplyOrderId\":\"191630232049483849\",\"drvId\":3452141,\"categoryCode\":\"airport_dropoff\",\"overDistanceLength\":0,\"rewardAmount\":1.00,\"waitFee\":0.00,\"overDistanceFee\":0.00,\"overTimeLength\":0,\"overTimeFee\":0.00,\"parkingFee\":0.00,\"accommodationFee\":0.00,\"tollFee\":0,\"auditStatus\":-1,\"auditEffectiveTimeBj\":null,\"payStatus\":-1,\"extraFeeStatus\":3,\"currentExchangeRate\":null,\"userExchangeRate\":null,\"vendorExchangeRate\":null,\"userNeedPayInfo\":\"\",\"confirmTimeLocal\":null,\"confirmTimeBj\":null,\"feeExpirationDateBj\":null,\"feeExpirationDateLocal\":null,\"operator\":\"SYSTEM\",\"welfareMoney\":0.00,\"datachangeCreatetime\":null,\"datachangeLasttime\":null,\"extendRule\":\"{\\\"vendorOverDurationFee\\\":0,\\\"vendorOverKilometreFee\\\":0}\",\"commissionRule\":\"\"},\"sopRecords\":null,\"orderStandardRecord\":null,\"orderPackageServices\":null,\"cancelRecord\":null,\"breakAppointmentRecord\":null,\"orderConfirmRecord\":{\"id\":255354,\"recordId\":3094739390609834165,\"dspOrderId\":\"*****************\",\"supplyOrderId\":\"191630232049483849\",\"userOrderId\":\"***********\",\"categoryCode\":\"airport_dropoff\",\"driverOrderId\":\"D94863437939146802\",\"groupId\":0,\"confirmType\":\"40\",\"confirmTime\":null,\"confirmEvent\":10,\"duid\":\"0-0-0-0-0-0-0-0-0-0\",\"dspRewardAmount\":\"\",\"takenType\":0,\"serviceProviderId\":1,\"serviceProviderPhone\":\"\",\"serviceProviderLanguage\":\"\",\"supplierId\":30804,\"transportGroupId\":186,\"dispatcherPhone\":\"189Pi6i0264\",\"dispatcherPhoneCode\":\"86\",\"dispatcherLanguage\":\"hk\",\"dispatcherName\":\"范德萨\",\"dispatcherEmail\":\"\",\"dispatcherContactType\":\"\",\"dispatcherContactAccount\":\"wechat\",\"overseaDispatcherPhone\":\"\",\"overseaDispatcherPhoneCode\":\"\",\"driverId\":3452141,\"driverName\":\"测试个\",\"driverPic\":\"https://dimg04.c-ctrip.com/images/0412t1200096r7p09AC35.jpg\",\"driverPhone\":\"13298y59687\",\"driverPhoneIntlCode\":\"86\",\"driverVirtualPhone\":\"\",\"driverVirtualPhoneIntlCode\":\"\",\"driverEmail\":\"z.5=<EMAIL>#\",\"driverContactType\":\"wechat\",\"driverContactAccount\":\"\",\"driverLanguage\":\"en\",\"driverSex\":null,\"nucleicAcidTestingResult\":null,\"vaccinationStatus\":null,\"carId\":\"1000129\",\"carLicense\":\"沪G89812\",\"carBrandId\":373,\"carBrandName\":\"荣威\",\"carSeriesId\":3846,\"carSeriesName\":\"e950\",\"vehicleGroupId\":118,\"vehicleGroupName\":\"舒适5座\",\"carColorId\":38,\"carColor\":\"白色\",\"energyType\":63,\"driverProvidePackageServices\":\"[{\\\"categoryCode\\\":\\\"pick_up_card\\\",\\\"driverCanProvide\\\":true},{\\\"categoryCode\\\":\\\"childseat\\\",\\\"driverCanProvide\\\":true}]\",\"isDel\":0,\"flightNo\":\"\",\"flightDate\":\"\",\"plannedDepartureDate\":\"\",\"plannedArrivalDate\":\"\",\"estimateDepartureDate\":\"\",\"estimateArriveDate\":\"\",\"actualDepartureDate\":\"\",\"actualArrivalDate\":\"\",\"departureCityId\":\"\",\"arrivalCityId\":\"\",\"departureAirportCode\":\"\",\"arrivalAirportCode\":\"\",\"departureTerminalId\":\"\",\"arrivalTerminalId\":\"\",\"flightStatus\":\"\",\"overDays\":\"\",\"operateUserAccount\":\"system\",\"operateUserType\":\"systemUser\",\"operateUserName\":\"system\",\"oldOrderStatus\":200,\"newOrderStatus\":240,\"datachangeCreatetime\":null,\"datachangeLasttime\":null,\"dispatcherConfirmType\":1,\"confirmSnapShotInfo\":null,\"goldDriver\":0},\"platformPriceStrategyList\":[{\"id\":682074,\"userOrderId\":\"***********\",\"dspOrderId\":\"*****************\",\"driverOrderId\":\"\",\"countryId\":1,\"countryName\":\"\",\"cityId\":2,\"cityName\":\"\",\"categoryCode\":\"airport_dropoff\",\"carTypeId\":118,\"settleType\":1,\"source\":0,\"strategyJson\":\"{\\\"settleSceneType\\\":1,\\\"countryId\\\":1,\\\"cityId\\\":2,\\\"categoryCode\\\":\\\"airport_dropoff\\\",\\\"settleDetailList\\\":[{\\\"vendorType\\\":1,\\\"billTime\\\":\\\"2024-09-04 14:23:32\\\",\\\"carTypeId\\\":118,\\\"feeType\\\":1,\\\"totalAmount\\\":136.000000,\\\"noCommissionAmount\\\":20.000000,\\\"serviceProviderId\\\":1,\\\"preferential\\\":0},{\\\"vendorType\\\":1,\\\"billTime\\\":\\\"2024-09-04 14:23:32\\\",\\\"carTypeId\\\":118,\\\"feeType\\\":3,\\\"totalAmount\\\":10.00,\\\"noCommissionAmount\\\":0,\\\"serviceProviderId\\\":1,\\\"strategyId\\\":\\\"3\\\",\\\"preferential\\\":0},{\\\"vendorType\\\":1,\\\"billTime\\\":\\\"2024-09-04 14:23:32\\\",\\\"carTypeId\\\":118,\\\"feeType\\\":3,\\\"totalAmount\\\":0,\\\"noCommissionAmount\\\":0,\\\"serviceProviderId\\\":1,\\\"strategyId\\\":\\\"6\\\",\\\"preferential\\\":0},{\\\"vendorType\\\":1,\\\"billTime\\\":\\\"2024-09-04 14:23:32\\\",\\\"carTypeId\\\":118,\\\"feeType\\\":4,\\\"totalAmount\\\":0.000000,\\\"serviceProviderId\\\":1,\\\"preferential\\\":0},{\\\"vendorType\\\":1,\\\"billTime\\\":\\\"2024-09-04 14:23:32\\\",\\\"carTypeId\\\":118,\\\"feeType\\\":5,\\\"totalAmount\\\":20.000000,\\\"serviceProviderId\\\":1,\\\"preferential\\\":0}]}\",\"expectSettlementResult\":\"{\\\"totalSettleAmount\\\":165.000000,\\\"totalCommissionAmount\\\":1.000000,\\\"settleResultDetailList\\\":[{\\\"vendorType\\\":1,\\\"feeType\\\":1,\\\"feeTypeName\\\":\\\"basePrice\\\",\\\"feeAmount\\\":136.000000,\\\"settleAmount\\\":136.00,\\\"commissionAmount\\\":0.00,\\\"shareRatio\\\":1.0000,\\\"shareRuleId\\\":51505,\\\"categoryCode\\\":\\\"airport_dropoff\\\",\\\"carTypeId\\\":118,\\\"serviceProviderId\\\":1,\\\"settleSubjectType\\\":3},{\\\"vendorType\\\":1,\\\"feeType\\\":3,\\\"feeTypeName\\\":\\\"platformAddPriceStrategy\\\",\\\"feeAmount\\\":10.00,\\\"settleAmount\\\":9.00,\\\"commissionAmount\\\":1.00,\\\"shareRatio\\\":0.9000,\\\"shareRuleId\\\":17978,\\\"categoryCode\\\":\\\"airport_dropoff\\\",\\\"strategyId\\\":\\\"3\\\",\\\"carTypeId\\\":118,\\\"serviceProviderId\\\":1,\\\"settleSubjectType\\\":3},{\\\"vendorType\\\":1,\\\"feeType\\\":3,\\\"feeTypeName\\\":\\\"platformAddPriceStrategy\\\",\\\"feeAmount\\\":0,\\\"settleAmount\\\":0.00,\\\"commissionAmount\\\":0.00,\\\"shareRatio\\\":0,\\\"shareRuleId\\\":0,\\\"categoryCode\\\":\\\"airport_dropoff\\\",\\\"strategyId\\\":\\\"6\\\",\\\"comment\\\":\\\"no match any rule\\\",\\\"carTypeId\\\":118,\\\"serviceProviderId\\\":1},{\\\"vendorType\\\":1,\\\"feeType\\\":4,\\\"feeTypeName\\\":\\\"highwayFee\\\",\\\"feeAmount\\\":0.000000,\\\"settleAmount\\\":0.00,\\\"commissionAmount\\\":0.00,\\\"shareRatio\\\":1,\\\"shareRuleId\\\":0,\\\"categoryCode\\\":\\\"airport_dropoff\\\",\\\"carTypeId\\\":118,\\\"serviceProviderId\\\":1,\\\"settleSubjectType\\\":3},{\\\"vendorType\\\":1,\\\"feeType\\\":5,\\\"feeTypeName\\\":\\\"parkingFee\\\",\\\"feeAmount\\\":20.000000,\\\"settleAmount\\\":20.00,\\\"commissionAmount\\\":0.00,\\\"shareRatio\\\":1,\\\"shareRuleId\\\":0,\\\"categoryCode\\\":\\\"airport_dropoff\\\",\\\"carTypeId\\\":118,\\\"serviceProviderId\\\":1,\\\"settleSubjectType\\\":3}]}\",\"actualSettlementResult\":\"{\\\"totalSettleAmount\\\":165.000000,\\\"totalCommissionAmount\\\":1.000000,\\\"settleResultDetailList\\\":[{\\\"vendorType\\\":1,\\\"feeType\\\":1,\\\"feeTypeName\\\":\\\"basePrice\\\",\\\"feeAmount\\\":136.000000,\\\"settleAmount\\\":136.00,\\\"commissionAmount\\\":0.00,\\\"shareRatio\\\":1.0000,\\\"shareRuleId\\\":51505,\\\"categoryCode\\\":\\\"airport_dropoff\\\",\\\"carTypeId\\\":118,\\\"serviceProviderId\\\":1,\\\"settleSubjectType\\\":3},{\\\"vendorType\\\":1,\\\"feeType\\\":3,\\\"feeTypeName\\\":\\\"platformAddPriceStrategy\\\",\\\"feeAmount\\\":10.00,\\\"settleAmount\\\":9.00,\\\"commissionAmount\\\":1.00,\\\"shareRatio\\\":0.9000,\\\"shareRuleId\\\":17978,\\\"categoryCode\\\":\\\"airport_dropoff\\\",\\\"strategyId\\\":\\\"3\\\",\\\"carTypeId\\\":118,\\\"serviceProviderId\\\":1,\\\"settleSubjectType\\\":3},{\\\"vendorType\\\":1,\\\"feeType\\\":3,\\\"feeTypeName\\\":\\\"platformAddPriceStrategy\\\",\\\"feeAmount\\\":0,\\\"settleAmount\\\":0.00,\\\"commissionAmount\\\":0.00,\\\"shareRatio\\\":0,\\\"shareRuleId\\\":0,\\\"categoryCode\\\":\\\"airport_dropoff\\\",\\\"strategyId\\\":\\\"6\\\",\\\"comment\\\":\\\"no match any rule\\\",\\\"carTypeId\\\":118,\\\"serviceProviderId\\\":1},{\\\"vendorType\\\":1,\\\"feeType\\\":4,\\\"feeTypeName\\\":\\\"highwayFee\\\",\\\"feeAmount\\\":0.000000,\\\"settleAmount\\\":0.00,\\\"commissionAmount\\\":0.00,\\\"shareRatio\\\":1,\\\"shareRuleId\\\":0,\\\"categoryCode\\\":\\\"airport_dropoff\\\",\\\"carTypeId\\\":118,\\\"serviceProviderId\\\":1,\\\"settleSubjectType\\\":3},{\\\"vendorType\\\":1,\\\"feeType\\\":5,\\\"feeTypeName\\\":\\\"parkingFee\\\",\\\"feeAmount\\\":20.000000,\\\"settleAmount\\\":20.00,\\\"commissionAmount\\\":0.00,\\\"shareRatio\\\":1,\\\"shareRuleId\\\":0,\\\"categoryCode\\\":\\\"airport_dropoff\\\",\\\"carTypeId\\\":118,\\\"serviceProviderId\\\":1,\\\"settleSubjectType\\\":3}]}\",\"datachangeCreatetime\":null,\"datachangeLasttime\":null}],\"wayPointInfoList\":[]}}";
        QueryOrderDetailResponseType responseType = JacksonUtil.deserialize(s, QueryOrderDetailResponseType.class);

        SupplyOrder supplyOrder = converter.toSupplyOrder(responseType);
        Assert.assertNotNull(supplyOrder);
        Assert.assertEquals("***********", supplyOrder.getSourceOrderId());
        Assert.assertEquals("*****************", supplyOrder.getOrderId());
        Assert.assertEquals("2", supplyOrder.getCityCode());
        Assert.assertEquals(2, supplyOrder.getCityId().intValue());
        Assert.assertEquals(118, supplyOrder.getCarTypeId().intValue());
        Assert.assertEquals(1, supplyOrder.getServiceType().intValue());
        Assert.assertEquals(3, supplyOrder.getOrderStatus().intValue());
        Assert.assertEquals(0, supplyOrder.getOrderStatusDetail().intValue());
        Assert.assertEquals(0, supplyOrder.getIsOtaBookOrder().intValue());
        Assert.assertEquals(49737, supplyOrder.getSkuId().longValue());
        Assert.assertEquals("airport_dropoff", supplyOrder.getCategoryCode());
        Assert.assertEquals(30804, supplyOrder.getSupplierId().longValue());
        Assert.assertEquals(136.0, supplyOrder.getPredictPriceInfo().getDrivTotalFee(), 0.01);
        Assert.assertEquals(7.00, supplyOrder.getPredictPriceInfo().getKiloLength(), 0.01);
        Assert.assertEquals(15.00, supplyOrder.getPredictPriceInfo().getTimeLength(), 0.01);
        Assert.assertEquals(136.0, supplyOrder.getDriverOrderFee(), 0.01);
        Assert.assertTrue(supplyOrder.getOrderPackageServiceCodes().isEmpty());
    }

    @Test
    public void test3() {

        String s = "{\"ResponseStatus\":{\"Timestamp\":\"/Date(1725431042615+0800)/\",\"Ack\":\"Success\",\"Errors\":[],\"Build\":null,\"Version\":null,\"Extension\":[]},\"responseResult\":{\"success\":true,\"returnCode\":\"200\",\"returnMessage\":\"服务执行成功\"},\"orderDetail\":{\"baseDetail\":{\"userOrderId\":\"***********\",\"supplyOrderId\":\"191630232049483849\",\"dspOrderId\":\"*****************\",\"driverOrderId\":null,\"orderVersion\":5,\"confirmRecordId\":3094739390609834165,\"orderStatus\":240,\"oldOrderStatus\":3,\"oldOrderStatusDetail\":0,\"orderSource\":1,\"productType\":0,\"productCode\":\"\",\"productName\":\"\",\"categoryCode\":\"airport_dropoff\",\"serviceType\":1,\"cityId\":2,\"fromCityId\":2,\"toCityId\":2,\"vehicleGroupId\":118,\"estimatedUseTime\":\"2024-09-16 16:20:00\",\"estimatedUseTimeBj\":\"2024-09-16 16:20:00\",\"predicServiceStopTimeBj\":\"2024-09-16 16:35:00\",\"lastConfirmTime\":\"2024-09-15 16:20:00\",\"lastConfirmTimeBj\":\"2024-09-15 16:20:00\",\"lastConfirmCarTime\":\"2024-09-16 14:20:00\",\"lastConfirmCarTimeBj\":\"2024-09-16 14:20:00\",\"connectMode\":1,\"salesMode\":5,\"useDays\":1.00,\"estimatedKm\":7.00,\"estimatedMin\":15.00,\"actualKm\":0.00,\"actualMin\":0.00,\"priceMark\":\"103412\",\"serviceProviderId\":1,\"supplierId\":30804,\"countryId\":1,\"locale\":\"zh-cn\",\"skuId\":49737,\"cancelRule\":\"{\\\"timeType\\\":3,\\\"freeCancel\\\":true,\\\"timeZone\\\":8,\\\"timeInterval\\\":0,\\\"timeIntervalUnit\\\":\\\"MINUTES\\\",\\\"timeIntervalUnitDesc\\\":\\\"分钟\\\",\\\"cancelFeeRate\\\":50,\\\"freeCancelTime\\\":\\\"2024-09-16 16:20:00\\\",\\\"freeCancelTimeBJ\\\":\\\"2024-09-16 16:20:00\\\"}\",\"waitingRule\":\"[{\\\"referenceTimeType\\\":1,\\\"type\\\":0,\\\"waitingTime\\\":10,\\\"waitingTimeUnit\\\":\\\"MINUTES\\\",\\\"waitingTimeUnitDesc\\\":\\\"分钟\\\",\\\"amount\\\":1.00,\\\"currency\\\":\\\"CNY\\\",\\\"overWaitingTime\\\":1,\\\"overWaitingTimeUnit\\\":\\\"MINUTES\\\",\\\"overWaitingTimeUnitDesc\\\":\\\"分钟\\\",\\\"active\\\":true,\\\"freeWait\\\":false}]\",\"priceResultCode\":\"34vk_8iat_0_9df4,9dfi,9dfp,9dfb_0_0_1_0_0_3\",\"detailSnapShotid\":\"\",\"xproductInfo\":\"[{\\\"categoryCode\\\":\\\"pick_up_card\\\"}]\",\"spContractInfo\":\"{\\\"contact\\\":{\\\"id\\\":782282,\\\"supplierId\\\":30804,\\\"serviceProviderId\\\":1,\\\"code\\\":\\\"SHCT2022072100057778\\\",\\\"name\\\":\\\"用车平台协议模板 随县统计局555）\\\",\\\"status\\\":8,\\\"statusDesc\\\":\\\"已签署\\\",\\\"salesMode\\\":2,\\\"salesModeDesc\\\":\\\"零售\\\",\\\"settlementMethod\\\":2,\\\"priceTerms\\\":2,\\\"tocSettlementPeriodType\\\":\\\"E\\\",\\\"tobSettlementPeriodType\\\":\\\"\\\",\\\"partA\\\":\\\"上海华程西南国际旅行社有限公司\\\",\\\"type\\\":1,\\\"tocSettlementDateType\\\":1,\\\"tobSettlementDateType\\\":0,\\\"paySiteId\\\":2,\\\"partCPaySiteId\\\":0,\\\"invoicePattern\\\":2},\\\"supplier\\\":{\\\"id\\\":30804,\\\"name\\\":\\\"随县统计局555）\\\"},\\\"serviceProviderBaseInfo\\\":{\\\"id\\\":1,\\\"carrierLocalName\\\":\\\"天津海豚出游科技有限公司\\\",\\\"salesMode\\\":5}}\",\"dspStrategyStr\":\"{\\\"dspStrategyId\\\":1003,\\\"skuType\\\":1,\\\"dspData\\\":\\\"{\\\\\\\"skuType\\\\\\\":\\\\\\\"1\\\\\\\",\\\\\\\"beginSeconds\\\\\\\":\\\\\\\"0\\\\\\\",\\\\\\\"useSeconds\\\\\\\":\\\\\\\"0\\\\\\\",\\\\\\\"addPriceRatio\\\\\\\":\\\\\\\"0\\\\\\\",\\\\\\\"addPriceMoney\\\\\\\":\\\\\\\"0\\\\\\\",\\\\\\\"maxAddPriceRatioConf\\\\\\\":\\\\\\\"0\\\\\\\",\\\\\\\"maxAddPriceMoneyConf\\\\\\\":\\\\\\\"0\\\\\\\",\\\\\\\"addPriceRatioConf\\\\\\\":\\\\\\\"0\\\\\\\",\\\\\\\"addPriceMoneyConf\\\\\\\":\\\\\\\"0\\\\\\\",\\\\\\\"reason\\\\\\\":99,\\\\\\\"sceneId\\\\\\\":\\\\\\\"2022\\\\\\\",\\\\\\\"canDown\\\\\\\":false,\\\\\\\"humanDsp\\\\\\\":false,\\\\\\\"bookFirstFlag\\\\\\\":true,\\\\\\\"dspStrategyId\\\\\\\":1003,\\\\\\\"retryNum\\\\\\\":0,\\\\\\\"grade\\\\\\\":\\\\\\\"1_1\\\\\\\"}\\\",\\\"bookFirstFlag\\\":true}\",\"orderCarTypeId\":118,\"drvId\":null,\"drvName\":null,\"drvPhone\":null,\"drvPhoneAreaCode\":null,\"drvCoopMode\":null,\"drvLanguageCodeList\":null,\"carId\":null,\"carTypeId\":null,\"carLicense\":null,\"carColorId\":null,\"carBrandId\":null,\"carSeriesId\":null,\"transportGroupId\":null,\"transportGroupMode\":null,\"transportGroupName\":null,\"confirmMoneyTimeLocal\":null,\"confirmMoneyTimeBj\":null,\"finishTimeBj\":null,\"cancelTimeLocal\":null,\"cancelTimeBj\":null,\"bizAreaType\":32,\"adultCount\":2,\"childCount\":0,\"bagCount\":0,\"maxBagCount\":null,\"coordType\":\"GCJ02\",\"fromLongitude\":121.352141,\"fromLatitude\":31.222065,\"toLongitude\":121.347566,\"toLatitude\":31.194807,\"takenTimeBj\":null,\"targetId\":\"SHA\",\"terminalId\":\"10002\",\"fixedLocationType\":2,\"fixedPosition\":\"SHA\",\"premiumOrderFlag\":0,\"dspOrderUpdateTime\":\"2024-09-04 14:23:57\",\"dspOrderCreateTime\":\"2024-09-04 14:23:32\",\"drvOrderUpdateTime\":null,\"drvOrderCreateTime\":null,\"newPayProcess\":0,\"uid\":\"*********\",\"distributionChannelId\":5,\"giveFreeTime\":0,\"highUser\":1,\"dispatchLimitType\":null,\"allotDriverTimeBj\":null,\"fromPoiDTO\":{\"depPoiCode\":\"bb57cf24e13ece3e43afe640\",\"depPoiSourceType\":\"Baidu\",\"actualFromLongitude\":121.352141,\"actualFromLatitude\":31.222065,\"actualFromCoordsys\":\"GCJ02\",\"fromAddress\":\"长宁区 金钟路968号\",\"fromName\":\"凌空SOHO\"},\"toPoiDTO\":{\"arrivePoiCode\":\"10002\",\"arrivePoiSourceType\":\"basic_tml\",\"actualToLongitude\":121.347566,\"actualToLatitude\":31.194807,\"actualToCoordsys\":\"GCJ02\",\"toAddress\":\"虹桥国际机场 T1\",\"toName\":\"虹桥国际机场 T1\"},\"highGradeOrder\":1,\"timeZone\":8.00,\"orderDetailExtendInfo\":\"{\\\"useTime\\\":\\\"2024-09-16 16:20:00\\\",\\\"orderSysType\\\":1,\\\"crossBorderType\\\":1}\",\"rushOrder\":0,\"driverOfficialStatus\":null,\"vehicleOfficialStatus\":null,\"orderSysType\":1},\"estimatedCostInfo\":{\"salesAmount\":146.000000,\"costAmount\":136.000000,\"tollFee\":0.000000,\"parkingFee\":20.000000,\"noCommisionAmount\":20.000000,\"supplierBornePrice\":\"null\",\"premiumPrice\":\"[{\\\"strategyId\\\":\\\"3\\\",\\\"feeAmount\\\":10.00},{\\\"strategyId\\\":\\\"6\\\",\\\"feeAmount\\\":0}]\",\"userCurrency\":\"CNY\",\"supplierCurrency\":\"CNY\",\"sc2cnyExchangeRate\":1.000000,\"cny2ucExchangeRate\":1.000000,\"sc2ucExchangeRate\":1.000000,\"dspAddPrice\":0.000000,\"waypointAmount\":0.000000,\"emptyFareFee\":0.00,\"driverCurrency\":\"CNY\",\"sc2driverExchangeRate\":1.000000,\"uc2driverExchangeRate\":1.000000,\"settleToDriver\":null,\"cancelFineRate\":0.00},\"actualCostInfo\":{\"id\":515544,\"drvOrderId\":\"D94863437939146802\",\"dspOrderId\":\"*****************\",\"userOrderId\":\"***********\",\"supplyOrderId\":\"191630232049483849\",\"drvId\":3452141,\"categoryCode\":\"airport_dropoff\",\"overDistanceLength\":0,\"rewardAmount\":1.00,\"waitFee\":0.00,\"overDistanceFee\":0.00,\"overTimeLength\":0,\"overTimeFee\":0.00,\"parkingFee\":0.00,\"accommodationFee\":0.00,\"tollFee\":0,\"auditStatus\":-1,\"auditEffectiveTimeBj\":null,\"payStatus\":-1,\"extraFeeStatus\":3,\"currentExchangeRate\":null,\"userExchangeRate\":null,\"vendorExchangeRate\":null,\"userNeedPayInfo\":\"\",\"confirmTimeLocal\":null,\"confirmTimeBj\":null,\"feeExpirationDateBj\":null,\"feeExpirationDateLocal\":null,\"operator\":\"SYSTEM\",\"welfareMoney\":0.00,\"datachangeCreatetime\":null,\"datachangeLasttime\":null,\"extendRule\":\"{\\\"vendorOverDurationFee\\\":0,\\\"vendorOverKilometreFee\\\":0}\",\"commissionRule\":\"\"},\"sopRecords\":null,\"orderStandardRecord\":null,\"orderPackageServices\":null,\"cancelRecord\":null,\"breakAppointmentRecord\":null,\"orderConfirmRecord\":{\"id\":255354,\"recordId\":3094739390609834165,\"dspOrderId\":\"*****************\",\"supplyOrderId\":\"191630232049483849\",\"userOrderId\":\"***********\",\"categoryCode\":\"airport_dropoff\",\"driverOrderId\":\"D94863437939146802\",\"groupId\":0,\"confirmType\":\"40\",\"confirmTime\":null,\"confirmEvent\":10,\"duid\":\"0-0-0-0-0-0-0-0-0-0\",\"dspRewardAmount\":\"\",\"takenType\":0,\"serviceProviderId\":1,\"serviceProviderPhone\":\"\",\"serviceProviderLanguage\":\"\",\"supplierId\":30804,\"transportGroupId\":186,\"dispatcherPhone\":\"189Pi6i0264\",\"dispatcherPhoneCode\":\"86\",\"dispatcherLanguage\":\"hk\",\"dispatcherName\":\"范德萨\",\"dispatcherEmail\":\"\",\"dispatcherContactType\":\"\",\"dispatcherContactAccount\":\"wechat\",\"overseaDispatcherPhone\":\"\",\"overseaDispatcherPhoneCode\":\"\",\"driverId\":3452141,\"driverName\":\"测试个\",\"driverPic\":\"https://dimg04.c-ctrip.com/images/0412t1200096r7p09AC35.jpg\",\"driverPhone\":\"13298y59687\",\"driverPhoneIntlCode\":\"86\",\"driverVirtualPhone\":\"\",\"driverVirtualPhoneIntlCode\":\"\",\"driverEmail\":\"z.5=<EMAIL>#\",\"driverContactType\":\"wechat\",\"driverContactAccount\":\"\",\"driverLanguage\":\"en\",\"driverSex\":null,\"nucleicAcidTestingResult\":null,\"vaccinationStatus\":null,\"carId\":\"1000129\",\"carLicense\":\"沪G89812\",\"carBrandId\":373,\"carBrandName\":\"荣威\",\"carSeriesId\":3846,\"carSeriesName\":\"e950\",\"vehicleGroupId\":118,\"vehicleGroupName\":\"舒适5座\",\"carColorId\":38,\"carColor\":\"白色\",\"energyType\":63,\"driverProvidePackageServices\":\"[{\\\"categoryCode\\\":\\\"pick_up_card\\\",\\\"driverCanProvide\\\":true},{\\\"categoryCode\\\":\\\"childseat\\\",\\\"driverCanProvide\\\":true}]\",\"isDel\":0,\"flightNo\":\"\",\"flightDate\":\"\",\"plannedDepartureDate\":\"\",\"plannedArrivalDate\":\"\",\"estimateDepartureDate\":\"\",\"estimateArriveDate\":\"\",\"actualDepartureDate\":\"\",\"actualArrivalDate\":\"\",\"departureCityId\":\"\",\"arrivalCityId\":\"\",\"departureAirportCode\":\"\",\"arrivalAirportCode\":\"\",\"departureTerminalId\":\"\",\"arrivalTerminalId\":\"\",\"flightStatus\":\"\",\"overDays\":\"\",\"operateUserAccount\":\"system\",\"operateUserType\":\"systemUser\",\"operateUserName\":\"system\",\"oldOrderStatus\":200,\"newOrderStatus\":240,\"datachangeCreatetime\":null,\"datachangeLasttime\":null,\"dispatcherConfirmType\":1,\"confirmSnapShotInfo\":null,\"goldDriver\":0},\"platformPriceStrategyList\":[{\"id\":682074,\"userOrderId\":\"***********\",\"dspOrderId\":\"*****************\",\"driverOrderId\":\"\",\"countryId\":1,\"countryName\":\"\",\"cityId\":2,\"cityName\":\"\",\"categoryCode\":\"airport_dropoff\",\"carTypeId\":118,\"settleType\":1,\"source\":0,\"strategyJson\":\"{\\\"settleSceneType\\\":1,\\\"countryId\\\":1,\\\"cityId\\\":2,\\\"categoryCode\\\":\\\"airport_dropoff\\\",\\\"settleDetailList\\\":[{\\\"vendorType\\\":1,\\\"billTime\\\":\\\"2024-09-04 14:23:32\\\",\\\"carTypeId\\\":118,\\\"feeType\\\":1,\\\"totalAmount\\\":136.000000,\\\"noCommissionAmount\\\":20.000000,\\\"serviceProviderId\\\":1,\\\"preferential\\\":0},{\\\"vendorType\\\":1,\\\"billTime\\\":\\\"2024-09-04 14:23:32\\\",\\\"carTypeId\\\":118,\\\"feeType\\\":3,\\\"totalAmount\\\":10.00,\\\"noCommissionAmount\\\":0,\\\"serviceProviderId\\\":1,\\\"strategyId\\\":\\\"3\\\",\\\"preferential\\\":0},{\\\"vendorType\\\":1,\\\"billTime\\\":\\\"2024-09-04 14:23:32\\\",\\\"carTypeId\\\":118,\\\"feeType\\\":3,\\\"totalAmount\\\":0,\\\"noCommissionAmount\\\":0,\\\"serviceProviderId\\\":1,\\\"strategyId\\\":\\\"6\\\",\\\"preferential\\\":0},{\\\"vendorType\\\":1,\\\"billTime\\\":\\\"2024-09-04 14:23:32\\\",\\\"carTypeId\\\":118,\\\"feeType\\\":4,\\\"totalAmount\\\":0.000000,\\\"serviceProviderId\\\":1,\\\"preferential\\\":0},{\\\"vendorType\\\":1,\\\"billTime\\\":\\\"2024-09-04 14:23:32\\\",\\\"carTypeId\\\":118,\\\"feeType\\\":5,\\\"totalAmount\\\":20.000000,\\\"serviceProviderId\\\":1,\\\"preferential\\\":0}]}\",\"expectSettlementResult\":\"{\\\"totalSettleAmount\\\":165.000000,\\\"totalCommissionAmount\\\":1.000000,\\\"settleResultDetailList\\\":[{\\\"vendorType\\\":1,\\\"feeType\\\":1,\\\"feeTypeName\\\":\\\"basePrice\\\",\\\"feeAmount\\\":136.000000,\\\"settleAmount\\\":136.00,\\\"commissionAmount\\\":0.00,\\\"shareRatio\\\":1.0000,\\\"shareRuleId\\\":51505,\\\"categoryCode\\\":\\\"airport_dropoff\\\",\\\"carTypeId\\\":118,\\\"serviceProviderId\\\":1,\\\"settleSubjectType\\\":3},{\\\"vendorType\\\":1,\\\"feeType\\\":3,\\\"feeTypeName\\\":\\\"platformAddPriceStrategy\\\",\\\"feeAmount\\\":10.00,\\\"settleAmount\\\":9.00,\\\"commissionAmount\\\":1.00,\\\"shareRatio\\\":0.9000,\\\"shareRuleId\\\":17978,\\\"categoryCode\\\":\\\"airport_dropoff\\\",\\\"strategyId\\\":\\\"3\\\",\\\"carTypeId\\\":118,\\\"serviceProviderId\\\":1,\\\"settleSubjectType\\\":3},{\\\"vendorType\\\":1,\\\"feeType\\\":3,\\\"feeTypeName\\\":\\\"platformAddPriceStrategy\\\",\\\"feeAmount\\\":0,\\\"settleAmount\\\":0.00,\\\"commissionAmount\\\":0.00,\\\"shareRatio\\\":0,\\\"shareRuleId\\\":0,\\\"categoryCode\\\":\\\"airport_dropoff\\\",\\\"strategyId\\\":\\\"6\\\",\\\"comment\\\":\\\"no match any rule\\\",\\\"carTypeId\\\":118,\\\"serviceProviderId\\\":1},{\\\"vendorType\\\":1,\\\"feeType\\\":4,\\\"feeTypeName\\\":\\\"highwayFee\\\",\\\"feeAmount\\\":0.000000,\\\"settleAmount\\\":0.00,\\\"commissionAmount\\\":0.00,\\\"shareRatio\\\":1,\\\"shareRuleId\\\":0,\\\"categoryCode\\\":\\\"airport_dropoff\\\",\\\"carTypeId\\\":118,\\\"serviceProviderId\\\":1,\\\"settleSubjectType\\\":3},{\\\"vendorType\\\":1,\\\"feeType\\\":5,\\\"feeTypeName\\\":\\\"parkingFee\\\",\\\"feeAmount\\\":20.000000,\\\"settleAmount\\\":20.00,\\\"commissionAmount\\\":0.00,\\\"shareRatio\\\":1,\\\"shareRuleId\\\":0,\\\"categoryCode\\\":\\\"airport_dropoff\\\",\\\"carTypeId\\\":118,\\\"serviceProviderId\\\":1,\\\"settleSubjectType\\\":3}]}\",\"actualSettlementResult\":\"{\\\"totalSettleAmount\\\":165.000000,\\\"totalCommissionAmount\\\":1.000000,\\\"settleResultDetailList\\\":[{\\\"vendorType\\\":1,\\\"feeType\\\":1,\\\"feeTypeName\\\":\\\"basePrice\\\",\\\"feeAmount\\\":136.000000,\\\"settleAmount\\\":136.00,\\\"commissionAmount\\\":0.00,\\\"shareRatio\\\":1.0000,\\\"shareRuleId\\\":51505,\\\"categoryCode\\\":\\\"airport_dropoff\\\",\\\"carTypeId\\\":118,\\\"serviceProviderId\\\":1,\\\"settleSubjectType\\\":3},{\\\"vendorType\\\":1,\\\"feeType\\\":3,\\\"feeTypeName\\\":\\\"platformAddPriceStrategy\\\",\\\"feeAmount\\\":10.00,\\\"settleAmount\\\":9.00,\\\"commissionAmount\\\":1.00,\\\"shareRatio\\\":0.9000,\\\"shareRuleId\\\":17978,\\\"categoryCode\\\":\\\"airport_dropoff\\\",\\\"strategyId\\\":\\\"3\\\",\\\"carTypeId\\\":118,\\\"serviceProviderId\\\":1,\\\"settleSubjectType\\\":3},{\\\"vendorType\\\":1,\\\"feeType\\\":3,\\\"feeTypeName\\\":\\\"platformAddPriceStrategy\\\",\\\"feeAmount\\\":0,\\\"settleAmount\\\":0.00,\\\"commissionAmount\\\":0.00,\\\"shareRatio\\\":0,\\\"shareRuleId\\\":0,\\\"categoryCode\\\":\\\"airport_dropoff\\\",\\\"strategyId\\\":\\\"6\\\",\\\"comment\\\":\\\"no match any rule\\\",\\\"carTypeId\\\":118,\\\"serviceProviderId\\\":1},{\\\"vendorType\\\":1,\\\"feeType\\\":4,\\\"feeTypeName\\\":\\\"highwayFee\\\",\\\"feeAmount\\\":0.000000,\\\"settleAmount\\\":0.00,\\\"commissionAmount\\\":0.00,\\\"shareRatio\\\":1,\\\"shareRuleId\\\":0,\\\"categoryCode\\\":\\\"airport_dropoff\\\",\\\"carTypeId\\\":118,\\\"serviceProviderId\\\":1,\\\"settleSubjectType\\\":3},{\\\"vendorType\\\":1,\\\"feeType\\\":5,\\\"feeTypeName\\\":\\\"parkingFee\\\",\\\"feeAmount\\\":20.000000,\\\"settleAmount\\\":20.00,\\\"commissionAmount\\\":0.00,\\\"shareRatio\\\":1,\\\"shareRuleId\\\":0,\\\"categoryCode\\\":\\\"airport_dropoff\\\",\\\"carTypeId\\\":118,\\\"serviceProviderId\\\":1,\\\"settleSubjectType\\\":3}]}\",\"datachangeCreatetime\":null,\"datachangeLasttime\":null}],\"wayPointInfoList\":[]}}";
        QueryOrderDetailForScheduleResponseType responseType = JacksonUtil.deserialize(s, QueryOrderDetailForScheduleResponseType.class);

        SupplyOrder supplyOrder = converter.toSupplyOrderForSchedule(responseType);
        Assert.assertNotNull(supplyOrder);
        Assert.assertEquals("***********", supplyOrder.getSourceOrderId());
        Assert.assertEquals("*****************", supplyOrder.getOrderId());
        Assert.assertEquals("2", supplyOrder.getCityCode());
        Assert.assertEquals(2, supplyOrder.getCityId().intValue());
        Assert.assertEquals(118, supplyOrder.getCarTypeId().intValue());
        Assert.assertEquals(1, supplyOrder.getServiceType().intValue());
        Assert.assertEquals(3, supplyOrder.getOrderStatus().intValue());
        Assert.assertEquals(0, supplyOrder.getOrderStatusDetail().intValue());
        Assert.assertEquals(0, supplyOrder.getIsOtaBookOrder().intValue());
        Assert.assertEquals(49737, supplyOrder.getSkuId().longValue());
        Assert.assertEquals("airport_dropoff", supplyOrder.getCategoryCode());
        Assert.assertEquals(30804, supplyOrder.getSupplierId().longValue());
        Assert.assertEquals(7.00, supplyOrder.getPredictPriceInfo().getKiloLength(), 0.01);
        Assert.assertEquals(15.00, supplyOrder.getPredictPriceInfo().getTimeLength(), 0.01);
    }



    @Test
    public void test5() {

        String s = "{\"ResponseStatus\":{\"Timestamp\":\"/Date(1725431042615+0800)/\",\"Ack\":\"Success\",\"Errors\":[],\"Build\":null,\"Version\":null,\"Extension\":[]},\"responseResult\":{\"success\":true,\"returnCode\":\"200\",\"returnMessage\":\"服务执行成功\"},\"orderDetail\":{\"baseDetail\":{\"userOrderId\":\"***********\",\"supplyOrderId\":\"191630232049483849\",\"dspOrderId\":\"*****************\",\"driverOrderId\":null,\"orderVersion\":5,\"confirmRecordId\":3094739390609834165,\"orderStatus\":240,\"oldOrderStatus\":3,\"oldOrderStatusDetail\":0,\"orderSource\":1,\"productType\":0,\"productCode\":\"\",\"productName\":\"\",\"categoryCode\":\"airport_dropoff\",\"serviceType\":1,\"cityId\":2,\"fromCityId\":2,\"toCityId\":2,\"vehicleGroupId\":118,\"estimatedUseTime\":\"2024-09-16 16:20:00\",\"estimatedUseTimeBj\":\"2024-09-16 16:20:00\",\"predicServiceStopTimeBj\":\"2024-09-16 16:35:00\",\"lastConfirmTime\":\"2024-09-15 16:20:00\",\"lastConfirmTimeBj\":\"2024-09-15 16:20:00\",\"lastConfirmCarTime\":\"2024-09-16 14:20:00\",\"lastConfirmCarTimeBj\":\"2024-09-16 14:20:00\",\"connectMode\":1,\"salesMode\":5,\"useDays\":1.00,\"estimatedKm\":7.00,\"estimatedMin\":15.00,\"actualKm\":0.00,\"actualMin\":0.00,\"priceMark\":\"103412\",\"serviceProviderId\":1,\"supplierId\":30804,\"countryId\":1,\"locale\":\"zh-cn\",\"skuId\":49737,\"cancelRule\":\"{\\\"timeType\\\":3,\\\"freeCancel\\\":true,\\\"timeZone\\\":8,\\\"timeInterval\\\":0,\\\"timeIntervalUnit\\\":\\\"MINUTES\\\",\\\"timeIntervalUnitDesc\\\":\\\"分钟\\\",\\\"cancelFeeRate\\\":50,\\\"freeCancelTime\\\":\\\"2024-09-16 16:20:00\\\",\\\"freeCancelTimeBJ\\\":\\\"2024-09-16 16:20:00\\\"}\",\"waitingRule\":\"[{\\\"referenceTimeType\\\":1,\\\"type\\\":0,\\\"waitingTime\\\":10,\\\"waitingTimeUnit\\\":\\\"MINUTES\\\",\\\"waitingTimeUnitDesc\\\":\\\"分钟\\\",\\\"amount\\\":1.00,\\\"currency\\\":\\\"CNY\\\",\\\"overWaitingTime\\\":1,\\\"overWaitingTimeUnit\\\":\\\"MINUTES\\\",\\\"overWaitingTimeUnitDesc\\\":\\\"分钟\\\",\\\"active\\\":true,\\\"freeWait\\\":false}]\",\"priceResultCode\":\"34vk_8iat_0_9df4,9dfi,9dfp,9dfb_0_0_1_0_0_3\",\"detailSnapShotid\":\"\",\"xproductInfo\":\"null\",\"spContractInfo\":\"{\\\"contact\\\":{\\\"id\\\":782282,\\\"supplierId\\\":30804,\\\"serviceProviderId\\\":1,\\\"code\\\":\\\"SHCT2022072100057778\\\",\\\"name\\\":\\\"用车平台协议模板 随县统计局555）\\\",\\\"status\\\":8,\\\"statusDesc\\\":\\\"已签署\\\",\\\"salesMode\\\":2,\\\"salesModeDesc\\\":\\\"零售\\\",\\\"settlementMethod\\\":2,\\\"priceTerms\\\":2,\\\"tocSettlementPeriodType\\\":\\\"E\\\",\\\"tobSettlementPeriodType\\\":\\\"\\\",\\\"partA\\\":\\\"上海华程西南国际旅行社有限公司\\\",\\\"type\\\":1,\\\"tocSettlementDateType\\\":1,\\\"tobSettlementDateType\\\":0,\\\"paySiteId\\\":2,\\\"partCPaySiteId\\\":0,\\\"invoicePattern\\\":2},\\\"supplier\\\":{\\\"id\\\":30804,\\\"name\\\":\\\"随县统计局555）\\\"},\\\"serviceProviderBaseInfo\\\":{\\\"id\\\":1,\\\"carrierLocalName\\\":\\\"天津海豚出游科技有限公司\\\",\\\"salesMode\\\":5}}\",\"dspStrategyStr\":\"{\\\"dspStrategyId\\\":1003,\\\"skuType\\\":1,\\\"dspData\\\":\\\"{\\\\\\\"skuType\\\\\\\":\\\\\\\"1\\\\\\\",\\\\\\\"beginSeconds\\\\\\\":\\\\\\\"0\\\\\\\",\\\\\\\"useSeconds\\\\\\\":\\\\\\\"0\\\\\\\",\\\\\\\"addPriceRatio\\\\\\\":\\\\\\\"0\\\\\\\",\\\\\\\"addPriceMoney\\\\\\\":\\\\\\\"0\\\\\\\",\\\\\\\"maxAddPriceRatioConf\\\\\\\":\\\\\\\"0\\\\\\\",\\\\\\\"maxAddPriceMoneyConf\\\\\\\":\\\\\\\"0\\\\\\\",\\\\\\\"addPriceRatioConf\\\\\\\":\\\\\\\"0\\\\\\\",\\\\\\\"addPriceMoneyConf\\\\\\\":\\\\\\\"0\\\\\\\",\\\\\\\"reason\\\\\\\":99,\\\\\\\"sceneId\\\\\\\":\\\\\\\"2022\\\\\\\",\\\\\\\"canDown\\\\\\\":false,\\\\\\\"humanDsp\\\\\\\":false,\\\\\\\"bookFirstFlag\\\\\\\":true,\\\\\\\"dspStrategyId\\\\\\\":1003,\\\\\\\"retryNum\\\\\\\":0,\\\\\\\"grade\\\\\\\":\\\\\\\"1_1\\\\\\\"}\\\",\\\"bookFirstFlag\\\":true}\",\"orderCarTypeId\":118,\"drvId\":null,\"drvName\":null,\"drvPhone\":null,\"drvPhoneAreaCode\":null,\"drvCoopMode\":null,\"drvLanguageCodeList\":null,\"carId\":null,\"carTypeId\":null,\"carLicense\":null,\"carColorId\":null,\"carBrandId\":null,\"carSeriesId\":null,\"transportGroupId\":null,\"transportGroupMode\":null,\"transportGroupName\":null,\"confirmMoneyTimeLocal\":null,\"confirmMoneyTimeBj\":null,\"finishTimeBj\":null,\"cancelTimeLocal\":null,\"cancelTimeBj\":null,\"bizAreaType\":32,\"adultCount\":2,\"childCount\":0,\"bagCount\":0,\"maxBagCount\":null,\"coordType\":\"GCJ02\",\"fromLongitude\":121.352141,\"fromLatitude\":31.222065,\"toLongitude\":121.347566,\"toLatitude\":31.194807,\"takenTimeBj\":null,\"targetId\":\"SHA\",\"terminalId\":\"10002\",\"fixedLocationType\":2,\"fixedPosition\":\"SHA\",\"premiumOrderFlag\":0,\"dspOrderUpdateTime\":\"2024-09-04 14:23:57\",\"dspOrderCreateTime\":\"2024-09-04 14:23:32\",\"drvOrderUpdateTime\":null,\"drvOrderCreateTime\":null,\"newPayProcess\":0,\"uid\":\"*********\",\"distributionChannelId\":5,\"giveFreeTime\":0,\"highUser\":1,\"dispatchLimitType\":null,\"allotDriverTimeBj\":null,\"fromPoiDTO\":{\"depPoiCode\":\"bb57cf24e13ece3e43afe640\",\"depPoiSourceType\":\"Baidu\",\"actualFromLongitude\":121.352141,\"actualFromLatitude\":31.222065,\"actualFromCoordsys\":\"GCJ02\",\"fromAddress\":\"长宁区 金钟路968号\",\"fromName\":\"凌空SOHO\"},\"toPoiDTO\":{\"arrivePoiCode\":\"10002\",\"arrivePoiSourceType\":\"basic_tml\",\"actualToLongitude\":121.347566,\"actualToLatitude\":31.194807,\"actualToCoordsys\":\"GCJ02\",\"toAddress\":\"虹桥国际机场 T1\",\"toName\":\"虹桥国际机场 T1\"},\"highGradeOrder\":1,\"timeZone\":8.00,\"orderDetailExtendInfo\":\"{\\\"useTime\\\":\\\"2024-09-16 16:20:00\\\",\\\"orderSysType\\\":1,\\\"crossBorderType\\\":1}\",\"rushOrder\":0,\"driverOfficialStatus\":null,\"vehicleOfficialStatus\":null,\"orderSysType\":1},\"estimatedCostInfo\":{\"salesAmount\":146.000000,\"costAmount\":136.000000,\"tollFee\":0.000000,\"parkingFee\":20.000000,\"noCommisionAmount\":20.000000,\"supplierBornePrice\":\"null\",\"premiumPrice\":\"[{\\\"strategyId\\\":\\\"3\\\",\\\"feeAmount\\\":10.00},{\\\"strategyId\\\":\\\"6\\\",\\\"feeAmount\\\":0}]\",\"userCurrency\":\"CNY\",\"supplierCurrency\":\"CNY\",\"sc2cnyExchangeRate\":1.000000,\"cny2ucExchangeRate\":1.000000,\"sc2ucExchangeRate\":1.000000,\"dspAddPrice\":0.000000,\"waypointAmount\":0.000000,\"emptyFareFee\":0.00,\"driverCurrency\":\"CNY\",\"sc2driverExchangeRate\":1.000000,\"uc2driverExchangeRate\":1.000000,\"settleToDriver\":null,\"cancelFineRate\":0.00},\"actualCostInfo\":{\"id\":515544,\"drvOrderId\":\"D94863437939146802\",\"dspOrderId\":\"*****************\",\"userOrderId\":\"***********\",\"supplyOrderId\":\"191630232049483849\",\"drvId\":3452141,\"categoryCode\":\"airport_dropoff\",\"overDistanceLength\":0,\"rewardAmount\":1.00,\"waitFee\":0.00,\"overDistanceFee\":0.00,\"overTimeLength\":0,\"overTimeFee\":0.00,\"parkingFee\":0.00,\"accommodationFee\":0.00,\"tollFee\":0,\"auditStatus\":-1,\"auditEffectiveTimeBj\":null,\"payStatus\":-1,\"extraFeeStatus\":3,\"currentExchangeRate\":null,\"userExchangeRate\":null,\"vendorExchangeRate\":null,\"userNeedPayInfo\":\"\",\"confirmTimeLocal\":null,\"confirmTimeBj\":null,\"feeExpirationDateBj\":null,\"feeExpirationDateLocal\":null,\"operator\":\"SYSTEM\",\"welfareMoney\":0.00,\"datachangeCreatetime\":null,\"datachangeLasttime\":null,\"extendRule\":\"{\\\"vendorOverDurationFee\\\":0,\\\"vendorOverKilometreFee\\\":0}\",\"commissionRule\":\"\"},\"sopRecords\":null,\"orderStandardRecord\":null,\"orderPackageServices\":null,\"cancelRecord\":null,\"breakAppointmentRecord\":null,\"orderConfirmRecord\":{\"id\":255354,\"recordId\":3094739390609834165,\"dspOrderId\":\"*****************\",\"supplyOrderId\":\"191630232049483849\",\"userOrderId\":\"***********\",\"categoryCode\":\"airport_dropoff\",\"driverOrderId\":\"D94863437939146802\",\"groupId\":0,\"confirmType\":\"40\",\"confirmTime\":null,\"confirmEvent\":10,\"duid\":\"0-0-0-0-0-0-0-0-0-0\",\"dspRewardAmount\":\"\",\"takenType\":0,\"serviceProviderId\":1,\"serviceProviderPhone\":\"\",\"serviceProviderLanguage\":\"\",\"supplierId\":30804,\"transportGroupId\":186,\"dispatcherPhone\":\"189Pi6i0264\",\"dispatcherPhoneCode\":\"86\",\"dispatcherLanguage\":\"hk\",\"dispatcherName\":\"范德萨\",\"dispatcherEmail\":\"\",\"dispatcherContactType\":\"\",\"dispatcherContactAccount\":\"wechat\",\"overseaDispatcherPhone\":\"\",\"overseaDispatcherPhoneCode\":\"\",\"driverId\":3452141,\"driverName\":\"测试个\",\"driverPic\":\"https://dimg04.c-ctrip.com/images/0412t1200096r7p09AC35.jpg\",\"driverPhone\":\"13298y59687\",\"driverPhoneIntlCode\":\"86\",\"driverVirtualPhone\":\"\",\"driverVirtualPhoneIntlCode\":\"\",\"driverEmail\":\"z.5=<EMAIL>#\",\"driverContactType\":\"wechat\",\"driverContactAccount\":\"\",\"driverLanguage\":\"en\",\"driverSex\":null,\"nucleicAcidTestingResult\":null,\"vaccinationStatus\":null,\"carId\":\"1000129\",\"carLicense\":\"沪G89812\",\"carBrandId\":373,\"carBrandName\":\"荣威\",\"carSeriesId\":3846,\"carSeriesName\":\"e950\",\"vehicleGroupId\":118,\"vehicleGroupName\":\"舒适5座\",\"carColorId\":38,\"carColor\":\"白色\",\"energyType\":63,\"driverProvidePackageServices\":\"[{\\\"categoryCode\\\":\\\"pick_up_card\\\",\\\"driverCanProvide\\\":true},{\\\"categoryCode\\\":\\\"childseat\\\",\\\"driverCanProvide\\\":true}]\",\"isDel\":0,\"flightNo\":\"\",\"flightDate\":\"\",\"plannedDepartureDate\":\"\",\"plannedArrivalDate\":\"\",\"estimateDepartureDate\":\"\",\"estimateArriveDate\":\"\",\"actualDepartureDate\":\"\",\"actualArrivalDate\":\"\",\"departureCityId\":\"\",\"arrivalCityId\":\"\",\"departureAirportCode\":\"\",\"arrivalAirportCode\":\"\",\"departureTerminalId\":\"\",\"arrivalTerminalId\":\"\",\"flightStatus\":\"\",\"overDays\":\"\",\"operateUserAccount\":\"system\",\"operateUserType\":\"systemUser\",\"operateUserName\":\"system\",\"oldOrderStatus\":200,\"newOrderStatus\":240,\"datachangeCreatetime\":null,\"datachangeLasttime\":null,\"dispatcherConfirmType\":1,\"confirmSnapShotInfo\":null,\"goldDriver\":0},\"platformPriceStrategyList\":[{\"id\":682074,\"userOrderId\":\"***********\",\"dspOrderId\":\"*****************\",\"driverOrderId\":\"\",\"countryId\":1,\"countryName\":\"\",\"cityId\":2,\"cityName\":\"\",\"categoryCode\":\"airport_dropoff\",\"carTypeId\":118,\"settleType\":1,\"source\":0,\"strategyJson\":\"{\\\"settleSceneType\\\":1,\\\"countryId\\\":1,\\\"cityId\\\":2,\\\"categoryCode\\\":\\\"airport_dropoff\\\",\\\"settleDetailList\\\":[{\\\"vendorType\\\":1,\\\"billTime\\\":\\\"2024-09-04 14:23:32\\\",\\\"carTypeId\\\":118,\\\"feeType\\\":1,\\\"totalAmount\\\":136.000000,\\\"noCommissionAmount\\\":20.000000,\\\"serviceProviderId\\\":1,\\\"preferential\\\":0},{\\\"vendorType\\\":1,\\\"billTime\\\":\\\"2024-09-04 14:23:32\\\",\\\"carTypeId\\\":118,\\\"feeType\\\":3,\\\"totalAmount\\\":10.00,\\\"noCommissionAmount\\\":0,\\\"serviceProviderId\\\":1,\\\"strategyId\\\":\\\"3\\\",\\\"preferential\\\":0},{\\\"vendorType\\\":1,\\\"billTime\\\":\\\"2024-09-04 14:23:32\\\",\\\"carTypeId\\\":118,\\\"feeType\\\":3,\\\"totalAmount\\\":0,\\\"noCommissionAmount\\\":0,\\\"serviceProviderId\\\":1,\\\"strategyId\\\":\\\"6\\\",\\\"preferential\\\":0},{\\\"vendorType\\\":1,\\\"billTime\\\":\\\"2024-09-04 14:23:32\\\",\\\"carTypeId\\\":118,\\\"feeType\\\":4,\\\"totalAmount\\\":0.000000,\\\"serviceProviderId\\\":1,\\\"preferential\\\":0},{\\\"vendorType\\\":1,\\\"billTime\\\":\\\"2024-09-04 14:23:32\\\",\\\"carTypeId\\\":118,\\\"feeType\\\":5,\\\"totalAmount\\\":20.000000,\\\"serviceProviderId\\\":1,\\\"preferential\\\":0}]}\",\"expectSettlementResult\":\"{\\\"totalSettleAmount\\\":165.000000,\\\"totalCommissionAmount\\\":1.000000,\\\"settleResultDetailList\\\":[{\\\"vendorType\\\":1,\\\"feeType\\\":1,\\\"feeTypeName\\\":\\\"basePrice\\\",\\\"feeAmount\\\":136.000000,\\\"settleAmount\\\":136.00,\\\"commissionAmount\\\":0.00,\\\"shareRatio\\\":1.0000,\\\"shareRuleId\\\":51505,\\\"categoryCode\\\":\\\"airport_dropoff\\\",\\\"carTypeId\\\":118,\\\"serviceProviderId\\\":1,\\\"settleSubjectType\\\":3},{\\\"vendorType\\\":1,\\\"feeType\\\":3,\\\"feeTypeName\\\":\\\"platformAddPriceStrategy\\\",\\\"feeAmount\\\":10.00,\\\"settleAmount\\\":9.00,\\\"commissionAmount\\\":1.00,\\\"shareRatio\\\":0.9000,\\\"shareRuleId\\\":17978,\\\"categoryCode\\\":\\\"airport_dropoff\\\",\\\"strategyId\\\":\\\"3\\\",\\\"carTypeId\\\":118,\\\"serviceProviderId\\\":1,\\\"settleSubjectType\\\":3},{\\\"vendorType\\\":1,\\\"feeType\\\":3,\\\"feeTypeName\\\":\\\"platformAddPriceStrategy\\\",\\\"feeAmount\\\":0,\\\"settleAmount\\\":0.00,\\\"commissionAmount\\\":0.00,\\\"shareRatio\\\":0,\\\"shareRuleId\\\":0,\\\"categoryCode\\\":\\\"airport_dropoff\\\",\\\"strategyId\\\":\\\"6\\\",\\\"comment\\\":\\\"no match any rule\\\",\\\"carTypeId\\\":118,\\\"serviceProviderId\\\":1},{\\\"vendorType\\\":1,\\\"feeType\\\":4,\\\"feeTypeName\\\":\\\"highwayFee\\\",\\\"feeAmount\\\":0.000000,\\\"settleAmount\\\":0.00,\\\"commissionAmount\\\":0.00,\\\"shareRatio\\\":1,\\\"shareRuleId\\\":0,\\\"categoryCode\\\":\\\"airport_dropoff\\\",\\\"carTypeId\\\":118,\\\"serviceProviderId\\\":1,\\\"settleSubjectType\\\":3},{\\\"vendorType\\\":1,\\\"feeType\\\":5,\\\"feeTypeName\\\":\\\"parkingFee\\\",\\\"feeAmount\\\":20.000000,\\\"settleAmount\\\":20.00,\\\"commissionAmount\\\":0.00,\\\"shareRatio\\\":1,\\\"shareRuleId\\\":0,\\\"categoryCode\\\":\\\"airport_dropoff\\\",\\\"carTypeId\\\":118,\\\"serviceProviderId\\\":1,\\\"settleSubjectType\\\":3}]}\",\"actualSettlementResult\":\"{\\\"totalSettleAmount\\\":165.000000,\\\"totalCommissionAmount\\\":1.000000,\\\"settleResultDetailList\\\":[{\\\"vendorType\\\":1,\\\"feeType\\\":1,\\\"feeTypeName\\\":\\\"basePrice\\\",\\\"feeAmount\\\":136.000000,\\\"settleAmount\\\":136.00,\\\"commissionAmount\\\":0.00,\\\"shareRatio\\\":1.0000,\\\"shareRuleId\\\":51505,\\\"categoryCode\\\":\\\"airport_dropoff\\\",\\\"carTypeId\\\":118,\\\"serviceProviderId\\\":1,\\\"settleSubjectType\\\":3},{\\\"vendorType\\\":1,\\\"feeType\\\":3,\\\"feeTypeName\\\":\\\"platformAddPriceStrategy\\\",\\\"feeAmount\\\":10.00,\\\"settleAmount\\\":9.00,\\\"commissionAmount\\\":1.00,\\\"shareRatio\\\":0.9000,\\\"shareRuleId\\\":17978,\\\"categoryCode\\\":\\\"airport_dropoff\\\",\\\"strategyId\\\":\\\"3\\\",\\\"carTypeId\\\":118,\\\"serviceProviderId\\\":1,\\\"settleSubjectType\\\":3},{\\\"vendorType\\\":1,\\\"feeType\\\":3,\\\"feeTypeName\\\":\\\"platformAddPriceStrategy\\\",\\\"feeAmount\\\":0,\\\"settleAmount\\\":0.00,\\\"commissionAmount\\\":0.00,\\\"shareRatio\\\":0,\\\"shareRuleId\\\":0,\\\"categoryCode\\\":\\\"airport_dropoff\\\",\\\"strategyId\\\":\\\"6\\\",\\\"comment\\\":\\\"no match any rule\\\",\\\"carTypeId\\\":118,\\\"serviceProviderId\\\":1},{\\\"vendorType\\\":1,\\\"feeType\\\":4,\\\"feeTypeName\\\":\\\"highwayFee\\\",\\\"feeAmount\\\":0.000000,\\\"settleAmount\\\":0.00,\\\"commissionAmount\\\":0.00,\\\"shareRatio\\\":1,\\\"shareRuleId\\\":0,\\\"categoryCode\\\":\\\"airport_dropoff\\\",\\\"carTypeId\\\":118,\\\"serviceProviderId\\\":1,\\\"settleSubjectType\\\":3},{\\\"vendorType\\\":1,\\\"feeType\\\":5,\\\"feeTypeName\\\":\\\"parkingFee\\\",\\\"feeAmount\\\":20.000000,\\\"settleAmount\\\":20.00,\\\"commissionAmount\\\":0.00,\\\"shareRatio\\\":1,\\\"shareRuleId\\\":0,\\\"categoryCode\\\":\\\"airport_dropoff\\\",\\\"carTypeId\\\":118,\\\"serviceProviderId\\\":1,\\\"settleSubjectType\\\":3}]}\",\"datachangeCreatetime\":null,\"datachangeLasttime\":null}],\"wayPointInfoList\":[]}}";
        QueryOrderDetailForScheduleResponseType responseType = JacksonUtil.deserialize(s, QueryOrderDetailForScheduleResponseType.class);

        SupplyOrder supplyOrder = converter.toSupplyOrderForSchedule(responseType);
        Assert.assertNotNull(supplyOrder);
        Assert.assertEquals("***********", supplyOrder.getSourceOrderId());
        Assert.assertEquals("*****************", supplyOrder.getOrderId());
        Assert.assertEquals("2", supplyOrder.getCityCode());
        Assert.assertEquals(2, supplyOrder.getCityId().intValue());
        Assert.assertEquals(118, supplyOrder.getCarTypeId().intValue());
        Assert.assertEquals(1, supplyOrder.getServiceType().intValue());
        Assert.assertEquals(3, supplyOrder.getOrderStatus().intValue());
        Assert.assertEquals(0, supplyOrder.getOrderStatusDetail().intValue());
        Assert.assertEquals(0, supplyOrder.getIsOtaBookOrder().intValue());
        Assert.assertEquals(49737, supplyOrder.getSkuId().longValue());
        Assert.assertEquals("airport_dropoff", supplyOrder.getCategoryCode());
        Assert.assertEquals(30804, supplyOrder.getSupplierId().longValue());
        Assert.assertEquals(7.00, supplyOrder.getPredictPriceInfo().getKiloLength(), 0.01);
        Assert.assertEquals(15.00, supplyOrder.getPredictPriceInfo().getTimeLength(), 0.01);
    }

    @Test
    public void test6() {
        BaseDetail baseDetail = new BaseDetail();
        baseDetail.setUserOrderId("");
        baseDetail.setSupplyOrderId("");
        baseDetail.setDspOrderId("");
        baseDetail.setDriverOrderId("");
        baseDetail.setConfirmRecordId(0L);
        baseDetail.setOrderStatus(0);
        baseDetail.setOldOrderStatus(0);
        baseDetail.setOldOrderStatusDetail(0);
        baseDetail.setProductType(0);
        baseDetail.setProductCode("");
        baseDetail.setProductName("");
        baseDetail.setCategoryCode("");
        baseDetail.setServiceType(0);
        baseDetail.setCityId(0);
        baseDetail.setFromCityId(0);
        baseDetail.setToCityId(0);
        baseDetail.setVehicleGroupId(0);
        baseDetail.setEstimatedUseTime("");
        baseDetail.setEstimatedUseTimeBj("");
        baseDetail.setPredicServiceStopTimeBj("");
        baseDetail.setLastConfirmTime("");
        baseDetail.setLastConfirmTimeBj("");
        baseDetail.setLastConfirmCarTime("");
        baseDetail.setLastConfirmCarTimeBj("");
        baseDetail.setConnectMode(0);
        baseDetail.setSalesMode(0);
        baseDetail.setUseDays(new BigDecimal("0"));
        baseDetail.setEstimatedKm(new BigDecimal("0"));
        baseDetail.setEstimatedMin(new BigDecimal("0"));
        baseDetail.setActualKm(new BigDecimal("0"));
        baseDetail.setActualMin(new BigDecimal("0"));
        baseDetail.setPriceMark("");
        baseDetail.setServiceProviderId(0);
        baseDetail.setSupplierId(0);
        baseDetail.setCountryId(0L);
        baseDetail.setLocale("");
        baseDetail.setSkuId(0);
        baseDetail.setCancelRule("");
        baseDetail.setWaitingRule("");
        baseDetail.setPriceResultCode("");
        baseDetail.setDetailSnapShotid("");
        baseDetail.setXproductInfo("");
        baseDetail.setSpContractInfo("");
        baseDetail.setDspStrategyStr("");
        baseDetail.setOrderCarTypeId(0L);
        baseDetail.setDrvId(0L);
        baseDetail.setDrvName("");
        baseDetail.setDrvPhone("");
        baseDetail.setDrvPhoneAreaCode("");
        baseDetail.setDrvCoopMode(0);
        baseDetail.setDrvLanguageCodeList("");
        baseDetail.setCarId(0L);
        baseDetail.setCarTypeId(0L);
        baseDetail.setCarLicense("");
        baseDetail.setCarColorId(0L);
        baseDetail.setCarBrandId(0L);
        baseDetail.setCarSeriesId(0L);
        baseDetail.setTransportGroupId(0L);
        baseDetail.setTransportGroupMode(0);
        baseDetail.setTransportGroupName("");
        baseDetail.setEstimatedUseTime("");
        baseDetail.setEstimatedUseTimeBj("");
        baseDetail.setPredicServiceStopTimeBj("");
        baseDetail.setConfirmMoneyTimeLocal("");
        baseDetail.setConfirmMoneyTimeBj("");
        baseDetail.setFinishTimeBj("");
        baseDetail.setCancelTimeLocal("");
        baseDetail.setCancelTimeBj("");
        baseDetail.setBizAreaType(0);
        baseDetail.setAdultCount(0);
        baseDetail.setChildCount(0);
        baseDetail.setBagCount(0);
        baseDetail.setMaxBagCount(0);
        baseDetail.setCoordType("");
        baseDetail.setFromLongitude(0.0D);
        baseDetail.setFromLatitude(0.0D);
        baseDetail.setToLongitude(0.0D);
        baseDetail.setToLatitude(0.0D);
        baseDetail.setTakenTimeBj("");
        baseDetail.setTargetId("");
        baseDetail.setTerminalId("");
        baseDetail.setFixedLocationType(0);
        baseDetail.setFixedPosition("");
        baseDetail.setPremiumOrderFlag(0);
        baseDetail.setDspOrderUpdateTime("");
        baseDetail.setDspOrderCreateTime("");
        baseDetail.setDrvOrderUpdateTime("");
        baseDetail.setDrvOrderCreateTime("");
        baseDetail.setNewPayProcess(0);
        baseDetail.setUid("");
        baseDetail.setDistributionChannelId(0);
        baseDetail.setGiveFreeTime(0);
        baseDetail.setHighGradeOrder(1);

        OrderDetail orderDetail = new OrderDetail();
        orderDetail.setBaseDetail(baseDetail);
        orderDetail.setEstimatedCostInfo(new EstimatedCostInfo());
        orderDetail.setActualCostInfo(new ActualCostInfo());
        orderDetail.setSopRecords(Lists.newArrayList());
        orderDetail.setOrderStandardRecord(new OrderStandardRecord());
        orderDetail.setOrderPackageServices(Lists.newArrayList());
        orderDetail.setCancelRecord(new CancelRecord());
        orderDetail.setBreakAppointmentRecord(new BreakAppointmentRecord());
        orderDetail.setOrderConfirmRecord(new OrderConfirmRecord());
//        orderDetail.setPlatformPriceStrategy(new PlatformPriceStrategy());
        orderDetail.setOrderExtendAttributeInfo(Lists.newArrayList(new OrderExtendAttributeDTO("user_level_code", "a")));

        QueryOrderDetailResponseType responseType = new QueryOrderDetailResponseType();
        responseType.setResponseStatus(new ResponseStatusType());
        responseType.setResponseResult(new ResponseResult());
        responseType.setOrderDetail(orderDetail);


        SupplyOrder supplyOrder = converter.toSupplyOrder(responseType);
        Assert.assertEquals("0", supplyOrder.getIsVipUserLevel().toString());
    }
}
