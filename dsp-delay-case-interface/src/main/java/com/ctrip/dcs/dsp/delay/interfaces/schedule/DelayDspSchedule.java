package com.ctrip.dcs.dsp.delay.interfaces.schedule;

import com.ctrip.dcs.dsp.delay.consts.CommonConstant;
import com.ctrip.dcs.dsp.delay.enums.DelayDspTaskStatus;
import com.ctrip.dcs.dsp.delay.model.DelayDspTask;
import com.ctrip.dcs.dsp.delay.mq.MessageProducer;
import com.ctrip.dcs.dsp.delay.qconfig.DelayDspCommonQConfig;
import com.ctrip.dcs.dsp.delay.repository.DelayDspTaskRepository;
import com.ctrip.dcs.dsp.delay.util.JsonUtil;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerContext;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.dianping.cat.Cat;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qschedule.config.QSchedule;
import qunar.tc.schedule.Parameter;

import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * 匹配任务执行
 * <AUTHOR>
 */
@Component
public class DelayDspSchedule {

    private static final Logger logger = LoggerFactory.getLogger(DelayDspSchedule.class);

    private static final long DEFAULT_INTERVAL = 5000L;

    @Autowired
    private DelayDspTaskRepository delayDspTaskRepository;

    @Autowired
    private MessageProducer messageProducer;

    @Autowired
    private DelayDspCommonQConfig delayDspCommonQConfig;

    @QSchedule("dcs.dsp.delay.task.run.task")
    public void execute(Parameter parameter) {
        LoggerContext.newContext().withGlobalTraceId(UUID.randomUUID().toString()).init();
        Long interval = parameter.getProperty("interval", Long.class);
        if (interval == null) {
            interval = DEFAULT_INTERVAL;
        }
        logger.info("execute qschedule","interval: {}", interval);
        List<DelayDspTask> tasks = delayDspTaskRepository.queryByTime(new Date());
        logger.info("execute qschedule","schedule name is dcs.dsp.delay.run.task! task: {}", JsonUtil.toJson(tasks));
        tasks.sort(Comparator.comparing(DelayDspTask::getExecuteTimeDeadline));
        long delay = 0;
        for (DelayDspTask task : tasks) {
            delayDspTaskRepository.update(task.getTaskId(), DelayDspTaskStatus.EXECUTING.getCode());
            Map<String, Object> map = Maps.newHashMap();
            map.put("taskId", task.getTaskId());
            map.put("cityId", task.getCityId());
            map.put("carTypeId", task.getCarTypeId());
            map.put("taskType",task.getTaskType());
            messageProducer.sendDelayMessage(CommonConstant.DELAY_DSP_TASK_RUN_SUBJECT, map, delay);
            messageProducer.sendDelayMessage(CommonConstant.DELAY_DSP_TASK_RUN_CHECK_SUBJECT, map, delay + delayDspCommonQConfig.getCheckTaskRunMillisecond());
            delay += interval;
        }
    }
}
