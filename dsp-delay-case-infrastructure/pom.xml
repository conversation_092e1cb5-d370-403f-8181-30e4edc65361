<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <!-- super pom -->
    <parent>
        <groupId>com.ctrip.dcs.dsp</groupId>
        <artifactId>dsp-delay-case</artifactId>
        <version>1.0.0</version>
    </parent>

    <!-- port module -->
    <groupId>com.ctrip.dcs.dsp</groupId>
    <artifactId>dsp-delay-case-infrastructure</artifactId>
    <version>1.0.0</version>
    <name>dsp-delay-case-infrastructure</name>
    <url>http://www.ctrip.com</url>
    <modelVersion>4.0.0</modelVersion>

    <!-- dependencies -->
    <dependencies>
        <!-- framwork common -->
        <dependency>
            <groupId>com.ctrip.igt.framework</groupId>
            <artifactId>common</artifactId>
        </dependency>
        <!-- client -->
        <dependency>
            <groupId>com.ctrip.igt.framework</groupId>
            <artifactId>soa-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.igt.framework</groupId>
            <artifactId>soa-server</artifactId>
        </dependency>
        <!-- dal -->
        <dependency>
            <groupId>com.ctrip.igt.framework</groupId>
            <artifactId>dal</artifactId>
        </dependency>
        <!-- http -->
        <dependency>
            <groupId>com.ctrip.igt.framework</groupId>
            <artifactId>http</artifactId>
        </dependency>
        <!-- qmq -->
        <dependency>
            <groupId>com.ctrip.igt.framework</groupId>
            <artifactId>qmq</artifactId>
        </dependency>
        <!-- qconfig -->
        <dependency>
            <groupId>com.ctrip.igt.framework</groupId>
            <artifactId>qconfig</artifactId>
        </dependency>
        <!-- cache -->
        <dependency>
            <groupId>com.ctrip.igt.framework</groupId>
            <artifactId>cache</artifactId>
        </dependency>
        <!-- redis -->
        <dependency>
            <groupId>com.ctrip.igt.framework</groupId>
            <artifactId>redis</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mybatis.spring.boot</groupId>
            <artifactId>mybatis-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
        </dependency>
        <!-- domain -->
        <dependency>
            <groupId>com.ctrip.dcs.dsp</groupId>
            <artifactId>dsp-delay-case-domain</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.soa.dcs.shopping</groupId>
            <artifactId>shopping-common-service</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.dcs.tms</groupId>
            <artifactId>transport-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.soa.car.och.geoservice.v1</groupId>
            <artifactId>geoservice</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.soa.dcs.jnt.price</groupId>
            <artifactId>price-shopping</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.soa.21828</groupId>
            <artifactId>dcsdriverlevelservice</artifactId>
            <version>1.0.3</version>
        </dependency>
        <dependency>
            <groupId>com.ctrip.dcs.self</groupId>
            <artifactId>self-orderquery-service-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.dcs.dispatchorder</groupId>
            <artifactId>self-dispatchorder-service</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.dcs.driverorder</groupId>
            <artifactId>self-driver-order-domain-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.arch</groupId>
            <artifactId>distlock-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.dcs.self</groupId>
            <artifactId>self-transport-inventory-service-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.dcs.driver.domain</groupId>
            <artifactId>driver-domain-service</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.dcs.scm</groupId>
            <artifactId>scm-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.dcs.map.domain.service</groupId>
            <artifactId>dcs-map-domain-service-client</artifactId>
        </dependency>
    </dependencies>
</project>
