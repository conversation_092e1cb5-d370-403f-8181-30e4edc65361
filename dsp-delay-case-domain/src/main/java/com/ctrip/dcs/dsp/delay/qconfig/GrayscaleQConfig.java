package com.ctrip.dcs.dsp.delay.qconfig;

import com.ctrip.igt.framework.qconfig.QConfig2;
import com.google.common.base.Splitter;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 */
@Component
@Getter
@Setter
@QConfig2("grayscale.properties")
public class GrayscaleQConfig {

    /**
     * 灰度用户id
     * grayscaleUid=a,b,c
     */
    private String grayscaleUid;

    /**
     * 灰度城流量比例
     * grayscaleCityRate=1:100,2:500
     */
    private String grayscaleCityRate;

    /**
     * 灰度城流量比例
     * 城市:车系:比例
     * grayscaleCityRate=1:117:100,2:118:500
     */
    private String grayscaleCityCarTypeRate;

    public Boolean isGrayscaleUid(String uid) {
        if (StringUtils.isBlank(uid) || StringUtils.isBlank(grayscaleUid)) {
            return false;
        }
        List<String> list = Splitter.on(",").trimResults().omitEmptyStrings().splitToList(grayscaleUid);
        if (CollectionUtils.isEmpty(list)) {
            return false;
        }
        for (String item : list) {
            if (StringUtils.equalsIgnoreCase(item, uid) || StringUtils.containsIgnoreCase(item, "all")) {
                return true;
            }
        }
        return false;
    }

    public Boolean isGrayscaleCity(Integer cityId) {
        if (cityId == null || StringUtils.isBlank(grayscaleCityRate)) {
            return false;
        }
        List<String> list = Splitter.on(",").trimResults().omitEmptyStrings().splitToList(grayscaleCityRate);
        if (CollectionUtils.isEmpty(list)) {
            return false;
        }
        for (String item : list) {
            List<String> kv = Splitter.on(":").trimResults().omitEmptyStrings().splitToList(item);
            String city = kv.get(0);
            if (StringUtils.equalsIgnoreCase(city, "all") || StringUtils.equalsIgnoreCase(city, cityId.toString())) {
                return true;
            }
        }
        return false;
    }

    public Boolean isGrayscaleCityRateLimit(Integer cityId, String orderId) {
        if (cityId == null || StringUtils.isBlank(orderId) || StringUtils.isBlank(grayscaleCityRate)) {
            return false;
        }
        List<String> list = Splitter.on(",").trimResults().omitEmptyStrings().splitToList(grayscaleCityRate);
        if (CollectionUtils.isEmpty(list)) {
            return false;
        }
        for (String item : list) {
            List<String> kv = Splitter.on(":").trimResults().omitEmptyStrings().splitToList(item);
            String city = kv.get(0);
            if (StringUtils.equalsIgnoreCase(city, "all")) {
                return true;
            }
            if (StringUtils.equalsIgnoreCase(city, cityId.toString())) {
                return Math.abs(Long.valueOf(orderId.hashCode())) % 1000 < Integer.parseInt(kv.get(1));
            }
        }
        return false;
    }

    public Boolean isGrayscaleCityAndCarTypeId(Integer cityId, Integer carTypeId) {
        if (cityId == null || StringUtils.isBlank(grayscaleCityCarTypeRate)) {
            return false;
        }
        List<String> list = Splitter.on(",").trimResults().omitEmptyStrings().splitToList(grayscaleCityCarTypeRate);
        if (CollectionUtils.isEmpty(list)) {
            return false;
        }
        for (String item : list) {
            List<String> kv = Splitter.on(":").trimResults().omitEmptyStrings().splitToList(item);
            if (isGrayscaleCityAndCarTypeId(cityId, carTypeId, kv)) {
                return true;
            }
        }
        return false;
    }

    public Boolean isGrayscaleCityAndCarTypeIdRateLimit(Integer cityId, Integer carTypeId, String orderId) {
        if (cityId == null || StringUtils.isBlank(orderId) || StringUtils.isBlank(grayscaleCityCarTypeRate)) {
            return false;
        }
        List<String> list = Splitter.on(",").trimResults().omitEmptyStrings().splitToList(grayscaleCityCarTypeRate);
        if (CollectionUtils.isEmpty(list)) {
            return false;
        }
        for (String item : list) {
            List<String> kv = Splitter.on(":").trimResults().omitEmptyStrings().splitToList(item);
            if (isGrayscaleCityAndCarTypeId(cityId, carTypeId, kv)) {
                return Math.abs(Long.valueOf(orderId.hashCode())) % 1000 < Integer.parseInt(kv.get(2));
            }
        }
        return false;
    }

    public Boolean isGrayscaleCityAndCarTypeId(Integer cityId, Integer carTypeId, List<String> kv) {
        String city = kv.get(0);
        String carType = kv.get(1);
        if (!StringUtils.equalsIgnoreCase(city, "all") && !StringUtils.equalsIgnoreCase(city, cityId.toString())) {
            // 城市不再灰度范围内
            return false;
        }
        // 车型是否在灰度范围内
        return StringUtils.equalsIgnoreCase(carType, "all") || StringUtils.equalsIgnoreCase(carType, carTypeId.toString());
    }
}
