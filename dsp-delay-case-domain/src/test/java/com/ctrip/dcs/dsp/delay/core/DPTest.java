package com.ctrip.dcs.dsp.delay.core;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.modules.junit4.PowerMockRunner;

/**
 * <AUTHOR>
 */
@RunWith(PowerMockRunner.class)
public class DPTest {

    @Test
    public void test() {
        double[] p = new double[]{75.54, 96.24, 120, 132.57, 162, 160.06};
        int[][] c = new int[][]{
                {0, 0, 0, 0, 0, 0, 0},
                {0, 0, 1, 1, 0, 0, 1},
                {0, 1, 0, 1, 0, 1, 1},
                {0, 1, 1, 0, 1, 1, 1},
                {0, 0, 0, 1, 0, 1, 1},
                {0, 0, 1, 1, 1, 0, 1},
                {0, 1, 1, 1, 1, 1, 0},
        };
        DP dp = new DP(6, 200, new DP.OrderNetProfitFunction());
        for (int i = 0; i < p.length; i++) {
            dp.profit()[i] = p[i];
        }
        for (int i = 0; i < c.length; i++) {
            for (int j = 0; j < c[i].length; j++) {
                dp.conflict()[i][j] = c[i][j] == 1;
            }
        }
        int[][] result = dp.execute();
        Assert.assertEquals(result[0][0], 0);
        Assert.assertEquals(result[0][1], 1);
        Assert.assertEquals(result[0][2], 0);
        Assert.assertEquals(result[0][3], 1);
        Assert.assertEquals(result[0][4], 0);
        Assert.assertEquals(result[0][5], 0);
    }
}
