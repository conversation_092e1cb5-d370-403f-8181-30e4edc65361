package com.ctrip.dcs.dsp.delay.service.impl;

import com.ctrip.dcs.dsp.delay.conflict.CheckCode;
import com.ctrip.dcs.dsp.delay.context.DelayDspContext;
import com.ctrip.dcs.dsp.delay.context.DispatcherManager;
import com.ctrip.dcs.dsp.delay.core.DP;
import com.ctrip.dcs.dsp.delay.enums.DelayDspTaskType;
import com.ctrip.dcs.dsp.delay.factory.DPFactory;
import com.ctrip.dcs.dsp.delay.factory.ModelFactory;
import com.ctrip.dcs.dsp.delay.model.DispatchResult;
import com.ctrip.dcs.dsp.delay.model.DispatchResultDetail;
import com.ctrip.dcs.dsp.delay.enums.YesOrNo;
import com.ctrip.dcs.dsp.delay.gateway.GeoGateway;
import com.ctrip.dcs.dsp.delay.model.DelayDspOrder;
import com.ctrip.dcs.dsp.delay.model.DelayDspTask;
import com.ctrip.dcs.dsp.delay.model.Driver;
import com.ctrip.dcs.dsp.delay.model.DriverAggregation;
import com.ctrip.dcs.dsp.delay.model.Position;
import com.ctrip.dcs.dsp.delay.model.Route;
import com.ctrip.dcs.dsp.delay.service.ConflictService;
import com.ctrip.dcs.dsp.delay.service.DispatchTaskService;
import com.ctrip.dcs.dsp.delay.util.JsonUtil;
import com.ctrip.dcs.dsp.delay.util.MetricsUtil;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Collections;
import java.util.Comparator;
import java.util.Iterator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component("dpDispatchTaskServiceImpl")
public class DPDispatchTaskServiceImpl implements DispatchTaskService {

    private static final Logger logger = LoggerFactory.getLogger(DPDispatchTaskServiceImpl.class);

    @Autowired
    private ConflictService conflictService;

    @Autowired
    private GeoGateway geoGateway;

    @Autowired
    private DPFactory dpFactory;

    @PostConstruct
    public void register() {
        DispatcherManager.register(DelayDspTaskType.DP, this);
    }

    @Override
    public DispatchResult dispatch(DelayDspContext context) {
        logger.info("DispatchServiceImpl.dispatch", "context: {}", context.toString());
        List<DelayDspOrder> orders = context.getOrders();
        List<DriverAggregation> driverAggregations = context.getDriverAggregations();
        if (CollectionUtils.isEmpty(driverAggregations)) {
            logger.info("DispatchServiceImpl.dispatch", "driver is empty! task id: {}", context.getDelayDspTask().getTaskId());
            return new DispatchResult(orders);
        }
        List<DispatchResultDetail> details = Lists.newArrayList();
        Iterator<DriverAggregation> iterator = driverAggregations.iterator();
        while (iterator.hasNext()) {
            DriverAggregation driverAggregation = iterator.next();
            if (CollectionUtils.isEmpty(orders)) {
                break;
            }
            // 派发
            List<DelayDspOrder> result = dispatch(context, driverAggregation, mergeAndSort(driverAggregation.getOrders(), orders));
            // 构建派单结果
            List<DispatchResultDetail> list = ModelFactory.buildDispatchResultDetails(context, driverAggregation, result, geoGateway);
            details.addAll(list);
            // 剔除已匹配到司机的订单
            orders.removeAll(result);
        }
        if (CollectionUtils.isNotEmpty(orders)) {   // 仍有剩余订单
            List<DispatchResultDetail> list = orders.stream().map(DispatchResultDetail::new).collect(Collectors.toList());
            details.addAll(list);
        }
        DispatchResult result = new DispatchResult();
        result.setDetails(details);
        return result;
    }

    private List<DelayDspOrder> dispatch(DelayDspContext context, DriverAggregation driverAggregation, List<DelayDspOrder> orders) {
        long start = System.currentTimeMillis();
        List<DelayDspOrder> result = dispatch(driverAggregation, orders, context);
        long end = System.currentTimeMillis();
        MetricsUtil.recordTime("dispatch.dp.time", end - start);
        return result;
    }

    private List<DelayDspOrder> dispatch(DriverAggregation driverAggregation, List<DelayDspOrder> orders, DelayDspContext context) {
        DP dp = dpFactory.create(driverAggregation, orders, context);
        int[][] matrix = dp.execute();
        return select(context, driverAggregation, orders, matrix);
    }

    public List<DelayDspOrder> select(DelayDspContext context, DriverAggregation driverAggregation, List<DelayDspOrder> orders, int[][] matrix) {
        for (int i = 0; i < matrix.length; i++) {
            List<DelayDspOrder> list = Lists.newArrayList();
            boolean flag = false;
            for (int j = 0; j < matrix[i].length; j++) {
                DelayDspOrder order = orders.get(j);
                if (matrix[i][j] == YesOrNo.YES.getCode()) {
                    list.add(order);
                    continue;
                }
                if (order.hasTaken()) { // 司机已接订单不在派单组合中，则丢弃这个结果
                    flag = true;
                }
            }
            if (flag) {
                continue;
            }
            if (CollectionUtils.isNotEmpty(list)) {
                CheckCode checkCode = conflictService.checkConflict(context, driverAggregation, list);
                if (!checkCode.isConflict()) {
                    return list;
                }
            }
        }
        return Collections.emptyList();
    }

    private List<DelayDspOrder> mergeAndSort(List<DelayDspOrder> l1, List<DelayDspOrder> l2) {
        List<DelayDspOrder> list = Lists.newArrayList();
        list.addAll(l1);
        list.addAll(l2);
        list.sort(Comparator.comparing(DelayDspOrder::getPredictServiceStopTime));
        return list;
    }
}
