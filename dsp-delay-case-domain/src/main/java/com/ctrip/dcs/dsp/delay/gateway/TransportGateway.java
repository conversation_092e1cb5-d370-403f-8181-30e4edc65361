package com.ctrip.dcs.dsp.delay.gateway;

import com.ctrip.dcs.dsp.delay.model.SupplyOrder;
import com.ctrip.dcs.dsp.delay.model.TransportGroupInfo;
import com.ctrip.dcs.dsp.delay.model.TransportSku;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface TransportGateway {

    /**
     * 查询运力组关联的sku
     * @param cityId
     * @param carTypeId
     * @return
     */
    List<TransportSku> queryTransportSku(Integer cityId, Integer carTypeId);

    List<TransportGroupInfo> queryTransportGroups(SupplyOrder supplyOrder);

}
