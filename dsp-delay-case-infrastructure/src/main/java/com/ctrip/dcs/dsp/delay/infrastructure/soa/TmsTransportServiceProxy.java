package com.ctrip.dcs.dsp.delay.infrastructure.soa;

import com.ctrip.dcs.tms.transport.api.TmsTransportServiceClient;
import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.igt.framework.soa.client.ServiceClient;

/**
 * <AUTHOR>
 */
@ServiceClient(TmsTransportServiceClient.class)
public interface TmsTransportServiceProxy {

    QueryApplyTransGroupsSkuForDspResponseType queryApplyTransGroupsSkuForDsp(QueryApplyTransGroupsSkuForDspRequestType request);

    DriverInfoSOAResponseType queryDriver(DriverInfoSOARequestType request);

    QueryDrvLeaveDetailForDspSOAResponseType queryDrvLeaveDetailForDsp(QueryDrvLeaveDetailForDspSOARequestType request);

    QueryDrvIdByTransportGroupsResponseType queryDrvIdByTransportGroups(QueryDrvIdByTransportGroupsRequestType request);

    QueryTransportGroupsSOAResponseType queryTransportGroups(QueryTransportGroupsSOARequestType request);


}
