<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ctrip.dcs.dsp.delay.infrastructure.mapper.DelayDspMatchTaskRecordMapper">

    <resultMap id="BaseResultMap" type="com.ctrip.dcs.dsp.delay.model.DelayDspMatchTaskRecord">
        <result column="id" property="id" />
        <result column="task_id" property="taskId" />
        <result column="task_version" property="taskVersion" />
        <result column="order_id" property="orderId" />
        <result column="user_order_id" property="userOrderId" />
        <result column="driver_id" property="driverId" />
        <result column="is_out" property="isOut" />
        <result column="taken_code" property="takenCode" />
        <result column="out_reason" property="outReason" />
        <result column="out_time" property="outTime" />
        <result column="is_delay" property="isDelay" />
        <result column="order_source" property="orderSource" />
        <result column="match_success" property="matchSuccess" />
        <result column="order_seq" property="orderSeq" />
        <result column="execute_time" property="executeTime" />
        <result column="driver_order_id" property="driverOrderId" />

    </resultMap>


    <sql id="Base_Column_List">
        id,
        task_id,
        task_version,
        order_id,
        user_order_id,
        driver_id,
        is_out,
        taken_code,
        out_reason,
        out_time,
        is_Delay,
        order_source,
        match_success,
        order_seq,
        execute_time,
        driver_order_id
    </sql>

    <insert id="insert">
        INSERT INTO delay_dsp_match_task_record (
        task_id,
        task_version,
        order_id,
        user_order_id,
        driver_id,
        is_out,
        taken_code,
        out_reason,
        out_time,
        is_Delay,
        order_source,
        match_success,
        order_seq,
        execute_time,
        driver_order_id,
        datachange_createtime,
        datachange_lasttime
        )
        VALUES
        <foreach collection="items" item="item" separator=",">
            (
                #{item.taskId},
                #{item.taskVersion},
                #{item.orderId},
                #{item.userOrderId},
                #{item.driverId},
                #{item.isOut},
                #{item.takenCode},
                #{item.outReason},
                #{item.outTime},
                #{item.isDelay},
                #{item.orderSource},
                #{item.matchSuccess},
                #{item.orderSeq},
                #{item.executeTime},
                #{item.driverOrderId},
                NOW(),
                NOW()
            )
        </foreach>
    </insert>

    <update id="update" parameterType="com.ctrip.dcs.dsp.delay.model.DelayDspMatchTaskRecord">
        UPDATE delay_dsp_match_task_record
        <set>
            <if test="takenCode != null">
                taken_code = #{takenCode},
            </if>
            <if test="outReason != null">
                out_reason = #{outReason},
            </if>
            <if test="outTime != null">
                out_time = #{outTime},
            </if>
            <if test="driverOrderId != null">
                driver_order_id = #{driverOrderId},
            </if>
            datachange_lasttime = NOW()
        </set>
        WHERE
        id = #{id}
    </update>

    <select id="query" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM delay_dsp_match_task_record
        WHERE
        task_id = #{taskId} and task_version = #{taskVersion}
    </select>

</mapper>