package com.ctrip.dcs.dsp.delay.infrastructure.soa;

import com.ctrip.dcs.driver.domain.account.QueryUDLByDriverIdRequestType;
import com.ctrip.dcs.driver.domain.account.QueryUDLByDriverIdResponseType;
import com.ctrip.dcs.driver.domain.interfaces.api.DscDriverDomainServiceClient;
import com.ctrip.igt.framework.soa.client.ServiceClient;

/**
 * <AUTHOR>
 * @since 2025/4/8 14:30
 */
@ServiceClient(value = DscDriverDomainServiceClient.class,format = "json" )
public interface DriverDomainServiceProxy {
    
    QueryUDLByDriverIdResponseType queryUDLByDriverId(QueryUDLByDriverIdRequestType request);
}
