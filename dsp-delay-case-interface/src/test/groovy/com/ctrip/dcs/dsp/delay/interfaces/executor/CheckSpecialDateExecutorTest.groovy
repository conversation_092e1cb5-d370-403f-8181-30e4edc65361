package com.ctrip.dcs.dsp.delay.interfaces.executor

import com.ctrip.dcs.dsp.application.service.DelayDspOrderApplicationService
import com.ctrip.dcs.dsp.delay.service.interfaces.message.CheckSpecialDateRequestType
import com.ctrip.dcs.dsp.delay.service.interfaces.message.CheckSpecialDateResponseType
import com.ctrip.igt.framework.common.clogging.Logger
import com.ctrip.igt.framework.common.exception.BizException
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator
import org.junit.Assert
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 * @since 2024/8/20 10:30
 */
class CheckSpecialDateExecutorTest extends Specification {
    def testObj = new CheckSpecialDateExecutor()
    def logger = Mock(Logger)
    def delayDspApplicationService = Mock(DelayDspOrderApplicationService)

    def setup() {
        testObj.delayDspApplicationService = delayDspApplicationService
        testObj.logger = logger
    }

    @Unroll
    def "test execute"() {
        given:
        delayDspApplicationService.checkSpecialDate(_, _, _) >> checkSpecialDateResult

        when:
        def result = testObj.execute(requestType)

        then:
        Objects.equals(result.isHit(), expectedResult);

        where:
        requestType                                                                                      | checkSpecialDateResult || expectedResult
        new CheckSpecialDateRequestType(cityId: 0, carTypeId: 0, sysExpectBookTime: "sysExpectBookTime") | Boolean.TRUE           || Boolean.TRUE
        new CheckSpecialDateRequestType(cityId: 0, carTypeId: 0, sysExpectBookTime: "sysExpectBookTime") | Boolean.FALSE           || Boolean.FALSE
    }

    def "test execute failed"() {
        given:
        delayDspApplicationService.checkSpecialDate(_, _, _) >> {throw new RuntimeException("")}
        CheckSpecialDateRequestType requestType = new CheckSpecialDateRequestType();

        when:
        def result = testObj.execute(requestType)

        then:
        Assert.assertTrue(result.isHit());
    }



    @Unroll
    def "test validate is failed"() {
        given:
        CheckSpecialDateRequestType req = new CheckSpecialDateRequestType();
        req.setCityId(cityId);
        req.setCarTypeId(carTypeId);
        req.setSysExpectBookTime(sysExpectBookTime);

        when:
        def result = testObj.validate(Mock(AbstractValidator), req);

        then:
        def ex = thrown(BizException);
        Assert.assertTrue(ex != null)

        where:
        cityId | carTypeId | sysExpectBookTime || expectedResult
        null   | null      | null              || null
        11     | null      | null              || null
        11     | 123       | null              || null
    }

    def "test validate success"() {
        given:
        CheckSpecialDateRequestType req = new CheckSpecialDateRequestType();
        req.setCityId(11);
        req.setCarTypeId(123);
        req.setSysExpectBookTime("2024-04-11 12:11:11");

        when:
        def result = testObj.validate(Mock(AbstractValidator), req);

        then:
        Assert.assertTrue(Objects.nonNull(req))

    }
}
