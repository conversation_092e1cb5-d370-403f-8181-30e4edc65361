package com.ctrip.dcs.dsp.delay.service;

import com.ctrip.dcs.dsp.delay.carconfig.CarConfig;
import com.ctrip.dcs.dsp.delay.carconfig.CarConfigCode;
import com.ctrip.dcs.dsp.delay.carconfig.value.DelayDspSubSkuValue;
import com.ctrip.dcs.dsp.delay.gateway.CarConfigGateway;
import com.ctrip.dcs.dsp.delay.model.SupplyOrder;
import com.ctrip.dcs.dsp.delay.service.impl.DispatchConfigServiceImpl;
import com.ctrip.dcs.dsp.delay.util.DateUtil;
import com.google.common.collect.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.modules.junit4.PowerMockRunner;

/**
 * <AUTHOR>
 */
@RunWith(PowerMockRunner.class)
public class DispatchConfigServiceTest {

    @InjectMocks
    private DispatchConfigServiceImpl dispatchConfigService;

    @Mock
    private CarConfig carConfig;

    @Mock
    private CarConfigGateway carConfigGateway;

    @Test
    public void testMatchDelayDspSubSkuConfig() {
        SupplyOrder order = new SupplyOrder();
        order.setCarTypeId(117);
        order.setCityCode("beijing_city");
        order.setSysExpectBookTime(DateUtil.parseDate("2022-04-28 12:00:00"));

        DelayDspSubSkuValue config = new DelayDspSubSkuValue();
        config.setInSubSkuId(8888);
        config.setOutSubSkuIds("8888,8889");

        PowerMockito.when(carConfigGateway.query(CarConfigCode.DELAY_DSP_SUB_SKU.getCode(), CarConfigCode.DELAY_DSP_SUB_SKU.getVersion())).thenReturn(carConfig);
        PowerMockito.when(carConfig.values(Mockito.any(), Mockito.any())).thenReturn(Lists.newArrayList(config));
        DelayDspSubSkuValue value = dispatchConfigService.matchDelayDspSubSkuConfig(order);
        Assert.assertNotNull(value);
    }
}
