package com.ctrip.dcs.dsp.delay.infrastructure.mapper.ck

import com.ctrip.dcs.dsp.delay.model.InsertPoolReasonRecord
import org.junit.Assert
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 * @since 2024/8/20 09:21
 */
class ClickHouseAdapterTest extends Specification {
    def testObj = new ClickHouseAdapter()

    @Unroll
    def "test save"() {
        given:
        InsertPoolReasonRecord record = buildInsertPoolReasonRecord();

        when:
        testObj.save("dcs-dsp-delay-insert-pool-reason-record", record.toMap())

        then:
        Assert.assertTrue(Objects.nonNull(record))
    }

    def InsertPoolReasonRecord buildInsertPoolReasonRecord() {
        InsertPoolReasonRecord record = new InsertPoolReasonRecord();
        record.setUserOrderId("userOrderId");
        record.setDspOrderId("dspOrderId");
        record.setReasonCode("OK");
        record.setStartTime("1");
        record.setEndTime("2");
        return record;
    }
}
