package com.ctrip.dcs.dsp.delay.infrastructure.converter;
import java.math.BigDecimal;
import com.ctrip.dcs.oms.driver.order.domain.api.drvOrder.CreateDriverOrderRequestType;
import com.ctrip.dcs.self.order.query.dto.*;
import com.ctrip.dcs.tms.transport.api.model.DriverInfo;
import com.google.common.collect.Lists;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.powermock.modules.junit4.PowerMockRunner;

/**
 * <AUTHOR>
 */
@RunWith(PowerMockRunner.class)
public class DriverOrderConverterTest {

    @InjectMocks
    private DriverOrderConverter converter;

    @Test
    public void test() {
        BaseDetail baseDetail = new BaseDetail();
        baseDetail.setUserOrderId("");
        baseDetail.setSupplyOrderId("");
        baseDetail.setDspOrderId("");
        baseDetail.setDriverOrderId("");
        baseDetail.setConfirmRecordId(0L);
        baseDetail.setOrderStatus(0);
        baseDetail.setOldOrderStatus(0);
        baseDetail.setOldOrderStatusDetail(0);
        baseDetail.setProductType(0);
        baseDetail.setProductCode("");
        baseDetail.setProductName("");
        baseDetail.setCategoryCode("");
        baseDetail.setServiceType(0);
        baseDetail.setCityId(0);
        baseDetail.setFromCityId(0);
        baseDetail.setToCityId(0);
        baseDetail.setVehicleGroupId(0);
        baseDetail.setEstimatedUseTime("");
        baseDetail.setEstimatedUseTimeBj("");
        baseDetail.setPredicServiceStopTimeBj("");
        baseDetail.setLastConfirmTime("");
        baseDetail.setLastConfirmTimeBj("");
        baseDetail.setLastConfirmCarTime("");
        baseDetail.setLastConfirmCarTimeBj("");
        baseDetail.setConnectMode(0);
        baseDetail.setSalesMode(0);
        baseDetail.setUseDays(new BigDecimal("0"));
        baseDetail.setEstimatedKm(new BigDecimal("0"));
        baseDetail.setEstimatedMin(new BigDecimal("0"));
        baseDetail.setActualKm(new BigDecimal("0"));
        baseDetail.setActualMin(new BigDecimal("0"));
        baseDetail.setPriceMark("");
        baseDetail.setServiceProviderId(0);
        baseDetail.setSupplierId(0);
        baseDetail.setCountryId(0L);
        baseDetail.setLocale("");
        baseDetail.setSkuId(0);
        baseDetail.setCancelRule("");
        baseDetail.setWaitingRule("");
        baseDetail.setPriceResultCode("");
        baseDetail.setDetailSnapShotid("");
        baseDetail.setXproductInfo("");
        baseDetail.setSpContractInfo("");
        baseDetail.setDspStrategyStr("");
        baseDetail.setOrderCarTypeId(0L);
        baseDetail.setDrvId(0L);
        baseDetail.setDrvName("");
        baseDetail.setDrvPhone("");
        baseDetail.setDrvPhoneAreaCode("");
        baseDetail.setDrvCoopMode(0);
        baseDetail.setDrvLanguageCodeList("");
        baseDetail.setCarId(0L);
        baseDetail.setCarTypeId(0L);
        baseDetail.setCarLicense("");
        baseDetail.setCarColorId(0L);
        baseDetail.setCarBrandId(0L);
        baseDetail.setCarSeriesId(0L);
        baseDetail.setTransportGroupId(0L);
        baseDetail.setTransportGroupMode(0);
        baseDetail.setTransportGroupName("");
        baseDetail.setEstimatedUseTime("");
        baseDetail.setEstimatedUseTimeBj("");
        baseDetail.setPredicServiceStopTimeBj("");
        baseDetail.setConfirmMoneyTimeLocal("");
        baseDetail.setConfirmMoneyTimeBj("");
        baseDetail.setFinishTimeBj("");
        baseDetail.setCancelTimeLocal("");
        baseDetail.setCancelTimeBj("");
        baseDetail.setBizAreaType(0);
        baseDetail.setAdultCount(0);
        baseDetail.setChildCount(0);
        baseDetail.setBagCount(0);
        baseDetail.setMaxBagCount(0);
        baseDetail.setCoordType("");
        baseDetail.setFromLongitude(0.0D);
        baseDetail.setFromLatitude(0.0D);
        baseDetail.setToLongitude(0.0D);
        baseDetail.setToLatitude(0.0D);
        baseDetail.setTakenTimeBj("");
        baseDetail.setTargetId("");
        baseDetail.setTerminalId("");
        baseDetail.setFixedLocationType(0);
        baseDetail.setFixedPosition("");
        baseDetail.setPremiumOrderFlag(0);
        baseDetail.setDspOrderUpdateTime("");
        baseDetail.setDspOrderCreateTime("");
        baseDetail.setDrvOrderUpdateTime("");
        baseDetail.setDrvOrderCreateTime("");
        baseDetail.setNewPayProcess(0);
        baseDetail.setUid("");
        baseDetail.setDistributionChannelId(0);
        baseDetail.setGiveFreeTime(0);

        OrderWayPointInfoDTO orderWayPointInfoDTO = new OrderWayPointInfoDTO();
        orderWayPointInfoDTO.setUserOrderId("1");
        orderWayPointInfoDTO.setDspOrderId("");
        orderWayPointInfoDTO.setDriverOrderId("");
        orderWayPointInfoDTO.setPointIndex(0);
        orderWayPointInfoDTO.setPointName("");
        orderWayPointInfoDTO.setPointAddress("");
        orderWayPointInfoDTO.setPointLongitude(0.0D);
        orderWayPointInfoDTO.setPointLatitude(0.0D);
        orderWayPointInfoDTO.setPointCoordSys("");
        orderWayPointInfoDTO.setCarPlaceId("");
        orderWayPointInfoDTO.setWaitTimeLength(0);
        orderWayPointInfoDTO.setWaitUnitPrice(new BigDecimal("0"));
        orderWayPointInfoDTO.setPriceCurrency("");
        orderWayPointInfoDTO.setFirstInTime("");
        orderWayPointInfoDTO.setFirstOutTime("");
        orderWayPointInfoDTO.setLastOutTime("");
        orderWayPointInfoDTO.setDriverOperateTime("");
        orderWayPointInfoDTO.setDriverOperateLongitude(0.0D);
        orderWayPointInfoDTO.setDriverOperateLatitude(0.0D);
        orderWayPointInfoDTO.setDriverCoordSys("");
        orderWayPointInfoDTO.setPointStandard(0);
        orderWayPointInfoDTO.setPointReason("");


        OrderDetail orderDetail = new OrderDetail();
        orderDetail.setBaseDetail(baseDetail);
        orderDetail.setEstimatedCostInfo(new EstimatedCostInfo());
        orderDetail.setActualCostInfo(new ActualCostInfo());
        orderDetail.setSopRecords(Lists.newArrayList());
        orderDetail.setOrderStandardRecord(new OrderStandardRecord());
        orderDetail.setOrderPackageServices(Lists.newArrayList());
        orderDetail.setCancelRecord(new CancelRecord());
        orderDetail.setBreakAppointmentRecord(new BreakAppointmentRecord());
        orderDetail.setOrderConfirmRecord(new OrderConfirmRecord());
        orderDetail.setWayPointInfoList(Lists.newArrayList(orderWayPointInfoDTO));
//        orderDetail.setPlatformPriceStrategy(new PlatformPriceStrategy());

        DriverInfo driverInfo = new DriverInfo();
        driverInfo.setDriverId(0L);
        driverInfo.setStatus(0);
        driverInfo.setBroadcast(0);
        driverInfo.setIsSendWorkPeriod(0);
        driverInfo.setCompatibleCoopMode(0);
        driverInfo.setCoopMode(0);
        driverInfo.setDriverName("");
        driverInfo.setDriverPhone("");
        driverInfo.setPhoneAreaCode("");
        driverInfo.setCarId(0L);
        driverInfo.setCarLicense("");
        driverInfo.setCarBrandId(0L);
        driverInfo.setCarBrandName("");
        driverInfo.setCarTypeId(0);
        driverInfo.setCarTypeName("");
        driverInfo.setCarColorId(0L);
        driverInfo.setCarColor("");
        driverInfo.setIntendVehicleTypeId("");
        driverInfo.setCityId(0L);
        driverInfo.setCityName("");
        driverInfo.setAddressLongitude(0.0D);
        driverInfo.setAddressLatitude(0.0D);
        driverInfo.setIsEnergy(0);
        driverInfo.setWorkTimes(Lists.newArrayList());
        driverInfo.setDriverLanguage("");
        driverInfo.setCountryId(0L);
        driverInfo.setCountryName("");
        driverInfo.setSupplierId(0L);
        driverInfo.setEmail("");
        driverInfo.setWechat("");
        driverInfo.setCreateTime("");
        driverInfo.setTransportGroups(Lists.newArrayList());
        driverInfo.setInternalScope(0);
        driverInfo.setQunarCityCode("");
        driverInfo.setPicUrl("");
        driverInfo.setCarSeriesId(0L);
        driverInfo.setCarSeriesName("");
        driverInfo.setMaxLuggages(0);
        driverInfo.setMaxPassengers(0);
        driverInfo.setSupplierName("");
        driverInfo.setDrvProductionLineCodeList(Lists.newArrayList());
        driverInfo.setVehProductionLineCodeList(Lists.newArrayList());
        driverInfo.setVehicleStatus(0);
        driverInfo.setDrvIdcard("");
        driverInfo.setCertiDate("");
        driverInfo.setExpiryBeginDate("");
        driverInfo.setExpiryEndDate("");
        driverInfo.setDrvLicenseNumber("");
        driverInfo.setVin("");
        driverInfo.setRideHailingVehCertValid(false);
        driverInfo.setRideHailingDrvCertValid(false);
        driverInfo.setDrvConnectAddress("");
        driverInfo.setOnlineTime("");
        driverInfo.setVehRegstDate("");
        driverInfo.setVehCreateTime("");
        driverInfo.setVehBindTime("");
        driverInfo.setDrvcardImg("");
        driverInfo.setVehicleFullImg("");
        driverInfo.setNation("");
        driverInfo.setRaisingPickUp(false);
        driverInfo.setChildSeat(false);
        driverInfo.setRealPicUrl("");
        driverInfo.setDriverNetCertNo("");
        driverInfo.setVehicleNetCertNo("");
        driverInfo.setDispatchSupplierIdList(Lists.newArrayList());


        CreateDriverOrderRequestType requestType = converter.toCreateDriverOrderRequestType(orderDetail, driverInfo);
        Assert.assertNotNull(requestType);
        Assert.assertEquals(requestType.getDriverOrderWayPointList().get(0).getUserOrderId(), "1");
    }
}
