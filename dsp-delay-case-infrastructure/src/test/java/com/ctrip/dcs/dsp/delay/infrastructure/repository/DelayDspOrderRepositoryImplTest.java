package com.ctrip.dcs.dsp.delay.infrastructure.repository;

import com.ctrip.dcs.dsp.delay.infrastructure.mapper.DelayDspOrderMapper;
import com.ctrip.dcs.dsp.delay.model.DelayDspOrder;
import com.google.common.collect.Lists;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;
import java.util.Calendar;
import java.util.GregorianCalendar;
import java.util.List;

import static org.mockito.Mockito.*;

/**
 * <AUTHOR>
 */
public class DelayDspOrderRepositoryImplTest {
    @Mock
    DelayDspOrderMapper delayDspOrderMapper;
    @InjectMocks
    DelayDspOrderRepositoryImpl delayDspOrderRepositoryImpl;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testQueryByOrderIds() throws Exception {
        List<String> ids = Lists.newArrayList();
        for (int i = 0; i < 300; i++) {
            ids.add(Integer.valueOf(i).toString());
        }
        when(delayDspOrderMapper.queryByOrderIds(any())).thenReturn(Lists.newArrayList(new DelayDspOrder()));
        List<DelayDspOrder> result = delayDspOrderRepositoryImpl.queryByOrderIds(ids);
        Assert.assertEquals(result.size(), 2);
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme