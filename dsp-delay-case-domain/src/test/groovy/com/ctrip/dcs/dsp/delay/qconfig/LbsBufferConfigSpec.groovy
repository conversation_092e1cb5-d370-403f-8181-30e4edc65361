package com.ctrip.dcs.dsp.delay.qconfig

import com.ctrip.dcs.dsp.delay.qconfig.key.LbsBufferKeyVO
import com.ctrip.dcs.dsp.delay.qconfig.key.OrderMileageConfigKeyVO
import com.ctrip.dcs.dsp.delay.qconfig.value.LbsBufferValueVO
import com.ctrip.dcs.dsp.delay.qconfig.value.OrderMileageConfigValueVO
import com.google.common.collect.Lists
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 */
class LbsBufferConfigSpec extends Specification{

    def map = new HashMap();

    def config = new LbsBufferConfig (context: map)

    @Unroll
    def "test"() {
        given: "Mock数据"
        LbsBufferKeyVO keyVO = new LbsBufferKeyVO(cityId)
        LbsBufferValueVO valueVO = new LbsBufferValueVO("", "", "", value)
        map.put(keyVO.toKey(), new BaseCarConfig.Config(keyVO, Lists.newArrayList(valueVO)))

        when: "执行校验方法"
        def result = config.getValue(1)

        then: "验证校验结果"
        result.getValue() == value

        where:
        cityId || value
        1      || 10
        2      || 0
        0      || 10
    }
}
