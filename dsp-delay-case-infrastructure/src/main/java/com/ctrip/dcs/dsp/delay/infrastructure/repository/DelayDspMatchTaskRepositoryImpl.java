package com.ctrip.dcs.dsp.delay.infrastructure.repository;

import com.ctrip.dcs.dsp.delay.infrastructure.geteway.ChannelGatewayImpl;
import com.ctrip.dcs.dsp.delay.infrastructure.mapper.DelayDspMatchTaskRecordMapper;
import com.ctrip.dcs.dsp.delay.model.DelayDspMatchTaskRecord;
import com.ctrip.dcs.dsp.delay.repository.DelayDspMatchTaskRepository;
import com.ctrip.dcs.dsp.delay.util.JsonUtil;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import org.apache.tools.ant.taskdefs.Jar;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 */
@Component
public class DelayDspMatchTaskRepositoryImpl implements DelayDspMatchTaskRepository {

    private static final Logger logger = LoggerFactory.getLogger(ChannelGatewayImpl.class);


    @Autowired
    private DelayDspMatchTaskRecordMapper delayDspMatchTaskRecordMapper;


    @Override
    public void save(List<DelayDspMatchTaskRecord> records) {
        delayDspMatchTaskRecordMapper.insert(records);
    }

    @Override
    public void update(DelayDspMatchTaskRecord record) {
        if(record == null || record.getId() == null){
            logger.error("DelayDspMatchTaskRepositoryImpl_update", JsonUtil.toJson(record));
            return;
        }
        delayDspMatchTaskRecordMapper.update(record);
    }

    @Override
    public List<DelayDspMatchTaskRecord> query(Long taskId, Integer taskVersion) {
        return delayDspMatchTaskRecordMapper.query(taskId, taskVersion);
    }
}
