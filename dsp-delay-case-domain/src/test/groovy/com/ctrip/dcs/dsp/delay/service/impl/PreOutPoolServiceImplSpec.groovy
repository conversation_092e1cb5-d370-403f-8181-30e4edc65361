package com.ctrip.dcs.dsp.delay.service.impl


import com.ctrip.dcs.dsp.delay.model.DelayDspOrder
import com.ctrip.dcs.dsp.delay.model.DelayDspTask
import com.ctrip.dcs.dsp.delay.model.PreOutPoolDTO
import com.ctrip.dcs.dsp.delay.mq.MessageProducer
import com.ctrip.dcs.dsp.delay.qconfig.DelayDspCommonQConfig
import com.ctrip.dcs.dsp.delay.repository.DelayDspOrderRepository
import com.ctrip.dcs.dsp.delay.repository.DelayDspTaskRepository
import com.ctrip.dcs.dsp.delay.service.RedispatchOrderService
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 */
class PreOutPoolServiceImplSpec extends Specification {

    def delayDspTaskRepository = Mock(DelayDspTaskRepository)

    def delayDspOrderRepository = Mock(DelayDspOrderRepository)

    def delayDspCommonQConfig = Mock(DelayDspCommonQConfig)

    def messageProducer = Mock(MessageProducer)

    def redispatchOrderService = Mock(RedispatchOrderService)


    def preOutPoolService = new PreOutPoolServiceImpl(
            delayDspTaskRepository: delayDspTaskRepository,
            delayDspOrderRepository: delayDspOrderRepository,
            delayDspCommonQConfig: delayDspCommonQConfig,
            messageProducer: messageProducer,
            redispatchOrderService: redispatchOrderService
    )

    @Unroll
    def "query and create task"() {
        given: "Mock数据"
        delayDspOrderRepository.queryDelayOrderByMainOrderId(_) >> delayDspOrder
        delayDspTaskRepository.queryByTaskId(_) >> delayDspTask
        delayDspCommonQConfig.getPreOutTimeDelayCheck() >> 0
        PreOutPoolDTO request = new PreOutPoolDTO()
        request.setUserOrderId("1")
        request.setCustomerServiceUid(null)
        request.setPressedDispatchSource(1)
        when: "执行校验方法"
        def res = preOutPoolService.preOutPool(request)

        then: "验证校验结果"
        res == result

        where:
        delayDspOrder                           | delayDspTask                    | config || result
        null                                    | null                            | null   || 1
        new DelayDspOrder()                     | null                            | null   || 1
        new DelayDspOrder()                     | new DelayDspTask(taskStatus: 1) | null   || 1
        new DelayDspOrder()                     | new DelayDspTask(taskStatus: 2) | null   || 1
        new DelayDspOrder()                     | new DelayDspTask(taskStatus: 3) | null   || 1
        new DelayDspOrder()                     | new DelayDspTask(taskStatus: 4) | null   || 1
        new DelayDspOrder(orderSource: "QUNAR") | new DelayDspTask(taskStatus: 0) | null   || 1
        new DelayDspOrder(orderSource: "CTRIP") | new DelayDspTask(taskStatus: 0) | null   || 1
    }

    def "test sendMessageToWorkBenchLog"() {
        given:

        when:
        preOutPoolService.sendMessageToWorkBenchLog("1", "param")

        then:
        1 * messageProducer.sendMessage(_,_)
    }
}
