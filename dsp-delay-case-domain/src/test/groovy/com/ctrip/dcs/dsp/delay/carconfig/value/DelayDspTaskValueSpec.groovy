package com.ctrip.dcs.dsp.delay.carconfig.value

import com.ctrip.dcs.dsp.delay.util.DateUtil
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 */
class DelayDspTaskValueSpec extends Specification{

    def value = new DelayDspTaskValue()

    @Unroll
    def "match"() {

        given: "Mock数据"
        value.setBegin(startTime)
        value.setEnd(endTime)

        when: "执行校验方法"
        def res = value.match(date)

        then: "验证校验结果"
        res == ok

        where:
        date                                    | startTime | endTime || ok
        getDate("2022-05-07 12:00:00") | "08:00"   | "20:00" || true
        getDate("2022-05-07 07:00:00") | "08:00"   | "20:00" || false
        getDate("2022-05-07 21:00:00") | "08:00"   | "20:00" || false
        getDate("2022-05-07 21:00:00") | "20:00"   | "08:00" || true
        getDate("2022-05-07 19:00:00") | "20:00"   | "08:00" || false
        getDate("2022-05-07 01:00:00") | "20:00"   | "08:00" || true
        getDate("2022-05-07 09:00:00") | "20:00"   | "08:00" || false
    }

    @Unroll
    def "begin and end"() {

        given: "Mock数据"
        value.setBegin(startTime)
        value.setEnd(endTime)

        when: "执行校验方法"
        def begin = value.getBeginTime(date)
        def end = value.getEndTime(date)

        then: "验证校验结果"
        begin == resultBegin
        end == resultEnd

        where:
        date                           | startTime | endTime || resultBegin                    | resultEnd
        getDate("2022-05-07 12:00:00") | "06:00"   | "17:59" || getDate("2022-05-07 06:00:00") | getDate("2022-05-07 17:59:59")
        getDate("2022-05-07 20:00:00") | "18:00"   | "05:59" || getDate("2022-05-07 18:00:00") | getDate("2022-05-08 05:59:59")
        getDate("2022-05-07 01:00:00") | "18:00"   | "05:59" || getDate("2022-05-06 18:00:00") | getDate("2022-05-07 05:59:59")
        getDate("2022-05-07 12:00:00") | "05:00"   | "16:59" || getDate("2022-05-07 05:00:00") | getDate("2022-05-07 16:59:59")
        getDate("2022-05-07 20:00:00") | "17:00"   | "04:59" || getDate("2022-05-07 17:00:00") | getDate("2022-05-08 04:59:59")
        getDate("2022-05-07 01:00:00") | "17:00"   | "04:59" || getDate("2022-05-06 17:00:00") | getDate("2022-05-07 04:59:59")
        getDate("2022-05-07 12:00:00") | "04:00"   | "15:59" || getDate("2022-05-07 04:00:00") | getDate("2022-05-07 15:59:59")
        getDate("2022-05-07 20:00:00") | "16:00"   | "03:59" || getDate("2022-05-07 16:00:00") | getDate("2022-05-08 03:59:59")
        getDate("2022-05-07 01:00:00") | "16:00"   | "03:59" || getDate("2022-05-06 16:00:00") | getDate("2022-05-07 03:59:59")

    }

    def getDate(String datetime) {
        return DateUtil.parseDate(datetime)
    }
}
