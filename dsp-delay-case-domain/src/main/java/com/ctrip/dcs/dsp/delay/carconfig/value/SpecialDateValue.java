package com.ctrip.dcs.dsp.delay.carconfig.value;

import com.ctrip.dcs.dsp.delay.carconfig.Value;
import com.ctrip.dcs.dsp.delay.enums.ConfigExecuteCountType;
import com.ctrip.dcs.dsp.delay.util.DateUtil;

import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024/8/12 11:44
 */
public class SpecialDateValue implements Value<Date> {
    
    private Integer cityId;
    
    private Integer carTypeId;
    
    private String startTime;
    
    private String endTime;
    
    private String startDate;
    
    private String endDate;
    
    private String type;
    
    public Integer getCityId() {
        return cityId;
    }
    
    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }
    
    public Integer getCarTypeId() {
        return carTypeId;
    }
    
    public void setCarTypeId(Integer carTypeId) {
        this.carTypeId = carTypeId;
    }
    
    public String getStartTime() {
        return startTime;
    }
    
    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }
    
    public String getEndTime() {
        return endTime;
    }
    
    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }
    
    public String getStartDate() {
        return startDate;
    }
    
    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }
    
    public String getEndDate() {
        return endDate;
    }
    
    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }
    
    public String getType() {
        return type;
    }
    
    public void setType(String type) {
        this.type = type;
    }
    
    @Override
    public boolean match(Date date) {
        //按每年
        if (Objects.equals(ConfigExecuteCountType.YEARS.getType(), this.type)) {
            return matchYearsType(date);
        }
        //按一次
        return matchOnceType(date);
    }
    
    private boolean matchYearsType(Date date) {
        //先按照天匹配（忽略年）
        String curDay = DateUtil.formatDate(date, DateUtil.MONTH_TO_DAY_FMT);
        String configStartDay = DateUtil.formatDate(DateUtil.parseDate(this.startDate, DateUtil.DAY_FMT), DateUtil.MONTH_TO_DAY_FMT);
        String configEndDay = DateUtil.formatDate(DateUtil.parseDate(this.endDate, DateUtil.DAY_FMT), DateUtil.MONTH_TO_DAY_FMT);
        if (curDay.compareTo(configStartDay) < 0 || curDay.compareTo(configEndDay) > 0) {
            return false;
        }
        //再按照时分匹配
        String curHourMin = DateUtil.formatDate(date, DateUtil.HOUR_MIN_SEC_FMT);
        if (curHourMin.compareTo(this.startTime) <0 || curHourMin.compareTo(this.endTime) > 0) {
            return false;
        }
        return true;
    }
    
    private boolean matchOnceType(Date date) {
        //先按照天匹配（不忽略年）
        String curDay = DateUtil.formatDate(date, DateUtil.DAY_FMT);
        if (curDay.compareTo(this.startDate) < 0 || curDay.compareTo(this.endDate) > 0) {
            return false;
        }
        //再按照时分匹配
        String curHourMin = DateUtil.formatDate(date, DateUtil.HOUR_MIN_SEC_FMT);
        if (curHourMin.compareTo(this.startTime) <0 || curHourMin.compareTo(this.endTime) > 0) {
            return false;
        }
        return true;
    }
}
