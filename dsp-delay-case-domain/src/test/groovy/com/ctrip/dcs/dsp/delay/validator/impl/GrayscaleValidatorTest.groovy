package com.ctrip.dcs.dsp.delay.validator.impl


import com.ctrip.dcs.dsp.delay.model.SupplyOrder
import com.ctrip.dcs.dsp.delay.qconfig.GrayscaleQConfig
import com.ctrip.dcs.dsp.delay.validator.ValidatorCode
import com.ctrip.dcs.dsp.delay.validator.ValidatorDTO
import spock.lang.Specification

/**
 * <AUTHOR>
 */
class GrayscaleValidatorTest extends Specification {

    GrayscaleQConfig grayscaleQConfig = Mock(GrayscaleQConfig)

    GrayscaleValidator validator = new GrayscaleValidator(grayscaleQConfig: grayscaleQConfig)

    def "Validate"() {
        given:
        grayscaleQConfig.isGrayscaleUid("uid") >> isGrayscaleUid
        grayscaleQConfig.isGrayscaleCityRateLimit(1, "2") >> isRateLimit
        grayscaleQConfig.isGrayscaleCityAndCarTypeIdRateLimit(1, 1, "2") >> isRateLimit

        when:
        ValidatorDTO result = validator.validate(new SupplyOrder(sourceOrderId: "1", orderId: "2", uid: "uid", cityId: 1, carTypeId: 1))

        then:
        result.getCode() == code

        where:
        isNew | isGrayscaleUid | isRateLimit || code
        true  | false          | false       || ValidatorCode.GRAYSCALE_UID
        true  | true           | false       || ValidatorCode.GRAYSCALE_CITY_RATE
        true  | true           | true        || ValidatorCode.OK
    }
}
