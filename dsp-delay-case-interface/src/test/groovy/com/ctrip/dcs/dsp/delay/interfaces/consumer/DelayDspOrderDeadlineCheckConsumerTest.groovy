package com.ctrip.dcs.dsp.delay.interfaces.consumer

import com.ctrip.dcs.dsp.delay.gateway.SelfDispatcherOrderGateway
import com.ctrip.dcs.dsp.delay.model.SupplyOrder
import qunar.tc.qmq.Message
import qunar.tc.qmq.NeedRetryException
import spock.lang.Specification

/**
 * <AUTHOR>
 */
class DelayDspOrderDeadlineCheckConsumerTest extends Specification {

    Message message = Mock(Message)

    SelfDispatcherOrderGateway selfDispatcherOrderGateway = Mock(SelfDispatcherOrderGateway)

    DelayDspOrderDeadlineCheckConsumer consumer = new DelayDspOrderDeadlineCheckConsumer(selfDispatcherOrderGateway: selfDispatcherOrderGateway)

    def "OnMessage"() {
        given:
        message.getStringProperty("userOrderId") >> "1"
        selfDispatcherOrderGateway.queryByUserOrderId("1") >> new SupplyOrder(orderStatus: 2, orderStatusDetail: 201)

        when:
        def result = consumer.onMessage(message)

        then:
        result == null
    }

    def "OnMessageException"() {
        given:
        message.getStringProperty("userOrderId") >> "1"
        selfDispatcherOrderGateway.queryByUserOrderId("1") >> null

        when:
        consumer.onMessage(message)

        then:
        NeedRetryException e = thrown(NeedRetryException)
        e.getNext() > 0
    }

    def "IsServiceConfirm"() {
        given:
        SupplyOrder supplyOrder1 = null
        SupplyOrder supplyOrder2 = new SupplyOrder(orderStatus: 3, orderStatusDetail: 201)
        SupplyOrder supplyOrder3 = new SupplyOrder(orderStatus: 2, orderStatusDetail: 201)
        when:
        boolean f1 = consumer.isServiceConfirm(supplyOrder1)
        boolean f2 = consumer.isServiceConfirm(supplyOrder2)
        boolean f3 = consumer.isServiceConfirm(supplyOrder3)
        then:
        f1 == false
        f2 == false
        f3 == true
    }
}
