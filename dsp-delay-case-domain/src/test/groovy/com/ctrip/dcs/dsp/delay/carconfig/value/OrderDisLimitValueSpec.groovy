package com.ctrip.dcs.dsp.delay.carconfig.value

import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 */
class OrderDisLimitValueSpec extends Specification {

    def value = new OrderDisLimitValue("limitDisLower": 10, "limitDisUpper": 20)

    @Unroll
    def "match"() {

        given: "Mock数据"
        value.setInUse(used)

        when: "执行校验方法"
        def res = value.match(kilo)

        then: "验证校验结果"
        res == ok

        where:
        kilo | used || ok
        10   | 0    || false
        10   | 1    || true
        9    | 1    || false
        30   | 1    || false
    }
}
