package com.ctrip.dcs.dsp.delay.model;

import com.ctrip.dcs.dsp.delay.enums.TransportGroupMode;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections.CollectionUtils;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class Driver {

    private String driverId;

    private Integer cityId;

    private String cityCode;

    private Integer status;

    private Integer carTypeId;

    private Integer coopMode;

    private String carLicense;

    private String overAgeTime;

    /**
     * 家庭住址经度
     */
    private Double addressLongitude;

    /**
     * 家庭住址维度
     */
    private Double addressLatitude;

    /**
     * 是否是新能源
     * 0-非新能源，1-新能源
     */
    private Integer isEnergy;

    /**
     * 工作时段
     */
    private List<String> workTimes;

    /**
     * 运力组列表
     */
    private List<TransportGroupInfo> transportGroups;

    public Integer getRegisterTransportGroupId() {
        if (CollectionUtils.isEmpty(transportGroups)) {
            return null;
        }
        for (TransportGroupInfo transportGroupInfo : transportGroups) {
            if (Objects.equals(transportGroupInfo.getTransportGroupMode(), TransportGroupMode.REGISTER_DISPATCH.getCode())) {
                return transportGroupInfo.getTransportGroupId();
            }
        }
        return null;
    }


    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Driver driver = (Driver) o;
        return Objects.equals(driverId, driver.driverId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(driverId);
    }
}
