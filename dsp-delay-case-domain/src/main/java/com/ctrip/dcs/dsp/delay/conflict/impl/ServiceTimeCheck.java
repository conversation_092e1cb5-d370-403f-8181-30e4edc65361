package com.ctrip.dcs.dsp.delay.conflict.impl;

import com.ctrip.dcs.dsp.delay.conflict.Check;
import com.ctrip.dcs.dsp.delay.conflict.CheckCode;
import com.ctrip.dcs.dsp.delay.conflict.ConflictContext;
import com.ctrip.dcs.dsp.delay.model.DelayDspOrder;
import com.ctrip.dcs.dsp.delay.util.DateUtil;
import org.springframework.stereotype.Component;

/**
 * 两单用车时间是否冲突
 * <AUTHOR>
 */
@Component("serviceTimeCheck")
public class ServiceTimeCheck implements Check {

    @Override
    public CheckCode check(ConflictContext context) {

        DelayDspOrder prev = context.getPrev();
        DelayDspOrder next = context.getNext();
        boolean ok = DateUtil.notOverlap(
                prev.getSysExpectBookTime(),
                prev.getPredictServiceStopTime(),
                next.getSysExpectBookTime(),
                next.getPredictServiceStopTime()
        );
        return ok ? CheckCode.OK : CheckCode.SERVICE_TIME_CONFLICT;
    }
}
