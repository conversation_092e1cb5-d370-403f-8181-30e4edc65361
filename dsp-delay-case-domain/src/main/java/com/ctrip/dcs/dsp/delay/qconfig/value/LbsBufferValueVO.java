package com.ctrip.dcs.dsp.delay.qconfig.value;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class LbsBufferValueVO implements CarConfigValueVO {

    /**
     * 开始时间(相对00:00)
     */
    private String startTime;

    /**
     * 结束时间(相对00:00)
     */
    private String endTime;

    //fixValue 固定值 ratio比例
    private String valueType;
    /**
     * 值   分钟数或者比例（比例的基数是行程的lbs时长）
     */
    private Double value;

    @JsonCreator
    public LbsBufferValueVO(@JsonProperty("startTime") String startTime, @JsonProperty("endTime") String endTime, @JsonProperty("valueType") String valueType, @JsonProperty("value") Double value) {
        this.startTime = startTime;
        this.endTime = endTime;
        this.valueType = valueType;
        this.value = value;
    }

    public Integer getType() {
        if ("fixValue".equalsIgnoreCase(valueType)) {
            return 0;
        }
        if ("ratio".equalsIgnoreCase(valueType)) {
            return 1;
        }
        return 0;
    }
}
