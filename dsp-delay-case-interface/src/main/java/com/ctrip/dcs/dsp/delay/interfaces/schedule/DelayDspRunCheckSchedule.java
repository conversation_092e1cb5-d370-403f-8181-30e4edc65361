package com.ctrip.dcs.dsp.delay.interfaces.schedule;

import com.ctrip.dcs.dsp.delay.enums.DelayDspTaskStatus;
import com.ctrip.dcs.dsp.delay.enums.MatchTaskStatus;
import com.ctrip.dcs.dsp.delay.model.DelayDspTask;
import com.ctrip.dcs.dsp.delay.qconfig.DelayDspCommonQConfig;
import com.ctrip.dcs.dsp.delay.repository.DelayDspTaskRepository;
import com.ctrip.dcs.dsp.delay.util.DateUtil;
import com.ctrip.dcs.dsp.delay.util.JsonUtil;
import com.ctrip.dcs.dsp.delay.util.MetricsUtil;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerContext;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import qunar.tc.qschedule.config.QSchedule;
import qunar.tc.schedule.Parameter;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * 检查是否存在仍未执行的匹配任务
 */
@Component
public class DelayDspRunCheckSchedule {

    private static final Logger logger = LoggerFactory.getLogger(DelayDspRunCheckSchedule.class);

    @Autowired
    private DelayDspTaskRepository delayDspTaskRepository;

    @Autowired
    private DelayDspCommonQConfig delayDspCommonQConfig;


    @QSchedule("dcs.dsp.delay.task.run.task.check")
    public void execute(Parameter parameter) {
        LoggerContext.newContext().withGlobalTraceId(UUID.randomUUID().toString()).init();
        Integer minuter = delayDspCommonQConfig.getWarnDeadTime();
        Date date = DateUtil.addMinutes(new Date(), minuter);
        logger.info("execute qschedule","date: {}", date);
        List<DelayDspTask> list = delayDspTaskRepository.queryCountByDeadTime(date);
        logger.info("DelayDspRunCheckSchedule_qschedule", JsonUtil.toJson(list));
        if(CollectionUtils.isEmpty(list)){
            return;
        }
        MetricsUtil.recordValue("delay.dsp.run.check.schedule", list.size());
        list.forEach(task -> delayDspTaskRepository.updateMatchInfo(task.getTaskId(), DelayDspTaskStatus.UN_EXECUTED.getCode(), null, null, MatchTaskStatus.END.getCode()));
    }
}
