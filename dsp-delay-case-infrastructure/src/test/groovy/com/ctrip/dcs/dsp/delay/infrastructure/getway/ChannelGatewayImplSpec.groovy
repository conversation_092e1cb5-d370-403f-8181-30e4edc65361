package com.ctrip.dcs.dsp.delay.infrastructure.getway

import com.ctrip.dcs.dsp.delay.infrastructure.geteway.ChannelGatewayImpl
import com.ctrip.dcs.dsp.delay.infrastructure.soa.ShoppingCommonServiceProxy
import com.ctrip.dcs.dsp.delay.model.ChannelNumber
import com.ctrip.dcs.shopping.common.interfaces.dto.ChannelNumberDTO
import com.ctrip.dcs.shopping.common.interfaces.message.QueryChannelNumbersResponseType
import com.ctrip.igt.ResponseResult
import com.google.common.collect.Lists
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 */
class ChannelGatewayImplSpec extends Specification {

    def shoppingCommonServiceProxy = Mock(ShoppingCommonServiceProxy)

    def gateway = new ChannelGatewayImpl("shoppingCommonServiceProxy": shoppingCommonServiceProxy)

    @Unroll
    def "query"() {

        given: "Mock数据"
        shoppingCommonServiceProxy.queryChannelNumbers(_) >> dto

        when: "执行校验方法"
        def res = gateway.queryChannelNumber(channelId)

        then: "验证校验结果"
        res.channelNumberId == channel.channelNumberId

        where:
        channelId | dto      || channel
        1L        | getDTO(1L) || new ChannelNumber("channelNumberId": 1L)
    }

    def getDTO(long channelNumberId) {
        def channelNumberList = Lists.newArrayList(new ChannelNumberDTO("channelNumberId" : channelNumberId))
        return new QueryChannelNumbersResponseType("channelNumberList" : channelNumberList, responseResult: new ResponseResult(success: true))
    }
}
