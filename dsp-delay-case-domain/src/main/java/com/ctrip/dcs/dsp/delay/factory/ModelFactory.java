package com.ctrip.dcs.dsp.delay.factory;

import com.ctrip.dcs.dsp.delay.carconfig.value.DelayDspSubSkuValue;
import com.ctrip.dcs.dsp.delay.carconfig.value.DelayDspTaskValue;
import com.ctrip.dcs.dsp.delay.consts.CommonConstant;
import com.ctrip.dcs.dsp.delay.context.DelayDspContext;
import com.ctrip.dcs.dsp.delay.enums.*;
import com.ctrip.dcs.dsp.delay.gateway.GeoGateway;
import com.ctrip.dcs.dsp.delay.model.*;
import com.ctrip.dcs.dsp.delay.util.DateUtil;
import com.ctrip.dcs.dsp.delay.util.GeoHashUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class ModelFactory {

    public static DelayDspTask buildDelayDspTask(String cityCode, Integer cityId, Integer carTypeId, Date datetime, DelayDspTaskValue value) {
        DelayDspTask task = new DelayDspTask();
        task.setCityCode(cityCode);
        task.setCityId(cityId);
        task.setCarTypeId(carTypeId);
        task.setTaskStatus(DelayDspTaskStatus.UN_EXECUTED.getCode());
        task.setTaskSeq(value.getTaskSeq());
        task.setBeginTime(value.getBeginTime(datetime));
        task.setEndTime(value.getEndTime(datetime));
        task.setTaskType(value.getType());
        if (task.getBeginTime() != null && task.getEndTime() != null) {    // 跨天
            if (task.getEndTime().before(task.getBeginTime())) {
                task.setEndTime(DateUtil.addDays(task.getEndTime(), 1));
            }
            task.setExecuteTime(DateUtil.addHours(task.getBeginTime(), -value.getHour()));
            // 最晚执行时间=任务开发时间范围-配置的小时
            task.setExecuteTimeDeadline(DateUtil.addHours(task.getBeginTime(), -value.getDeadline()));
            if (DelayDspTaskType.isSD(value.getType())) {
                task.setExecuteTime(DateUtil.addHours(datetime, -value.getHour()));
                // 最晚执行时间=任务开发时间范围-配置的小时
                task.setExecuteTimeDeadline(DateUtil.addHours(datetime, -value.getDeadline()));
            }
        }
        return task;
    }
    public static DelayDspOrder buildDelayDspOrder(SupplyOrder supplyOrder, DelayDspTask task, DelayDspSubSkuValue subSku, OrderSource orderSource) {
        DelayDspOrder order = buildDelayDspOrder(supplyOrder);
        order.setTaskId(task.getTaskId());
        order.setOrderSource(orderSource.name());
        if (Objects.equals(order.getIsDelay(), YesOrNo.YES.getCode())) {
            String duid = new Duid(supplyOrder.getOrderId(), subSku.getOutSubSkuIdList().get(0)).toString();
            order.setDuid(duid);
        }
        return order;
    }

    public static DelayDspOrder buildDelayDspOrder(SupplyOrder supplyOrder, DelayDspTask task, String duid, OrderSource orderSource) {
        DelayDspOrder order = buildDelayDspOrder(supplyOrder);
        order.setTaskId(task.getTaskId());
        order.setOrderSource(orderSource.name());
        order.setDuid(duid);
        return order;
    }

    public static DelayDspOrder buildDelayDspOrder(SupplyOrder supplyOrder) {
        DelayDspOrder order = new DelayDspOrder();
        order.setOrderId(supplyOrder.getOrderId());
        order.setMainOrderId(supplyOrder.getSourceOrderId());
        order.setCityId(supplyOrder.getCityId());
        order.setCityCode(supplyOrder.getCityCode());
        order.setCarTypeId(supplyOrder.getCarTypeId());
        order.setSkuId(supplyOrder.getSkuId());
        order.setChannelId(supplyOrder.getDistributionChannel());
        order.setSysExpectBookTime(supplyOrder.getSysExpectBookTime());
        order.setPredictServiceStopTime(supplyOrder.getPredicServiceStopTime());
        order.setCategoryCode(supplyOrder.getCategoryCode());
        order.setIsOta(supplyOrder.getIsOtaBookOrder());
        order.setFromLongitude(supplyOrder.getActualFromLongitude());
        order.setFromLatitude(supplyOrder.getActualFromLatitude());
        order.setFromCoordsys(supplyOrder.getActualFromCoordsys());
        order.setFromHash(GeoHashUtil.buildGeoHash(supplyOrder.getActualFromLongitude(), supplyOrder.getActualFromLatitude()));
        order.setToLongitude(supplyOrder.getActualToLongitude());
        order.setToLatitude(supplyOrder.getActualToLatitude());
        order.setToCoordsys(supplyOrder.getActualToCoordsys());
        order.setToHash(GeoHashUtil.buildGeoHash(supplyOrder.getActualToLongitude(), supplyOrder.getActualToLatitude()));
        order.setDriverId(supplyOrder.getDriverId());
        order.setIsCancel(supplyOrder.isCancel() ? YesOrNo.YES.getCode() : YesOrNo.NO.getCode());
        order.setIsDelay(supplyOrder.isTaken() ? YesOrNo.NO.getCode() : YesOrNo.YES.getCode());
        PredictPriceInfo predictPriceInfo = supplyOrder.getPredictPriceInfo();
        if (Objects.nonNull(predictPriceInfo)) {
            order.setKiloLength(predictPriceInfo.getKiloLength());
            order.setDriverOrderFee(predictPriceInfo.getDrivTotalFee());
        }
        return order;
    }

    public static List<Position> buildPositions(DelayDspTask task, DelayDspOrder order, List<DelayDspOrder> orders, List<Driver> drivers) {

        List<DelayDspOrder> adjacency = orders.stream()
                .filter(o -> Objects.equals(o.getIsCancel(), YesOrNo.NO.getCode()))
                .filter(o -> !Objects.equals(o.getOrderId(), order.getOrderId()))
                .filter(o -> {
                    if (Objects.equals(o.getIsDelay(), YesOrNo.YES.getCode()) || Objects.equals(order.getIsDelay(), YesOrNo.YES.getCode())) {
                        return true;
                    }
                    // 若两个订单都被司机接起，但是司机不相同，则两个订单不会互相链接，所以不需要计算这两单直接的空驶
                    return Objects.equals(o.getDriverId(), order.getDriverId());
                })
                .sorted(Comparator.comparing(DelayDspOrder::getPredictServiceStopTime))
                .collect(Collectors.toList());

        Set<Position> positions = Sets.newHashSet();
        // 首单&尾单点位
        for (Driver driver : drivers) {
            Position position = new Position(driver, order);
            if (Objects.equals(position.getFromHash(), position.getToHash())) {
                continue;
            }
            positions.add(position);
            // 订单在结束工作时间前2小时内的，需要构建订单完成地点到司机家的点位
            Date date = DateUtil.addHours(task.getEndTime(), -CommonConstant.DRIVER_WORK_TIME_RANGE_HOUR);
            if (order.getSysExpectBookTime().after(date)) {
                positions.add(new Position(order, driver, order.getPredictServiceStopTime()));
            }
        }
        // 相邻单点位
        for (DelayDspOrder adj : adjacency) {
            Position position;
            if (adj.getPredictServiceStopTime().before(order.getPredictServiceStopTime())) {
                position = new Position(adj, order,adj.getPredictServiceStopTime());
            } else {
                position = new Position(order, adj, order.getPredictServiceStopTime());
            }
            if (Objects.equals(position.getFromHash(), position.getToHash())) {
                continue;
            }
            positions.add(position);
        }
        List<Position> list = Lists.newArrayList(positions);
        list.sort(Comparator.comparing(Position::hash));
        return list;
    }


    public static List<DelayDspMatchTaskRecord> buildDelayDspMatchTaskRecords(DelayDspTask task, List<DispatchResultDetail> details, Date nowDate) {
        List<DelayDspMatchTaskRecord> list = Lists.newArrayList();
        for (DispatchResultDetail detail : details) {
            DelayDspMatchTaskRecord record = new DelayDspMatchTaskRecord();
            record.setTaskId(task.getTaskId());
            record.setTaskVersion(task.getTaskVersion());
            record.setOrderId(detail.getOrder().getOrderId());
            record.setUserOrderId(detail.getOrder().getMainOrderId());
            record.setDriverId("");
            record.setIsOut(YesOrNo.NO.getCode());
            record.setTakenCode(-1);
            record.setOutReason(-1);
            record.setOrderSource(detail.getOrder().getOrderSource());
            record.setIsDelay(0);
            record.setMatchSuccess(YesOrNo.NO.getCode());
            record.setOrderSeq(Optional.ofNullable(detail.getOrderSeq()).orElse(NumberUtils.INTEGER_ZERO));
            record.setExecuteTime(nowDate);
            record.setDriverOrderId("");

            if (Objects.equals(detail.getMatchSuccess(), YesOrNo.YES.getCode())) {
                record.setMatchSuccess(YesOrNo.YES.getCode());
                record.setDriverId(detail.getDriver().getDriverId());
                Integer isDelay = detail.getOrder().getIsDelay();
                if(Objects.equals(isDelay, YesOrNo.YES.getCode())){
                    record.setIsDelay(YesOrNo.YES.getCode());
                    record.setIsOut(YesOrNo.NO.getCode());
                }else {
                    record.setIsDelay(YesOrNo.NO.getCode());
                    record.setIsOut(YesOrNo.YES.getCode());
                    record.setOutReason(OutReasonEnum.IS_NOT_DELAY.getCode());
                }
            }else{
                record.setMatchSuccess(YesOrNo.NO.getCode());
                record.setIsOut(YesOrNo.NO.getCode());
                record.setIsDelay(YesOrNo.YES.getCode());
                record.setDriverId("");
            }
            list.add(record);
        }
        return list;
    }


    public static List<DelayDspTaskRecord> buildDelayDspTaskRecordsByMatchRecord(List<DelayDspMatchTaskRecord> details) {
        List<DelayDspTaskRecord> list = Lists.newArrayList();
        for (DelayDspMatchTaskRecord record : details) {
            DelayDspTaskRecord delayDspTaskRecord = new DelayDspTaskRecord();
            delayDspTaskRecord.setTaskId(record.getTaskId());
            delayDspTaskRecord.setOrderId(record.getOrderId());
            delayDspTaskRecord.setDriverId(record.getDriverId());
            delayDspTaskRecord.setDriverScore(NumberUtils.DOUBLE_ZERO);
            delayDspTaskRecord.setOrderSeq(record.getOrderSeq());
            delayDspTaskRecord.setTakenCode(OrderTakenCode.SUCCESS.getCode());
            delayDspTaskRecord.setEmptyDistance(NumberUtils.DOUBLE_ZERO);
            delayDspTaskRecord.setEmptyDuration(NumberUtils.DOUBLE_ZERO);
            delayDspTaskRecord.setMatchSuccess(record.getMatchSuccess());
            delayDspTaskRecord.setIsOut(record.getIsOut());
            delayDspTaskRecord.setDriverOrderId(record.getDriverOrderId());
            delayDspTaskRecord.setOrderSource(record.getOrderSource());
            delayDspTaskRecord.setPreOut(YesOrNo.YES.getCode());
            delayDspTaskRecord.setMatchRecordId(record.getId());
            list.add(delayDspTaskRecord);
        }
        return list;
    }



    public static List<DelayDspTaskRecord> buildDelayDspTaskRecords(DelayDspTask task, List<DispatchResultDetail> details) {
        List<DelayDspTaskRecord> list = Lists.newArrayList();
        for (DispatchResultDetail detail : details) {
            // 延后派订单：未出池。非延后派订单：已出池
            Integer out = Objects.equals(detail.getOrder().getIsDelay(), YesOrNo.YES.getCode()) ? YesOrNo.NO.getCode() : YesOrNo.YES.getCode();
            DelayDspTaskRecord record;
            if (Objects.equals(detail.getMatchSuccess(), YesOrNo.YES.getCode())) {
                record = new DelayDspTaskRecord(
                        task.getTaskId(),
                        detail.getOrder().getOrderId(),
                        detail.getDriver().getDriverId(),
                        Optional.ofNullable(detail.getDriverScore()).map(DriverScore::getTotalScore).orElse(NumberUtils.DOUBLE_ZERO),
                        Optional.ofNullable(detail.getOrderSeq()).orElse(NumberUtils.INTEGER_ZERO),
                        Optional.ofNullable(detail.getEmpty()).map(Route::getDistance).orElse(NumberUtils.DOUBLE_ZERO),
                        Optional.ofNullable(detail.getEmpty()).map(Route::getDuration).orElse(NumberUtils.DOUBLE_ZERO),
                        out,
                        detail.getDriverOrderId(),
                        detail.getOrder().getOrderSource()
                );
            } else {
                record = new DelayDspTaskRecord(task.getTaskId(), detail.getOrder().getOrderId(), out, detail.getOrder().getOrderSource());
            }
            record.setPreOut(YesOrNo.NO.getCode());
            list.add(record);
        }
        return list;
    }

    public static List<DispatchResultDetail> buildDispatchResultDetails(DelayDspContext context, DriverAggregation driverAggregation, List<DelayDspOrder> list, GeoGateway geoGateway) {
        if (CollectionUtils.isEmpty(list)) {
            return Lists.newArrayList();
        }
        DelayDspTask task = context.getDelayDspTask();
        List<DispatchResultDetail> details = Lists.newArrayList();
        DelayDspOrder first = list.get(0);
        Route firstRoute = geoGateway.queryRoute(task.getTaskId(), new Position(driverAggregation.getDriver(), first));
        details.add(new DispatchResultDetail(driverAggregation, first, 0, firstRoute));
        DelayDspOrder prev = first;
        for (int i = 1; i < list.size(); i++) {
            DelayDspOrder next = list.get(i);
            Route route = geoGateway.queryRoute(task.getTaskId(), new Position(prev, next));
            details.add(new DispatchResultDetail(driverAggregation, next, i, route));
            prev = next;
        }
        return details;
    }

}
