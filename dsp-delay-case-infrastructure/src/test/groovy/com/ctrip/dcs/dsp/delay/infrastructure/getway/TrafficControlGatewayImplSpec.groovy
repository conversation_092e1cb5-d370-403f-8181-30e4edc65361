package com.ctrip.dcs.dsp.delay.infrastructure.getway

import com.ctrip.dcs.dsp.delay.infrastructure.geteway.TrafficControlGatewayImpl
import com.ctrip.dcs.dsp.delay.infrastructure.soa.ShoppingServiceProxy
import com.ctrip.dcs.dsp.delay.qconfig.DelayDspCommonQConfig
import com.ctrip.dcs.dsp.delay.util.DateUtil
import com.ctrip.dcs.price.interfaces.dto.trafficcontrol.CheckDriverTrafficControlResponseType
import com.ctrip.igt.RestResponseResult
import com.google.common.collect.Lists
import com.google.common.collect.Sets
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 */
class TrafficControlGatewayImplSpec extends Specification {

    def shoppingServiceProxy = Mock(ShoppingServiceProxy)

    def config = Mock(DelayDspCommonQConfig)

    def gateway = new TrafficControlGatewayImpl("shoppingServiceProxy" : shoppingServiceProxy, delayDspCommonQConfig: config)

    @Unroll
    def "query"() {

        given: "Mock数据"
        shoppingServiceProxy.checkDriverTrafficControl(_) >> getDTO()
        config.getTrafficControlServiceProviderId() >> 1

        when: "执行校验方法"
        def begin = DateUtil.parseDate("2022-05-07 08:00:00")
        def end = DateUtil.parseDate("2022-05-07 20:00:00")
        def res = gateway.queryLimitLicense(1, begin, end, Sets.newHashSet("a", "b", "c"))

        then: "验证校验结果"
        res.size() == 2
    }

    def getDTO() {
        def limitLicensePlateNumberList = Lists.newArrayList("a", "c")
        return new CheckDriverTrafficControlResponseType("limitLicensePlateNumberList" : limitLicensePlateNumberList, resstatus : new RestResponseResult(rcode: "200"))
    }
}
