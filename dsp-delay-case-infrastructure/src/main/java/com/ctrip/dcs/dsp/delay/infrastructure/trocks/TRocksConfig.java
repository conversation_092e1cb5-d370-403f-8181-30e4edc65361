package com.ctrip.dcs.dsp.delay.infrastructure.trocks;

import credis.java.client.sync.trocks.TRocksCacheProvider;
import credis.java.client.sync.trocks.TRocksFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@Configuration
public class TRocksConfig {

    @Bean(name = "tRocksCacheProvider")
    public TRocksCacheProvider provider() {
        return TRocksFactory.getProvider("DCSDelayDispatchtrocks");
    }

}
