package com.ctrip.dcs.dsp.delay.infrastructure.getway

import com.ctrip.dcs.dsp.delay.infrastructure.geteway.GeoGatewayImpl
import com.ctrip.dcs.dsp.delay.infrastructure.soa.OchGeoServiceProxy
import com.ctrip.dcs.dsp.delay.infrastructure.trocks.TRocksProviderProxy
import com.ctrip.dcs.dsp.delay.limit.LbsRateLimiter
import com.ctrip.dcs.dsp.delay.model.Position
import com.ctrip.dcs.dsp.delay.util.MetricsUtil
import com.ctrip.dcs.dsp.delay.util.SpringApplicationContextUtil
import com.ctrip.igt.framework.common.metrics.MetricsFactory
import com.ctrip.igt.geo.interfaces.dto.GaodeDistanceInfoDTO
import com.ctrip.igt.geo.interfaces.message.QueryDistanceBatchResponseType
import com.github.benmanes.caffeine.cache.Cache
import com.google.common.collect.Lists
import io.dropwizard.metrics5.MetricRegistry
import org.junit.runner.RunWith
import org.mockito.Mock
import org.powermock.api.mockito.PowerMockito
import org.powermock.core.classloader.annotations.PrepareForTest
import org.powermock.core.classloader.annotations.SuppressStaticInitializationFor
import org.powermock.modules.junit4.PowerMockRunner
import org.powermock.modules.junit4.PowerMockRunnerDelegate
import org.spockframework.runtime.Sputnik
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 */
@RunWith(PowerMockRunner.class)
@PowerMockRunnerDelegate(Sputnik.class)
@PrepareForTest([SpringApplicationContextUtil.class, MetricsUtil.class, MetricsFactory.class])
@SuppressStaticInitializationFor(["com.ctrip.dcs.dsp.delay.util.SpringApplicationContextUtil", "com.ctrip.dcs.dsp.delay.util.MetricsUtil", "com.ctrip.igt.framework.common.metrics.MetricsFactory"])

class GeoGatewayImplSpec extends Specification {

    def ochGeoServiceProxy = Mock(OchGeoServiceProxy)
    def trocksProviderProxy = Mock(TRocksProviderProxy)
    def caffeineCache = Mock(Cache)
    def limiter = Mock(LbsRateLimiter)

    def gateway = new GeoGatewayImpl("ochGeoServiceProxy": ochGeoServiceProxy, "trocksProviderProxy": trocksProviderProxy, "caffeineCache": caffeineCache, rateLimiter: limiter)

    @Mock
    private MetricRegistry metricRegistry;

    void setup() {
        PowerMockito.mockStatic(SpringApplicationContextUtil.class)
        PowerMockito.mockStatic(MetricsFactory.class)
        PowerMockito.mockStatic(MetricsUtil.class)
        PowerMockito.when(MetricsFactory.getMetricRegistry()).thenReturn(metricRegistry);
    }

    @Unroll
    def "query"() {

        given: "Mock数据"
        ochGeoServiceProxy.queryDistanceBatch(_) >> dto
        when: "执行校验方法"
        def res = gateway.queryRoutes(cityId, list as List<Position>)

        then: "验证校验结果"
        res.size() == size

        where:
        cityId | list           | dto                                  || size
        1      | getPositions() | null                                 || 0
        1      | getPositions() | new QueryDistanceBatchResponseType() || 0
        1      | getPositions() | getDistanceDTO()                     || 1
    }

    @Unroll
    def "query cache"() {

        given: "Mock数据"
        caffeineCache.get(_, _) >> v
        when: "执行校验方法"
        def res = gateway.queryRoute(taskId, position)

        then: "验证校验结果"
        res.distance == dis

        where:
        taskId | position      | v      || dis
        1      | getPosition("a", "a") | ""     || 0
        1      | getPosition("a", "b") | ""     || Integer.MAX_VALUE
        1      | getPosition("a", "b") | "10#5" || 10
    }

    def getPosition(String from, String to) {
        return new Position(fromHash: from, toHash: to)
    }

    def getPositions() {
        return Lists.newArrayList(new Position("fromLongitude": 1.1, "fromLatitude": 2.2, "toLongitude": 3.3, "toLatitude": 3.3))
    }

    def getDistanceDTO() {
        def results = Lists.newArrayList(new GaodeDistanceInfoDTO(
                "originLatitude": BigDecimal.ONE,
                "originLongitude": BigDecimal.ONE,
                "destinationLatitude": BigDecimal.ONE,
                "destinationLongitude": BigDecimal.ONE,
                "distance": 10,
                "duration": 10,
        ))
        return new QueryDistanceBatchResponseType("results": results)
    }
}
