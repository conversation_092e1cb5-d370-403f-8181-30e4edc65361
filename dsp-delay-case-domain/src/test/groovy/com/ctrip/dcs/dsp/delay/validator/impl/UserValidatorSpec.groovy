package com.ctrip.dcs.dsp.delay.validator.impl

import com.ctrip.dcs.dsp.delay.model.SupplyOrder
import com.ctrip.dcs.dsp.delay.qconfig.DelayDspCommonQConfig
import com.ctrip.dcs.dsp.delay.validator.ValidatorCode
import com.google.common.collect.Lists
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 */
class UserValidatorSpec extends Specification {

    def config = Mock(DelayDspCommonQConfig)

    def validator = new UserValidator(delayDspCommonQConfig: config)

    @Unroll
    def "validate"() {

        given: "Mock数据"
        def order = new SupplyOrder(uid: uid)
        config.getUserWhiteList() >> list

        when: "执行校验方法"
        def res = validator.validate(order)

        then: "验证校验结果"
        res.getCode() == code

        where:
        uid | list            || code
        "1" | newList("1") || ValidatorCode.OK
        "1" | newList("2") || ValidatorCode.UID
    }

    def newList(String s) {
        return Lists.newArrayList(s)
    }
}
