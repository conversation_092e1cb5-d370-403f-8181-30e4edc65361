package com.ctrip.dcs.dsp.delay.conflict;

import com.ctrip.dcs.dsp.delay.qconfig.DelayDspChecksQConfig;
import com.ctrip.dcs.dsp.delay.qconfig.value.DelayDspCheckValueVO;
import com.ctrip.dcs.dsp.delay.util.JsonUtil;
import com.ctrip.dcs.dsp.delay.util.MetricsUtil;
import com.ctrip.dcs.dsp.delay.util.SpringApplicationContextUtil;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 */
@Component
public class CheckChain {

    private final Logger logger = LoggerFactory.getLogger(CheckChain.class);

    @Autowired
    private DelayDspChecksQConfig delayDspChecksQConfig;

    public CheckCode check(ConflictContext context) {
        List<Check> checks = buildChecks(context);
        return check(context, checks);
    }

    private CheckCode check(ConflictContext context, List<Check> checks) {
        for (Check check : checks) {
            CheckCode code = check.check(context);
            if (code.isConflict()) {
                return code;
            }
        }
        return CheckCode.OK;
    }

    private List<Check> buildChecks(ConflictContext context) {
        List<String> checkNames = Lists.newArrayList();
        DelayDspCheckValueVO checkValueVO = delayDspChecksQConfig.getCheckValueVO(context.getDelayDspTask().getCityId());
        switch (context.getCheckType()) {
            case SINGLE:
                checkNames = checkValueVO.getDriverToOrderCheckList();
                break;
            case PAIR:
                checkNames = checkValueVO.getOrderToOrderCheckList();
                break;
            case LIST:
                checkNames = checkValueVO.getOrderToTakenCheckList();
                break;
        }
        List<Check> checks = Lists.newArrayList();
        logger.info("CheckChain.buildCheck", JsonUtil.toJson(checkNames) );
        for (String name : checkNames) {
            try {
                Check check = SpringApplicationContextUtil.getBean(name, Check.class);
                checks.add(check);
            } catch (Exception e) {
                MetricsUtil.recordValue("conflict.check.chain.build.error", 1);
                logger.error("CheckChain.buildCheck", "there is no check. name: {}, error: {}", name, e);
            }
        }
        return checks;
    }
}
