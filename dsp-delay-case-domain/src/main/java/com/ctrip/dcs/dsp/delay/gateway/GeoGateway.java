package com.ctrip.dcs.dsp.delay.gateway;

import com.ctrip.dcs.dsp.delay.model.Position;
import com.ctrip.dcs.dsp.delay.model.Route;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface GeoGateway {

    /**
     * lbs
     * @param cityId
     * @param positions
     * @return
     */
    List<Route> queryRoutes(Integer cityId, List<Position> positions);

    Route queryRoute(Long taskId, Position position);


    List<Route> queryRoutes(Integer cityId, String orderId, List<Position> positions);
}
