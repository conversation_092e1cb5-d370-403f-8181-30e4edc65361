package com.ctrip.dcs.dsp.delay.service.impl


import com.ctrip.dcs.dsp.delay.gateway.InventoryGateway
import com.ctrip.dcs.dsp.delay.model.DelayDspTask
import com.ctrip.dcs.dsp.delay.model.SupplyOrder
import org.assertj.core.util.Lists
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 */
class DriverInventoryServiceImplSpec extends Specification {

    def inventoryGateway = Mock(InventoryGateway)


    def driverInventoryServiceImpl = new DriverInventoryServiceImpl("inventoryGateway": inventoryGateway)

    @Unroll
    def "query and create task"() {
        given: "Mock数据"
        inventoryGateway.checkInventoryConflict(_, _) >> null
        when: "执行校验方法"
        def res = driverInventoryServiceImpl.checkInventoryConflict(Lists.newArrayList(), new SupplyOrder())

        then: "验证校验结果"
        Objects.isNull(res)

    }
}
