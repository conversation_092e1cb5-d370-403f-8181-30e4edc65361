package com.ctrip.dcs.dsp.delay.infrastructure.soa;

import com.ctrip.igt.framework.soa.client.ServiceClient;
import com.ctrip.igt.geo.interfaces.OchGeoServiceClient;
import com.ctrip.igt.geo.interfaces.message.QueryDistanceBatchRequestType;
import com.ctrip.igt.geo.interfaces.message.QueryDistanceBatchResponseType;

/**
 * <AUTHOR>
 */
@ServiceClient(OchGeoServiceClient.class)
public interface OchGeoServiceProxy {

    QueryDistanceBatchResponseType queryDistanceBatch(QueryDistanceBatchRequestType request);
}
