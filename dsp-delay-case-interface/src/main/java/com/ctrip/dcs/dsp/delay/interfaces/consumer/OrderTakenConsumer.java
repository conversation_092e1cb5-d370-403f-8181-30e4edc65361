package com.ctrip.dcs.dsp.delay.interfaces.consumer;

import com.ctrip.dcs.dsp.application.service.DelayDspOrderApplicationService;
import com.ctrip.dcs.dsp.delay.consts.CommonConstant;
import com.ctrip.dcs.dsp.delay.enums.OrderSource;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.qmq.annontation.QmqLogTag;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;

/**
 * <AUTHOR>
 */
@Component
public class OrderTakenConsumer {

    private static final Logger logger = LoggerFactory.getLogger(OrderTakenConsumer.class);

    @Autowired
    private DelayDspOrderApplicationService delayDspApplicationService;

    @QmqLogTag()
    @QmqConsumer(prefix = CommonConstant.DSP_ORDER_DRIVER_CAR_CONFIRM_TOPIC, consumerGroup = CommonConstant.APP_ID)
    public void onMessage1(Message message) {
        try {
            String dspOrderId = message.getStringProperty("dspOrderId");
            if (StringUtils.isBlank(dspOrderId)) {
                return;
            }
            delayDspApplicationService.insertTakenDelayOrder(dspOrderId, OrderSource.CTRIP.name());

        } catch (Exception e) {
            logger.error("consume car.qbest.order.orderstate.ORDER_TAKEN! error!", e);
        }
    }
}
