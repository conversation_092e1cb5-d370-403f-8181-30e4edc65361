package com.ctrip.dcs.dsp.delay.validator.impl;

import com.ctrip.dcs.dsp.delay.enums.CoopMode;
import com.ctrip.dcs.dsp.delay.gateway.DriverGateway;
import com.ctrip.dcs.dsp.delay.model.Driver;
import com.ctrip.dcs.dsp.delay.model.SupplyOrder;
import com.ctrip.dcs.dsp.delay.validator.Validator;
import com.ctrip.dcs.dsp.delay.validator.ValidatorCode;
import com.ctrip.dcs.dsp.delay.validator.ValidatorDTO;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 */
@Component("driverValidator")
public class DriverValidator implements Validator {

    private static final Logger logger = LoggerFactory.getLogger(DriverValidator.class);

    @Autowired
    private DriverGateway driverGateway;

    @Override
    public ValidatorDTO validate(SupplyOrder order) {
        if (StringUtils.isBlank(order.getDriverId()) || "0".equals(order.getDriverId())) {
            return new ValidatorDTO(ValidatorCode.NO_DRIVER);
        }
        Driver driver = driverGateway.query(order.getDriverId());
        if (Objects.isNull(driver)) {
            return new ValidatorDTO(ValidatorCode.NO_DRIVER);
        }
        if (!Objects.equals(driver.getCoopMode(), CoopMode.FULL_TIME_ASSIGN.getCode())) {
            logger.info("DriverValidator", "driver is not full time!, order id: {}, driver id: {}", order.getOrderId(), driver.getDriverId());
            return new ValidatorDTO(ValidatorCode.NOT_FULL_TIME_DRIVER);

        }
        return new ValidatorDTO(ValidatorCode.OK);
    }
}
