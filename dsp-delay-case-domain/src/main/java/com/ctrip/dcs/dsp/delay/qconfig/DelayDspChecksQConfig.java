package com.ctrip.dcs.dsp.delay.qconfig;

import com.ctrip.dcs.dsp.delay.qconfig.key.CarConfigKeyVO;
import com.ctrip.dcs.dsp.delay.qconfig.key.DelayDspCheckKeyVO;
import com.ctrip.dcs.dsp.delay.qconfig.value.DelayDspCheckValueVO;
import org.springframework.stereotype.Component;
import qunar.tc.qconfig.client.spring.QConfig;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Component
public class DelayDspChecksQConfig extends BaseCarConfig<DelayDspCheckKeyVO, DelayDspCheckValueVO> {

    private static final DelayDspCheckValueVO DEFAULT_VALUE = new DelayDspCheckValueVO("carAgeOutTimeCheck,headOrderCheck,prevMileageCheck",
            "carAgeOutTimeCheck,serviceTimeCheck,emptyDurationCheck,prevMileageCheck",
            "carAgeOutTimeCheck,tailOrderCheck");

//检查项配置 5.10.26
    @QConfig("visualConfig_delayDspChecks_0.json")
    HashMap<String, Config<DelayDspCheckKeyVO, DelayDspCheckValueVO >> context;


    @Override
    Map<String, Config<DelayDspCheckKeyVO, DelayDspCheckValueVO>> getContext() {
        return this.context;
    }


    public DelayDspCheckValueVO getCheckValueVO(Integer cityId) {
        return get(cityId);
    }

    private DelayDspCheckValueVO get(Integer cityId) {
        DelayDspCheckValueVO valueVO = get(new DelayDspCheckKeyVO(cityId));

        if (Objects.isNull(valueVO)) {
            valueVO = get(new DelayDspCheckKeyVO(CarConfigKeyVO.ALL_CITY_ID));
        }
        return Objects.isNull(valueVO) ? DEFAULT_VALUE : valueVO;
    }


}
