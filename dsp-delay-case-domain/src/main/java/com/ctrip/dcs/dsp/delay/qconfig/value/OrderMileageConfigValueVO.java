package com.ctrip.dcs.dsp.delay.qconfig.value;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;

@Getter
public class OrderMileageConfigValueVO implements CarConfigValueVO {

    private String startKilo;
    private String endKilo;
    private String startTime;
    private String endTime;

    private String value;

    /**
     * 跨城订单后向订单里程价值
     */
    private String crossCityOrderBackwardOrderValue;

    /**
     * 临近用车订单里程价值系数
     */
    private String imminentOrderValueCoefficient;

    @JsonCreator
    public OrderMileageConfigValueVO(@JsonProperty("startKilo") String startKilo, @JsonProperty("endKilo") String endKilo, @JsonProperty("startTime") String startTime, @JsonProperty("endTime") String endTime, @JsonProperty("value") String value, @JsonProperty("crossCityOrderBackwardOrderValue") String crossCityOrderBackwardOrderValue, @JsonProperty("imminentOrderValueCoefficient") String imminentOrderValueCoefficient) {
        this.startKilo = startKilo;
        this.endKilo = endKilo;
        this.startTime = startTime;
        this.endTime = endTime;
        this.value = value;
        this.crossCityOrderBackwardOrderValue = crossCityOrderBackwardOrderValue;
        this.imminentOrderValueCoefficient = imminentOrderValueCoefficient;
    }
}
