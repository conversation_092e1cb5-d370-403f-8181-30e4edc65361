package com.ctrip.dcs.dsp.delay.service.impl;

import com.ctrip.dcs.dsp.delay.carconfig.CarConfig;
import com.ctrip.dcs.dsp.delay.carconfig.CarConfigCode;
import com.ctrip.dcs.dsp.delay.carconfig.Key;
import com.ctrip.dcs.dsp.delay.carconfig.key.DelayDspSubSkuKey;
import com.ctrip.dcs.dsp.delay.carconfig.value.DelayDspSubSkuValue;
import com.ctrip.dcs.dsp.delay.gateway.CarConfigGateway;
import com.ctrip.dcs.dsp.delay.model.SupplyOrder;
import com.ctrip.dcs.dsp.delay.service.DispatchConfigService;
import org.apache.commons.collections.CollectionUtils;
import org.codehaus.jackson.type.TypeReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 */
@Component
public class DispatchConfigServiceImpl implements DispatchConfigService {

    @Autowired
    private CarConfigGateway carConfigGateway;

    @Override
    public DelayDspSubSkuValue matchDelayDspSubSkuConfig(SupplyOrder order) {
        return matchDelayDspSubSkuConfig(order.getCityCode());
    }
    
    @Override
    public DelayDspSubSkuValue matchDelayDspSubSkuConfig(String cityCode) {
        CarConfig carConfig = carConfigGateway.query(CarConfigCode.DELAY_DSP_SUB_SKU.getCode(), CarConfigCode.DELAY_DSP_SUB_SKU.getVersion());
        List<DelayDspSubSkuValue> list = carConfig.values(
                new TypeReference<DelayDspSubSkuValue>(){},
                new DelayDspSubSkuKey(cityCode),
                new DelayDspSubSkuKey(Key.ALL_CITY)
        );
        return CollectionUtils.isEmpty(list) ? DelayDspSubSkuValue.DEFAULT : list.get(0);
    }
}
