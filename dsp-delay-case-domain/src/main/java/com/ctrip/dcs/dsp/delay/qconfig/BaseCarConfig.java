package com.ctrip.dcs.dsp.delay.qconfig;

import com.ctrip.dcs.dsp.delay.qconfig.key.CarConfigKeyVO;
import com.ctrip.dcs.dsp.delay.qconfig.value.CarConfigValueVO;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Lists;
import lombok.Getter;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 */
public abstract class BaseCarConfig<K extends CarConfigKeyVO, V extends CarConfigValueVO> {

    public V get(K key) {
        Config<K, V> config = getContext().get(key.toKey());
        return Optional.ofNullable(config).map(Config::getValues).orElse(Collections.emptyList()).stream().findFirst().orElse(null);
    }


    public List<V> getList(K key) {
        Config<K, V> config = getContext().get(key.toKey());
        return Optional.ofNullable(config).map(Config::getValues).orElse(Collections.emptyList());
    }

    public List<V> get(List<K> keys) {
        List<V> result = Lists.newArrayList();
        for (K key : keys) {
            Config<K, V> config = getContext().get(key.toKey());
            List<V> list = Optional.ofNullable(config).map(Config::getValues).orElse(Collections.emptyList());
            result.addAll(list);
        }

        return result;
    }

    abstract Map<String, Config<K, V>> getContext();

    @Getter
    public static class Config<K extends CarConfigKeyVO, V extends CarConfigValueVO> {

        private K params;

        private List<V> values;

        @JsonCreator
        public Config(@JsonProperty("params") K params, @JsonProperty("values") List<V> values) {
            this.params = params;
            this.values = values;
        }
    }
}
