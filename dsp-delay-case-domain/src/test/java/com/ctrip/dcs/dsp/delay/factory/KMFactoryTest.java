package com.ctrip.dcs.dsp.delay.factory;

import com.ctrip.dcs.dsp.delay.core.KM;
import com.ctrip.dcs.dsp.delay.model.DelayDspOrder;
import com.ctrip.dcs.dsp.delay.model.Driver;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Table;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.modules.junit4.PowerMockRunner;

/**
 * <AUTHOR>
 */
@RunWith(PowerMockRunner.class)
public class KMFactoryTest {

    @InjectMocks
    private KMFactory factory;

    @Mock
    private DelayDspOrder delayDspOrder;

    @Mock
    private Driver driver;

    @Test
    public void test() {
        Table<Long, String, Double> table = HashBasedTable.create();
        table.put(1L, "1", 100D);
        PowerMockito.when(delayDspOrder.getOrderId()).thenReturn("1");
        PowerMockito.when(driver.getDriverId()).thenReturn("1");
        KM km = factory.create(ImmutableMap.of("1", delayDspOrder), ImmutableMap.of(1L, driver), table);
        int[] result = km.execute();
        Assert.assertEquals(result[0], 0);
    }
}
