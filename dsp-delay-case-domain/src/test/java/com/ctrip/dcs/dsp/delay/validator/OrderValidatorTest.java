package com.ctrip.dcs.dsp.delay.validator;

import com.ctrip.dcs.dsp.delay.enums.BizAreaTypeEnum;
import com.ctrip.dcs.dsp.delay.enums.OrderSysTypeEnum;
import com.ctrip.dcs.dsp.delay.model.SupplyOrder;
import com.ctrip.dcs.dsp.delay.repository.DelayDspOrderRepository;
import com.ctrip.dcs.dsp.delay.validator.impl.OrderValidator;
import com.google.common.collect.Sets;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.modules.junit4.PowerMockRunner;

/**
 * <AUTHOR>
 */
@RunWith(PowerMockRunner.class)
public class OrderValidatorTest {

    @InjectMocks
    private OrderValidator orderValidator;

    @Mock
    private DelayDspOrderRepository delayDspOrderRepository;

    @Test
    public void testOrderStatus() {
        SupplyOrder order = new SupplyOrder();
        order.setIsOtaBookOrder(1);
        order.setOrderStatus(8);
        ValidatorDTO code = orderValidator.validate(order);
        Assert.assertEquals(ValidatorCode.ILLEGAL_STATUS, code.getCode());
    }

    @Test
    public void testOTA() {
        SupplyOrder order = new SupplyOrder();
        order.setIsOtaBookOrder(1);
        order.setOrderStatus(2);
        ValidatorDTO code = orderValidator.validate(order);
        Assert.assertEquals(ValidatorCode.OTA, code.getCode());
    }

    @Test
    public void testNotJNT() {
        SupplyOrder order = new SupplyOrder();
        order.setIsOtaBookOrder(0);
        order.setOrderStatus(2);
        order.setCategoryCode("day_rent");
        ValidatorDTO code = orderValidator.validate(order);
        Assert.assertEquals(ValidatorCode.NOT_JNT, code.getCode());
    }

    @Test
    public void test() {
        PowerMockito.when(delayDspOrderRepository.queryByMainOrderId(Mockito.any())).thenReturn(null);
        SupplyOrder order = new SupplyOrder();
        order.setOrderStatus(2);
        order.setIsOtaBookOrder(0);
        order.setCategoryCode("airport_pickup");
        ValidatorDTO code = orderValidator.validate(order);
        Assert.assertEquals(ValidatorCode.OK, code.getCode());
    }

    @Test
    public void testTaken() {
        PowerMockito.when(delayDspOrderRepository.queryByMainOrderId(Mockito.any())).thenReturn(null);
        SupplyOrder order = new SupplyOrder();
        order.setOrderStatus(3);
        order.setIsOtaBookOrder(0);
        order.setCategoryCode("airport_pickup");
        order.setDriverId("123");
        ValidatorDTO code = orderValidator.validate(order);
        Assert.assertEquals(ValidatorCode.OK, code.getCode());
    }

    @Test
    public void testIGT() {
        PowerMockito.when(delayDspOrderRepository.queryByMainOrderId(Mockito.any())).thenReturn(null);
        SupplyOrder order = new SupplyOrder();
        order.setOrderStatus(3);
        order.setIsOtaBookOrder(0);
        order.setCategoryCode("airport_pickup");
        order.setDriverId("123");
        order.setBizAreaType(BizAreaTypeEnum.IGT.getQunarCode());
        ValidatorDTO code = orderValidator.validate(order);
        Assert.assertEquals(ValidatorCode.IGT, code.getCode());
    }

    @Test
    public void testT() {
        PowerMockito.when(delayDspOrderRepository.queryByMainOrderId(Mockito.any())).thenReturn(null);
        SupplyOrder order = new SupplyOrder();
        order.setOrderStatus(2);
        order.setIsOtaBookOrder(0);
        order.setCategoryCode("airport_pickup");
        order.setDriverId("123");
        order.setBizAreaType(BizAreaTypeEnum.CHF.getQunarCode());
        order.setOrderSysType(OrderSysTypeEnum.T.getCode());
        ValidatorDTO code = orderValidator.validate(order);
        Assert.assertEquals(ValidatorCode.T, code.getCode());
    }

    @Test
    public void testVip() {
        SupplyOrder order = new SupplyOrder();
        order.setOrderStatus(2);
        order.setIsOtaBookOrder(0);
        order.setCategoryCode("airport_pickup");
        order.setDriverId("123");
        order.setBizAreaType(BizAreaTypeEnum.CHF.getQunarCode());
        order.setOrderSysType(OrderSysTypeEnum.C.getCode());
        order.setIsVipUserLevel(1);
        ValidatorDTO code = orderValidator.validate(order);
        Assert.assertEquals(ValidatorCode.VIP, code.getCode());

        order.setIsVipUserLevel(0);
        ValidatorDTO code1 = orderValidator.validate(order);
        Assert.assertEquals(ValidatorCode.OK, code1.getCode());
    }
}
