package com.ctrip.dcs.dsp.delay.interfaces.executor;

import com.ctrip.dcs.dsp.delay.service.DispatcherDriverService;
import com.ctrip.dcs.dsp.delay.service.interfaces.message.QueryIsDriverMatchingRequestType;
import com.ctrip.dcs.dsp.delay.service.interfaces.message.QueryIsDriverMatchingResponseType;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.mockito.Mockito.*;

/**
 * <AUTHOR>
 */
public class QueryIsDriverMatchingExecutorTest {
    @Mock
    DispatcherDriverService dispatcherDriverService;
    @InjectMocks
    QueryIsDriverMatchingExecutor queryIsDriverMatchingExecutor;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testExecute() throws Exception {
        when(dispatcherDriverService.isMatch(anyString(), anyString())).thenReturn(Boolean.TRUE);
        QueryIsDriverMatchingRequestType requestType = new QueryIsDriverMatchingRequestType();
        requestType.setDriverId("driverId");
        requestType.setSysExpectBookTime("2023-11-23 12:00:00");
        QueryIsDriverMatchingResponseType responseType = queryIsDriverMatchingExecutor.execute(requestType);
        Assert.assertEquals("driverId", responseType.getDriverId());
        Assert.assertEquals(1, responseType.getIsMatching().intValue());
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme