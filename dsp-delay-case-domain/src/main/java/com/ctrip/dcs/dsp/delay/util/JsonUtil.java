package com.ctrip.dcs.dsp.delay.util;


import com.ctrip.igt.framework.common.exception.BizException;
import org.codehaus.jackson.JsonNode;
import org.codehaus.jackson.map.DeserializationConfig;
import org.codehaus.jackson.map.ObjectMapper;
import org.codehaus.jackson.map.SerializationConfig;
import org.codehaus.jackson.map.annotate.JsonSerialize;
import org.codehaus.jackson.type.TypeReference;

import java.io.IOException;
import java.text.SimpleDateFormat;

public final class JsonUtil {

    private static final String DATE_FORMAT = "yyyy-MM-dd HH:mm:ss";
    /** 对象映射  */
    private static final ObjectMapper objMapper = new ObjectMapper();

    static{
        objMapper.configure(DeserializationConfig.Feature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        objMapper.configure(SerializationConfig.Feature.WRITE_DATES_AS_TIMESTAMPS, false);
        objMapper.setDateFormat(new SimpleDateFormat(DATE_FORMAT));
        objMapper.setSerializationInclusion(JsonSerialize.Inclusion.NON_NULL);
        objMapper.configure(SerializationConfig.Feature.FAIL_ON_EMPTY_BEANS, false);
    }

    public static String toJson(Object obj) {
        String rst = null;
        if(obj == null || obj instanceof String){
            return (String)obj;
        }
        try {
            rst = objMapper.writeValueAsString(obj);
        } catch (Exception e) {
            throw new BizException("json format error！",e);
        }
        return rst;
    }

    public static <T> T fromJson(String json, TypeReference<T> typeReference) {
        T rst = null;
        try {
            rst = (T)objMapper.readValue(json, typeReference);
        } catch (Exception e) {
            throw new BizException("json parse error！",e);
        }
        return rst;
    }

    public static <T> T fromJson(JsonNode json, TypeReference<T> typeReference) {
        T rst = null;
        try {
            rst = (T)objMapper.readValue(json, typeReference);
        } catch (Exception e) {
            throw new BizException("json parse error！",e);
        }
        return rst;
    }

    public static JsonNode path(String json, String key) {
        try {
            JsonNode jsonNode = objMapper.readTree(json);
            return jsonNode.path(key);
        } catch (IOException e) {
            throw new BizException("json path error！",e);
        }
    }
}
