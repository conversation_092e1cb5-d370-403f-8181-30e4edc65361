package com.ctrip.dcs.dsp.delay.interfaces.executor;

import com.ctrip.dcs.dsp.application.service.DelayDspOrderApplicationService;
import com.ctrip.dcs.dsp.delay.service.interfaces.message.CheckSpecialDateRequestType;
import com.ctrip.dcs.dsp.delay.service.interfaces.message.CheckSpecialDateResponseType;
import com.ctrip.dcs.dsp.delay.util.JsonUtil;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.exception.BizException;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.annontation.ServiceLogTagPair;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024/8/15 20:13
 */

@Component
@ServiceLogTagPair(key = "cityId")
@ServiceLogTagPair(key = "carTypeId")
public class CheckSpecialDateExecutor extends AbstractRpcExecutor<CheckSpecialDateRequestType, CheckSpecialDateResponseType> implements Validator<CheckSpecialDateRequestType> {
    
    private static Logger logger = LoggerFactory.getLogger(CheckSpecialDateExecutor.class);
    @Resource
    private DelayDspOrderApplicationService delayDspApplicationService;
    
    @Override
    public CheckSpecialDateResponseType execute(CheckSpecialDateRequestType requestType) {
        try {
            Boolean isHit = delayDspApplicationService.checkSpecialDate(requestType.getCityId(), requestType.getCarTypeId(), requestType.getSysExpectBookTime());
            CheckSpecialDateResponseType responseType = new CheckSpecialDateResponseType();
            responseType.setHit(isHit);
            return ServiceResponseUtils.success(responseType);
        } catch (Exception ex) {
            logger.error("check_special_date_error", "check special date is exception. requestType:" + JsonUtil.toJson(requestType), ex, Maps.newHashMap());
            //兜底不走延后派
            CheckSpecialDateResponseType responseType = new CheckSpecialDateResponseType();
            responseType.setHit(Boolean.TRUE);
            return ServiceResponseUtils.success(responseType);
        }
    }
    
    @Override
    public void validate(AbstractValidator<CheckSpecialDateRequestType> validator, CheckSpecialDateRequestType req) {
        if (Objects.isNull(req.getCityId())) {
            throw new BizException("city is null");
        }
        if (Objects.isNull(req.getCarTypeId())) {
            throw new BizException("carTypeId is null");
        }
        if (StringUtils.isBlank(req.getSysExpectBookTime())) {
            throw new BizException("sysExpectBookTime is null");
        }
    }
}
