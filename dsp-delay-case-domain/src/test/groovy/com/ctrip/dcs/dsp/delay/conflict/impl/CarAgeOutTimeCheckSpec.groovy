package com.ctrip.dcs.dsp.delay.conflict.impl

import com.ctrip.dcs.dsp.delay.conflict.CheckCode
import com.ctrip.dcs.dsp.delay.conflict.ConflictContext
import com.ctrip.dcs.dsp.delay.gateway.GeoGateway
import com.ctrip.dcs.dsp.delay.model.DelayDspOrder
import com.ctrip.dcs.dsp.delay.model.DelayDspTask
import com.ctrip.dcs.dsp.delay.model.DispatcherConfig
import com.ctrip.dcs.dsp.delay.model.Driver
import com.ctrip.dcs.dsp.delay.model.DriverAggregation
import com.ctrip.dcs.dsp.delay.model.Route
import com.ctrip.dcs.dsp.delay.util.DateUtil
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 */
class CarAgeOutTimeCheckSpec extends Specification {

    def check = new CarAgeOutTimeCheck()

    @Unroll
    def "check"() {
        given: "Mock数据"
        def next = new DelayDspOrder(isDelay: isDelay, sysExpectBookTime: sysExpectBookTime)
        def driver = new Driver(overAgeTime: overAgeTime)
        def driverAggregation = new DriverAggregation(driver: driver)
        def context = new ConflictContext(next: next, driverAggregation: driverAggregation)
        when: "执行校验方法"
        def res = check.check(context)

        then: "验证校验结果"
        res == code

        where:
        overAgeTime           | sysExpectBookTime | isDelay || code
        "2022-08-29 12:00:00" | new Date()        | 0       || CheckCode.OK
        null                  | new Date()        | 1       || CheckCode.OK
        "2022-08-29 12:00:00" | null              | 1       || CheckCode.OK
        "2022-08-29 12:00:00" | new Date()        | 1       || CheckCode.CAR_OVER_AGE
        "2025-08-29 12:00:00" | new Date()        | 1       || CheckCode.OK
    }

    @Unroll
    def "check_null"() {

        given: "Mock数据"
        def next = null
        def driver = new Driver(overAgeTime: overAgeTime)
        def driverAggregation = new DriverAggregation(driver: driver)
        def context = new ConflictContext(next: next, driverAggregation: driverAggregation)
        when: "执行校验方法"
        def res = check.check(context)

        then: "验证校验结果"
        res == code

        where:
        overAgeTime           | sysExpectBookTime | isDelay || code
        "2022-08-29 12:00:00" | new Date()        | 0       || CheckCode.OK
    }


    @Unroll
    def "check_null1"() {

        given: "Mock数据"
        def next = new DelayDspOrder(isDelay: isDelay, sysExpectBookTime: sysExpectBookTime)
        def driver = new Driver(overAgeTime: overAgeTime)
        def driverAggregation = new DriverAggregation(driver: driver)
        def context = new ConflictContext(next: next, driverAggregation: driverAggregation)
        when: "执行校验方法"
        def res = check.check(context)

        then: "验证校验结果"
        res == code

        where:
        overAgeTime           | sysExpectBookTime | isDelay || code
        "2022-08-29 12:00:00" | new Date()        | null       || CheckCode.OK
    }
}
