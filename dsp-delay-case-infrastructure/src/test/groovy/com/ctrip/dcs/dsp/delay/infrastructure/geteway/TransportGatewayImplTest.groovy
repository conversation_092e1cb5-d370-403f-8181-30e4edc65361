package com.ctrip.dcs.dsp.delay.infrastructure.geteway

import com.ctrip.dcs.dsp.delay.carconfig.CarConfig
import com.ctrip.dcs.dsp.delay.carconfig.CarConfigCode
import com.ctrip.dcs.dsp.delay.infrastructure.soa.TmsTransportServiceProxy
import com.ctrip.dcs.dsp.delay.model.SupplyOrder
import com.ctrip.dcs.dsp.delay.model.TransportGroupInfo
import com.ctrip.dcs.tms.transport.api.model.QueryTransportGroupsSOAResponseType
import com.ctrip.dcs.tms.transport.api.model.TransportGroupDetailSOAType
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 */
class TransportGatewayImplTest extends Specification {

    TmsTransportServiceProxy transportServiceProxy = Mock(TmsTransportServiceProxy)

    TransportGatewayImpl transportGateway = new TransportGatewayImpl(transportServiceProxy: transportServiceProxy)

    @Unroll
    def "queryTransportGroups"() {

        given: "Mock数据"
        SupplyOrder supplyOrder = new SupplyOrder(skuId: 1L, categoryCode: categoryCode)
        transportServiceProxy.queryTransportGroups(_) >> new QueryTransportGroupsSOAResponseType(data: [new TransportGroupDetailSOAType(transportGroupId: 1, transportGroupName: "test", transportGroupMode: 1001, supplierId: 2L)])

        when: "执行校验方法"
        List<TransportGroupInfo> res = transportGateway.queryTransportGroups(supplyOrder)

        then: "验证校验结果"
        res.size() == size
        with(res.get(0)) {
            transportGroupId == 1
            transportGroupName == "test"
            transportGroupMode == 1001
            supplierId == 2L
        }

        where:
        categoryCode      || size
        "airport_pickup"  || 1
        "airport_dropoff" || 1
    }

}
