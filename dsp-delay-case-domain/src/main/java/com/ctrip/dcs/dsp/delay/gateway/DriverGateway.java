package com.ctrip.dcs.dsp.delay.gateway;

import com.ctrip.dcs.dsp.delay.model.Driver;
import com.ctrip.dcs.dsp.delay.model.DriverLeave;
import com.ctrip.dcs.dsp.delay.model.DriverScore;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
public interface DriverGateway {

    Driver query(String driverId);

    List<Driver> query(Set<String> driverIds);

    List<Driver> query(Integer cityId, Integer carTypeId,String taskType);

    List<Driver> query(List<Long> transportGroupIds);

    List<DriverLeave> queryLeave(List<String> driverIds);

    List<DriverScore> queryDriverScore(Set<String> driverIds);

}
