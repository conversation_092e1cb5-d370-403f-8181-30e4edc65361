package com.ctrip.dcs.dsp.delay.infrastructure.trocks;

import com.ctrip.dcs.dsp.delay.infrastructure.util.TokenUtil;
import org.junit.Assert;
import org.junit.Test;

/**
 * 单侧
 */
public class TokenUtilTest {

    @Test
    public void testGenerateToken() {
        String token = TokenUtil.generateToken("123456789");
        Assert.assertTrue("488e6d9f58365585fd4383714452cca7".equals(token));
    }

    @Test
    public void testGenerateTokenParamEmpty() {
        String token = TokenUtil.generateToken("");
        Assert.assertTrue("".equals(token));
    }

    @Test
    public void testGenerateTokenMain() {
        TokenUtil.main(null);
        Assert.assertTrue(true);
    }

}
