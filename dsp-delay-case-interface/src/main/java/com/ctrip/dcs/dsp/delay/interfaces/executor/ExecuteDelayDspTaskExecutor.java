package com.ctrip.dcs.dsp.delay.interfaces.executor;

import com.ctrip.dcs.dsp.delay.consts.CommonConstant;
import com.ctrip.dcs.dsp.delay.enums.DelayDspTaskStatus;
import com.ctrip.dcs.dsp.delay.model.DelayDspTask;
import com.ctrip.dcs.dsp.delay.mq.MessageProducer;
import com.ctrip.dcs.dsp.delay.repository.DelayDspTaskRepository;
import com.ctrip.dcs.dsp.delay.service.interfaces.message.ExecuteDelayDspTaskRequestType;
import com.ctrip.dcs.dsp.delay.service.interfaces.message.ExecuteDelayDspTaskResponseType;
import com.ctrip.dcs.dsp.delay.util.MetricsUtil;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.annontation.ServiceLogTag;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Component
@ServiceLogTag(tagKeys = {"taskId"})
public class ExecuteDelayDspTaskExecutor extends AbstractRpcExecutor<ExecuteDelayDspTaskRequestType, ExecuteDelayDspTaskResponseType> implements Validator<ExecuteDelayDspTaskRequestType> {

    @Autowired
    private DelayDspTaskRepository delayDspTaskRepository;

    @Autowired
    private MessageProducer messageProducer;

    @Override
    public ExecuteDelayDspTaskResponseType execute(ExecuteDelayDspTaskRequestType requestType) {
        DelayDspTask task = delayDspTaskRepository.queryByTaskId(requestType.getTaskId());
        if (Objects.isNull(task)) {
            return ServiceResponseUtils.fail(new ExecuteDelayDspTaskResponseType());
        }
        if (!Objects.equals(task.getTaskStatus(), DelayDspTaskStatus.EXECUTING.getCode())) {
            delayDspTaskRepository.update(task.getTaskId(), DelayDspTaskStatus.EXECUTING.getCode());
        }
        MetricsUtil.recordValue("run.task.retry.manual", 1);
        Map<String, Object> map = Maps.newHashMap();
        map.put("taskId", task.getTaskId());
        map.put("taskType",task.getTaskType());
        messageProducer.sendMessage(CommonConstant.DELAY_DSP_TASK_RUN_SUBJECT, map);
        return ServiceResponseUtils.success(new ExecuteDelayDspTaskResponseType());
    }

    @Override
    public void validate(AbstractValidator<ExecuteDelayDspTaskRequestType> validator) {
        validator.ruleFor("taskId").notNull().notEmpty();
    }
}
