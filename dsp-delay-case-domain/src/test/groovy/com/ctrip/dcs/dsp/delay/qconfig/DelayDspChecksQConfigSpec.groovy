package com.ctrip.dcs.dsp.delay.qconfig

import com.ctrip.dcs.dsp.delay.qconfig.key.DelayDspCheckKeyVO
import com.ctrip.dcs.dsp.delay.qconfig.value.DelayDspCheckValueVO
import com.google.common.collect.Lists
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 */
class DelayDspChecksQConfigSpec extends Specification {

    def map = new HashMap();

    def config = new DelayDspChecksQConfig(context: map)

    @Unroll
    def "test"() {
        given: "Mock数据"
        DelayDspCheckKeyVO keyVO = new DelayDspCheckKeyVO(cityId)
        DelayDspCheckValueVO valueVO = new DelayDspCheckValueVO("headOrderCheck,prevMileageCheck",
                "serviceTimeCheck,emptyDurationCheck,prevMileageCheck",
                "tailOrderCheck")
        map.put(keyVO.toKey(), new BaseCarConfig.Config(keyVO, Lists.newArrayList(valueVO)))

        when: "执行校验方法"
        def result = config.getCheckValueVO(1)

        then: "验证校验结果"
        result.getDriverToOrderCheckList().size() > 0 == res

        where:
        cityId || res
        1      || true
        2      || true
        0      || true
    }
}
