package com.ctrip.dcs.dsp.delay.carconfig.key;

import com.ctrip.dcs.dsp.delay.carconfig.Key;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class DelayDspTaskKeyV2 implements Key {

    private Integer cityId;

    @Override
    public String toKey() {
        return cityId.toString();
    }
}
