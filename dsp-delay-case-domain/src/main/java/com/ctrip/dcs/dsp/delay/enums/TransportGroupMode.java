package com.ctrip.dcs.dsp.delay.enums;

import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 */
@Getter
public enum  TransportGroupMode {

    FULL_TIME_ASSIGN(1001),
    PART_TIME_BROADCAST(1002),
    MANUAL_DISPATCH(1003),
    VIP_MANUAL_DISPATCH(1004),
    DEFAULT_MANUAL_DISPATCH(1005),
    REGISTER_DISPATCH(1006),
    ;
    private int code;

    TransportGroupMode(int code) {
        this.code = code;
    }

    public static boolean isFullTimeOrRegisterMode(Integer transportGroupMode) {
        return Objects.equals(transportGroupMode, FULL_TIME_ASSIGN.getCode()) || Objects.equals(transportGroupMode, REGISTER_DISPATCH.getCode());
    }

    public static boolean isRegisterMode(int transportGroupMode) {
        return Objects.equals(transportGroupMode, REGISTER_DISPATCH.getCode());
    }
}
