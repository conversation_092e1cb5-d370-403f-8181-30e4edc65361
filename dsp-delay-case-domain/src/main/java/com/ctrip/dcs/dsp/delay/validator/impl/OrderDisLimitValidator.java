package com.ctrip.dcs.dsp.delay.validator.impl;

import com.ctrip.dcs.dsp.delay.carconfig.CarConfig;
import com.ctrip.dcs.dsp.delay.carconfig.CarConfigCode;
import com.ctrip.dcs.dsp.delay.carconfig.key.OrderDisLimitKey;
import com.ctrip.dcs.dsp.delay.carconfig.value.OrderDisLimitValue;
import com.ctrip.dcs.dsp.delay.gateway.CarConfigGateway;
import com.ctrip.dcs.dsp.delay.model.SupplyOrder;
import com.ctrip.dcs.dsp.delay.qconfig.DelayDspCommonQConfig;
import com.ctrip.dcs.dsp.delay.util.JsonUtil;
import com.ctrip.dcs.dsp.delay.validator.Validator;
import com.ctrip.dcs.dsp.delay.validator.ValidatorCode;
import com.ctrip.dcs.dsp.delay.validator.ValidatorDTO;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import org.codehaus.jackson.type.TypeReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 长短公里限制
 * <AUTHOR>
 */
@Component("orderDisLimitValidator")
public class OrderDisLimitValidator implements Validator {

    private static final Logger logger = LoggerFactory.getLogger(OrderDisLimitValidator.class);

    @Autowired
    private CarConfigGateway carConfigGateway;

    @Override
    public ValidatorDTO validate(SupplyOrder order) {
        CarConfig carConfig = carConfigGateway.query(CarConfigCode.ORDER_DIS_LIMIT.getCode(), CarConfigCode.ORDER_DIS_LIMIT.getVersion());

        List<OrderDisLimitValue> values = carConfig.values(
                new TypeReference<List<OrderDisLimitValue>>(){},
                new OrderDisLimitKey(order.getCityCode(), order.getCarTypeId()),
                new OrderDisLimitKey(order.getCityCode(), OrderDisLimitKey.ALL_CAR_TYPE),
                new OrderDisLimitKey(OrderDisLimitKey.ALL_CITY, order.getCarTypeId()),
                new OrderDisLimitKey(OrderDisLimitKey.ALL_CITY, OrderDisLimitKey.ALL_CAR_TYPE)
        );
        Double kiloLength = order.getPredictPriceInfo().getKiloLength();
        for (OrderDisLimitValue value : values) {
            if (value.match(kiloLength)) {
                logger.info("OrderDisLimitValidator", "order dis limit matched!, order id: {}, value: {} ", order.getOrderId(), JsonUtil.toJson(value));
                return new ValidatorDTO(ValidatorCode.ORDER_DIS_LIMIT);

            }
        }
        return new ValidatorDTO(ValidatorCode.OK);
    }

}
