package com.ctrip.dcs.dsp.delay.filter.impl;

import com.ctrip.dcs.dsp.delay.filter.Filter;
import com.ctrip.dcs.dsp.delay.model.DelayDspTask;
import com.ctrip.dcs.dsp.delay.model.Driver;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public abstract class CommonFilter implements Filter {

    private static final Logger logger = LoggerFactory.getLogger(CommonFilter.class);

    @Override
    public List<Driver> doFilter(List<Driver> list, DelayDspTask task) {
        return list.stream().filter(d -> doFilter(d, task)).collect(Collectors.toList());
    }

    private boolean doFilter(Driver driver, DelayDspTask task) {
        if (!filter(driver, task)) {
            logger.info("filter", "driver id: {}, filter reason: {}", driver.getDriverId(), reason());
            return false;
        }
        return true;
    }

    abstract String reason();

    abstract boolean filter(Driver driver, DelayDspTask task);


}
