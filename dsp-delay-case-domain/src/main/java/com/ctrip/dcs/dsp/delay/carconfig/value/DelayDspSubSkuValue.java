package com.ctrip.dcs.dsp.delay.carconfig.value;

import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.google.common.base.Splitter;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class DelayDspSubSkuValue {

    private static final Logger logger = LoggerFactory.getLogger(DelayDspSubSkuValue.class);

    public static final DelayDspSubSkuValue DEFAULT = new DelayDspSubSkuValue(8888, "8889");

    /**
     * 延后派入池子产品id
     */
    private Integer inSubSkuId;

    /**
     * 延后派出池子产品id，使用逗号分隔
     */
    private String outSubSkuIds;

    public List<Integer> getOutSubSkuIdList() {
        try {
            List<String> skuIds = Splitter.on(",").omitEmptyStrings().trimResults().splitToList(this.outSubSkuIds);
            return skuIds.stream().map(Integer::valueOf).collect(Collectors.toList());
        } catch (Exception e) {
            logger.error("parse outSubSkuIds error!", "outSubSkuIds:{}, e:{}", outSubSkuIds, e);
        }
        return DEFAULT.getOutSubSkuIdList();
    }
}
