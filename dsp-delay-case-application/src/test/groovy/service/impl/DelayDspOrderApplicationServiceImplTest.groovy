package service.impl

import com.ctrip.dcs.dsp.application.service.impl.DelayDspOrderApplicationServiceImpl
import com.ctrip.dcs.dsp.delay.cache.DistributedCache
import com.ctrip.dcs.dsp.delay.carconfig.SpecialDateConfig
import com.ctrip.dcs.dsp.delay.gateway.DriverGateway
import com.ctrip.dcs.dsp.delay.gateway.SelfDispatcherOrderGateway
import com.ctrip.dcs.dsp.delay.model.DelayDspTask
import com.ctrip.dcs.dsp.delay.mq.MessageProducer
import com.ctrip.dcs.dsp.delay.qconfig.DelayDspCommonQConfig
import com.ctrip.dcs.dsp.delay.repository.DelayDspOrderRepository
import com.ctrip.dcs.dsp.delay.repository.DelayDspTaskRepository
import com.ctrip.dcs.dsp.delay.service.DelayDspOrderService
import com.ctrip.dcs.dsp.delay.service.DelayTaskService
import com.ctrip.dcs.dsp.delay.service.DispatchConfigService
import com.ctrip.dcs.dsp.delay.validator.ValidatorChain
import org.junit.Assert
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 * @since 2024/8/20 11:50
 */
class DelayDspOrderApplicationServiceImplTest extends Specification {
    def testObj = new DelayDspOrderApplicationServiceImpl()
    def dispatchConfigService = Mock(DispatchConfigService)
    def delayDspOrderRepository = Mock(DelayDspOrderRepository)
    def delayDspTaskRepository = Mock(DelayDspTaskRepository)
    def messageProducer = Mock(MessageProducer)
    def distributedCache = Mock(DistributedCache)
    def delayDspCommonQConfig = Mock(DelayDspCommonQConfig)
    def validatorChain = Mock(ValidatorChain)
    def delayTaskService = Mock(DelayTaskService)
    def driverGateway = Mock(DriverGateway)
    def selfDispatcherOrderGateway = Mock(SelfDispatcherOrderGateway)
    def delayDspOrderService = Mock(DelayDspOrderService)
    def specialDateConfig = Mock(SpecialDateConfig)

    def setup() {
        testObj.delayTaskService = delayTaskService
        testObj.delayDspTaskRepository = delayDspTaskRepository
        testObj.delayDspOrderService = delayDspOrderService
        testObj.delayDspOrderRepository = delayDspOrderRepository
        testObj.messageProducer = messageProducer
        testObj.validatorChain = validatorChain
        testObj.delayDspCommonQConfig = delayDspCommonQConfig
        testObj.driverGateway = driverGateway
        testObj.selfDispatcherOrderGateway = selfDispatcherOrderGateway
        testObj.distributedCache = distributedCache
        testObj.dispatchConfigService = dispatchConfigService
    }

    @Unroll
    def "test checkSpecialDate"() {
        given:
        delayDspOrderService.checkSpecialDate(_,_,_) >> specialDateValueResult

        when:
        def result = testObj.checkSpecialDate(11, 124, "2024-08-20 11:00:00");

        then:
        Objects.equals(result, expectedResult);

        where:
        specialDateValueResult || expectedResult
        Boolean.FALSE          || Boolean.FALSE
        Boolean.TRUE           || Boolean.TRUE
    }

    def "test updateTaskInfo"() {
        given:

        when:
        def updateRes = delayTaskService.updateTaskInfo(new DelayDspTask())
        then:
        Assert.assertTrue(Objects.isNull(updateRes));
    }
}
