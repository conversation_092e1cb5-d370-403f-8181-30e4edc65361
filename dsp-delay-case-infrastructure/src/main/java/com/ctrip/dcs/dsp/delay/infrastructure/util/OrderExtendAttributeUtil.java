package com.ctrip.dcs.dsp.delay.infrastructure.util;

import com.ctrip.dcs.self.order.query.dto.OrderExtendAttributeDTO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
public class OrderExtendAttributeUtil {

    public static String getAttributeValue(List<OrderExtendAttributeDTO> attributes, String code, String defaultValue) {
        if (CollectionUtils.isEmpty(attributes) || StringUtils.isBlank(code)) {
            return defaultValue;
        }
        return attributes.stream()
                .filter(attribute -> Objects.equals(code, attribute.getAttributeCode()))
                .map(OrderExtendAttributeDTO::getAttributeValue)
                .findFirst()
                .orElse(defaultValue);
    }
}
