package com.ctrip.dcs.dsp.delay.validator.impl;

import com.ctrip.dcs.dsp.delay.model.DelayDspTask;
import com.ctrip.dcs.dsp.delay.model.Driver;
import com.ctrip.dcs.dsp.delay.model.DriverAggregation;
import com.ctrip.dcs.dsp.delay.model.SupplyOrder;
import com.ctrip.dcs.dsp.delay.qconfig.InventoryValidatorCityValueConfig;
import com.ctrip.dcs.dsp.delay.service.DelayTaskService;
import com.ctrip.dcs.dsp.delay.service.DriverInventoryService;
import com.ctrip.dcs.dsp.delay.service.DriverService;
import com.ctrip.dcs.dsp.delay.util.DateUtil;
import com.ctrip.dcs.dsp.delay.util.JsonUtil;
import com.ctrip.dcs.dsp.delay.validator.Validator;
import com.ctrip.dcs.dsp.delay.validator.ValidatorCode;
import com.ctrip.dcs.dsp.delay.validator.ValidatorDTO;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 运力校验
 * <AUTHOR>
 */
@Component("inventoryValidator")
public class InventoryValidator implements Validator {

    private static final Logger logger = LoggerFactory.getLogger(InventoryValidator.class);

    @Autowired
    private DelayTaskService delayTaskService;
    @Autowired
    private DriverService driverService;
    @Autowired
    private DriverInventoryService driverInventoryService;
    @Autowired
    private InventoryValidatorCityValueConfig inventoryValidatorCityValueConfig;

    @Override
    public ValidatorDTO validate(SupplyOrder order) {
        logger.info("InventoryValidator_SupplyOrder", JsonUtil.toJson(order));
        boolean value = inventoryValidatorCityValueConfig.getValue(order.getCityId());
        logger.info("InventoryValidator_inventoryValidatorCityWitch", JsonUtil.toJson(value));
        if(!value){
            return new ValidatorDTO(ValidatorCode.OK);
        }
        DelayDspTask task = delayTaskService.buildTaskTemp(order);
        if (Objects.isNull(task)) {
            return new ValidatorDTO(ValidatorCode.TASK_NOT_EXIST);
        }
        List<DriverAggregation> driverAggregations = driverService.queryDriverAggregationForInsert(task);
        driverAggregations = driverAggregations.stream().filter(x -> {
            Driver driver = x.getDriver();
            if(Objects.nonNull(driver)){
                String driverId = driver.getDriverId();
                Date overAgeTime = DateUtil.parseDate(driver.getOverAgeTime(), DateUtil.DATE_FMT);
                Date sysExpectBookTime = order.getSysExpectBookTime();
                return StringUtils.isNotBlank(driverId) && !driverId.equals("0") && (Objects.isNull(overAgeTime) || Objects.isNull(sysExpectBookTime) || overAgeTime.after(sysExpectBookTime));
            }
            return false;
        }).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(driverAggregations)) {
            return new ValidatorDTO(ValidatorCode.NO_DRIVER);
        }
        List<String> driverIdList = driverAggregations.stream().map(DriverAggregation::getDriver).map(Driver::getDriverId).collect(Collectors.toList());
        String usableDriverId = driverInventoryService.checkInventoryConflict(driverIdList, order);
        logger.info("InventoryValidator_checkInventoryConflict", JsonUtil.toJson(usableDriverId));
        if(StringUtils.isBlank(usableDriverId)){
            return new ValidatorDTO(ValidatorCode.NO_DRIVER);
        }
        DriverAggregation driverAggregation = driverAggregations.stream().filter(x -> x.getDriver().getDriverId().equals(usableDriverId)).findFirst().orElse(null);
        if(Objects.isNull(driverAggregation)){
            //理论上这里不会走到
            return new ValidatorDTO(ValidatorCode.NO_DRIVER);
        }
        return new ValidatorDTO(ValidatorCode.OK,driverAggregation);
    }
}
