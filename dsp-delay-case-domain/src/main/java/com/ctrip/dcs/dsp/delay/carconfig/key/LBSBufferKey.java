package com.ctrip.dcs.dsp.delay.carconfig.key;

import com.ctrip.dcs.dsp.delay.carconfig.Key;
import com.ctrip.dcs.dsp.delay.consts.CommonConstant;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class LBSBuffer<PERSON>ey implements Key {

    private String cityCode;
    private int isWorkDay = 0;
    private int ifOutCity = 0;

    public LBSBufferKey(String cityCode) {
        this.cityCode = cityCode;
    }

    @Override
    public String toKey() {
        return cityCode + CommonConstant.PLACEHOLDER + isWorkDay + CommonConstant.PLACEHOLDER + ifOutCity;
    }
}
