package com.ctrip.dcs.dsp.delay.validator.impl;

import com.ctrip.dcs.dsp.delay.gateway.SelfDispatcherOrderGateway;
import com.ctrip.dcs.dsp.delay.model.SupplyOrder;
import com.ctrip.dcs.dsp.delay.validator.Validator;
import com.ctrip.dcs.dsp.delay.validator.ValidatorCode;
import com.ctrip.dcs.dsp.delay.validator.ValidatorDTO;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 订单改派拦截器
 * <AUTHOR>
 */
@Component("orderRedispatchValidator")
public class OrderRedispatchValidator implements Validator {

    private static final Logger logger = LoggerFactory.getLogger(OrderRedispatchValidator.class);

    @Autowired
    private SelfDispatcherOrderGateway selfDispatcherOrderGateway;

    @Override
    public ValidatorDTO validate(SupplyOrder order) {
        // 兜底逻辑
        Boolean isRedispatch = true;
        try {
            isRedispatch = selfDispatcherOrderGateway.isRedispatch(order.getSourceOrderId());
        } catch (Exception e) {
            logger.error("OrderRedispatchValidator", "order id" + order.getSourceOrderId(), e);
        }
        logger.info("OrderRedispatchValidator", "orderId: {}, isRedispatch: {}",  order.getSourceOrderId(), isRedispatch);
        return isRedispatch ? new ValidatorDTO(ValidatorCode.REDISPATCH) : new ValidatorDTO(ValidatorCode.OK);
    }
}
