package com.ctrip.dcs.dsp.delay.infrastructure.soa;

import com.ctrip.dcs.self.transport.inventory.api.SelfTransportInventoryServiceClient;
import com.ctrip.dcs.self.transport.inventory.api.UseVirtualInventoryRequestType;
import com.ctrip.dcs.self.transport.inventory.api.UseVirtualInventoryResponseType;
import com.ctrip.igt.framework.soa.client.ServiceClient;

/**
 * <AUTHOR>
 */
@ServiceClient(value = SelfTransportInventoryServiceClient.class)
public interface TransportInventoryServiceProxy {

    UseVirtualInventoryResponseType useVirtualInventory(UseVirtualInventoryRequestType request);

}
