package com.ctrip.dcs.dsp.delay.limit

import com.ctrip.dcs.dsp.delay.model.DelayDspTask
import com.ctrip.dcs.dsp.delay.util.DateUtil
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 */
class OutPoolRateLimiterTest extends Specification {

    def limiter = new OutPoolRateLimiter()

    @Unroll
    def "limit"() {

        given: "Mock数据"
        def task1 = new DelayDspTask(taskId: 1, executeTime: new Date(), executeTimeDeadline: DateUtil.addHours(new Date(), 3))
        def task2 = new DelayDspTask(taskId: 1, executeTime: new Date(), executeTimeDeadline: DateUtil.addHours(new Date(), 1))

        when: "执行校验方法"
        OutPoolRateLimiter.createLimiter(null)
        def res1 = limiter.getConfirmRateLimiter(task1)
        def res2 = limiter.getConfirmRateLimiter(task2)
        def res3 = limiter.getRedispatchRateLimiter(task1)
        def res4 = limiter.getRedispatchRateLimiter(task2)

        then: "验证校验结果"
        res1 != null
        res2 != null
        res3 != null
        res4 != null

    }

}
