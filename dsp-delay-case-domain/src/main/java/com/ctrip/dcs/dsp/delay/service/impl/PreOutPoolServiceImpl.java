package com.ctrip.dcs.dsp.delay.service.impl;

import com.ctrip.dcs.dsp.delay.consts.CommonConstant;
import com.ctrip.dcs.dsp.delay.enums.OrderSource;
import com.ctrip.dcs.dsp.delay.enums.PreOutPoolResStatus;
import com.ctrip.dcs.dsp.delay.model.DelayDspOrder;
import com.ctrip.dcs.dsp.delay.model.DelayDspTask;
import com.ctrip.dcs.dsp.delay.model.PreOutPoolDTO;
import com.ctrip.dcs.dsp.delay.model.WorkBenchLogMessage;
import com.ctrip.dcs.dsp.delay.mq.MessageProducer;
import com.ctrip.dcs.dsp.delay.qconfig.DelayDspCommonQConfig;
import com.ctrip.dcs.dsp.delay.repository.DelayDspOrderRepository;
import com.ctrip.dcs.dsp.delay.repository.DelayDspTaskRepository;
import com.ctrip.dcs.dsp.delay.service.PreOutPoolService;
import com.ctrip.dcs.dsp.delay.service.RedispatchOrderService;
import com.ctrip.dcs.dsp.delay.util.JsonUtil;
import com.ctrip.dcs.dsp.delay.util.MetricsUtil;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.jackson.JacksonUtil;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Component
public class PreOutPoolServiceImpl implements PreOutPoolService {

    private static final Logger logger = LoggerFactory.getLogger(PreOutPoolServiceImpl.class);

    @Autowired
    private DelayDspTaskRepository delayDspTaskRepository;

    @Autowired
    private DelayDspOrderRepository delayDspOrderRepository;

    @Autowired
    private DelayDspCommonQConfig delayDspCommonQConfig;

    @Autowired
    private MessageProducer messageProducer;

    @Autowired
    private RedispatchOrderService redispatchOrderService;


    @Override
    public int preOutPool(PreOutPoolDTO preOutPoolDTO) {
        try{
            MetricsUtil.recordValue("pre_out_pool_sum", 1);
            DelayDspOrder delayDspOrder = delayDspOrderRepository.queryDelayOrderByMainOrderId(preOutPoolDTO.getUserOrderId());
            if(Objects.isNull(delayDspOrder)){
                MetricsUtil.recordValueWithTag("pre_out_pool", 1, "outRes", "0");
                return PreOutPoolResStatus.SUCCESS.getCode();
            }
            DelayDspTask delayDspTask = delayDspTaskRepository.queryByTaskId(delayDspOrder.getTaskId());
            if(Objects.isNull(delayDspTask) || delayDspTask.isExecuted()){
                MetricsUtil.recordValueWithTag("pre_out_pool", 1, "outRes", "0");
                return PreOutPoolResStatus.SUCCESS.getCode();
            }
            //正在定时匹配任务执行中，延后出池
            if(delayDspTask.isMatchExecuting()){
                Map<String, Object> map = Maps.newHashMap();
                map.put("userOrderId", preOutPoolDTO.getUserOrderId());
                map.put("pressedDispatchSource", preOutPoolDTO.getPressedDispatchSource());
                map.put("customerServiceUid", preOutPoolDTO.getCustomerServiceUid());
                map.put("dspOrderId", delayDspOrder.getOrderId());
                map.put("orderSource", delayDspOrder.getOrderSource());
                messageProducer.sendDelayMessage(CommonConstant.DELAY_PRE_OUT_POOL_CHECK, map, delayDspCommonQConfig.getPreOutTimeDelayCheck());
                return PreOutPoolResStatus.SUCCESS.getCode();
            }
            int res = redispatchForPreOutPool(delayDspOrder.getOrderId(), preOutPoolDTO.getUserOrderId(), preOutPoolDTO.getPressedDispatchSource(), preOutPoolDTO.getCustomerServiceUid(), delayDspOrder.getOrderSource());
            MetricsUtil.recordValueWithTag("pre_out_pool", 1, "outRes", String.valueOf(res));
            return res;
        }catch (Exception ex){
            logger.error("PreOutPoolServiceImpl_preOutPool", ex);
        }
        MetricsUtil.recordValueWithTag("pre_out_pool", 1, "outRes", "2");
        return PreOutPoolResStatus.FAILED.getCode();
    }

    @Override
    public int redispatchForPreOutPool(String dspOrderId, String userOrderId, Integer pressedDispatchSource, String customerServiceUid, String orderSource) {
        boolean ctrip = OrderSource.isCtrip(orderSource);
        if(!ctrip){
            return PreOutPoolResStatus.SUCCESS.getCode();
        }
        try{
            redispatchOrderService.redispatchForPreOutPool(dspOrderId, pressedDispatchSource);
            return PreOutPoolResStatus.SUCCESS.getCode();
        }catch (Exception ex){
            logger.error("PreOutPoolServiceImpl_redispatchForPreOutPool", ex);
            return PreOutPoolResStatus.FAILED.getCode();
        }
    }


    public void sendMessageToWorkBenchLog(String userOrderId,String contentParam) {
        try{
            Map<String, Object> map = Maps.newHashMap();
            WorkBenchLogMessage workBenchLogMessage = reDispatchSubmitWorkBenchLog(userOrderId, contentParam);
            String content = JacksonUtil.serialize(workBenchLogMessage);
            map.put("content", content);
            messageProducer.sendMessage(CommonConstant.DCS_ORDER_WORK_BENCH_LOG_CREATED, map);
        }catch (Exception ex){
            logger.error("PreOutPoolServiceImpl_sendMessageToWorkBenchLog", ex);
        }
    }

    public WorkBenchLogMessage reDispatchSubmitWorkBenchLog(String userOrderId,String contentParam) {
        WorkBenchLogMessage message = new WorkBenchLogMessage();
        message.setOrderId(Long.valueOf(userOrderId));
        message.setLogType(0);
        message.setSourceType(4);
        message.setContentType(0);
        Map<String,String> map = new HashMap<>();
        map.put("contentKey", contentParam);
        message.setContentParams(JsonUtil.toJson(map));
        message.setOperator("system");
        message.setLogTime(System.currentTimeMillis());
        message.setAppId(CommonConstant.APP_ID);
        message.setTitleKey("op.operationlog.title.expediting.driver");
        message.setContentKey("op.operationlog.content.expediting.driver");
        return message;
    }
}
