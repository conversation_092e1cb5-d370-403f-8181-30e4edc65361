package com.ctrip.dcs.dsp.delay.qconfig.key;

import com.ctrip.dcs.dsp.delay.consts.CommonConstant;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class DSPScheduleMatchKeyVO implements CarConfigKeyVO {

    private String cityId;

    private Integer carTypeId;

    @Override
    public String toKey() {
        return cityId + CommonConstant.PLACEHOLDER + carTypeId;
    }
}
