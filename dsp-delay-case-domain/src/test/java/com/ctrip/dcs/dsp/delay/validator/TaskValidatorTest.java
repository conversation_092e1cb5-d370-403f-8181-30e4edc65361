package com.ctrip.dcs.dsp.delay.validator;

import com.ctrip.dcs.dsp.delay.carconfig.DelayTaskConfig;
import com.ctrip.dcs.dsp.delay.carconfig.value.DelayDspTaskValue;
import com.ctrip.dcs.dsp.delay.enums.DelayDspTaskStatus;
import com.ctrip.dcs.dsp.delay.enums.DelayDspTaskType;
import com.ctrip.dcs.dsp.delay.enums.SupplyOrderStatus;
import com.ctrip.dcs.dsp.delay.gateway.DriverGateway;
import com.ctrip.dcs.dsp.delay.model.DelayDspTask;
import com.ctrip.dcs.dsp.delay.model.Driver;
import com.ctrip.dcs.dsp.delay.model.SupplyOrder;
import com.ctrip.dcs.dsp.delay.qconfig.DelayDspCommonQConfig;
import com.ctrip.dcs.dsp.delay.qconfig.GrayscaleQConfig;
import com.ctrip.dcs.dsp.delay.service.DelayTaskService;
import com.ctrip.dcs.dsp.delay.util.DateUtil;
import com.ctrip.dcs.dsp.delay.validator.impl.TaskValidator;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.Date;

/**
 * <AUTHOR>
 */
@RunWith(PowerMockRunner.class)
public class TaskValidatorTest {

    @InjectMocks
    private TaskValidator taskValidator;

    @Mock
    private DelayTaskService delayTaskService;

    @Mock
    private DelayTaskConfig delayTaskConfig;

    @Mock
    private DriverGateway driverGateway;

    @Mock
    private DelayDspCommonQConfig delayDspCommonQConfig;

    @Mock
    private GrayscaleQConfig grayscaleQConfig;

    @Test
    public void testNotMatch() {
        SupplyOrder order = new SupplyOrder();
        order.setCityId(1);
        order.setCarTypeId(117);
        order.setSkuId(1L);

        PowerMockito.when(delayTaskService.queryOrCreate(Mockito.any())).thenReturn(null);
        PowerMockito.when(delayTaskConfig.get(Mockito.any(), Mockito.any(), Mockito.any(),Mockito.anyInt())).thenReturn(null);
        ValidatorDTO code = taskValidator.validate(order);
        Assert.assertEquals(ValidatorCode.TASK_NOT_EXIST, code.getCode());
    }

    @Test
    public void testOK() {
        SupplyOrder order = new SupplyOrder();
        order.setCityId(1);
        order.setCarTypeId(117);
        order.setSkuId(1L);
        order.setSysExpectBookTime(DateUtil.addDays(new Date(), 1));
        order.setOrderStatus(SupplyOrderStatus.TAKEN.getCode());
        order.setDriverId("1");
        order.setShortDisOrder(0);
        DelayDspTask task = new DelayDspTask();
        task.setTaskStatus(DelayDspTaskStatus.UN_EXECUTED.getCode());
        Driver driver = new Driver();
        DelayDspTaskValue value = new DelayDspTaskValue();
        value.setBegin("00:00");
        value.setEnd("23:59");
        value.setHour(1);
        value.setDeadline(1);
        PowerMockito.when(driverGateway.query(Mockito.anyString())).thenReturn(driver);
        PowerMockito.when(delayTaskConfig.get(Mockito.any(), Mockito.any(), Mockito.any(),Mockito.anyInt())).thenReturn(value);
        ValidatorDTO code = taskValidator.validate(order);
        Assert.assertEquals(ValidatorCode.OK, code.getCode());
    }

    @Test
    public void testNullDriver() {
        SupplyOrder order = new SupplyOrder();
        order.setCityId(1);
        order.setCarTypeId(117);
        order.setSkuId(1L);
        order.setSysExpectBookTime(DateUtil.addDays(new Date(), 1));
        order.setOrderStatus(SupplyOrderStatus.TAKEN.getCode());
        order.setDriverId("1");
        DelayDspTask task = new DelayDspTask();
        task.setTaskStatus(DelayDspTaskStatus.UN_EXECUTED.getCode());
        Driver driver = new Driver();
        DelayDspTaskValue value = new DelayDspTaskValue();
        value.setBegin("00:00");
        value.setEnd("23:59");
        value.setHour(1);
        PowerMockito.when(driverGateway.query(Mockito.anyString())).thenReturn(null);
        PowerMockito.when(delayTaskConfig.get(Mockito.any(), Mockito.any(), Mockito.any(),Mockito.anyInt())).thenReturn(value);
        ValidatorDTO code = taskValidator.validate(order);
        Assert.assertEquals(ValidatorCode.TASK_NOT_EXIST, code.getCode());
    }

    @Test
    public void testTaskExecuting() {
        SupplyOrder order = new SupplyOrder();
        order.setCityId(1);
        order.setCarTypeId(117);
        order.setSkuId(1L);
        order.setSysExpectBookTime(new Date());
        order.setOrderStatus(SupplyOrderStatus.TAKEN.getCode());
        order.setDriverId("1");
        DelayDspTask task = new DelayDspTask();
        task.setTaskStatus(DelayDspTaskStatus.UN_EXECUTED.getCode());
        Driver driver = new Driver();
        DelayDspTaskValue value = new DelayDspTaskValue();
        value.setBegin("00:00");
        value.setEnd("23:59");
        value.setHour(1);
        value.setDeadline(1);
        PowerMockito.when(driverGateway.query(Mockito.anyString())).thenReturn(driver);
        PowerMockito.when(delayTaskConfig.get(Mockito.any(), Mockito.any(), Mockito.any(),Mockito.anyInt())).thenReturn(value);
        ValidatorDTO code = taskValidator.validate(order);
        Assert.assertEquals(ValidatorCode.TASK_NOT_EXIST, code.getCode());
    }

    @Test
    public void testKM() {
        SupplyOrder order = new SupplyOrder();
        order.setCityId(1);
        order.setCarTypeId(117);
        order.setSkuId(1L);
        order.setSysExpectBookTime(DateUtil.addDays(new Date(), 1));
        order.setOrderStatus(SupplyOrderStatus.TAKEN.getCode());
        order.setDriverId("1");
        DelayDspTask task = new DelayDspTask();
        task.setTaskStatus(DelayDspTaskStatus.UN_EXECUTED.getCode());
        Driver driver = new Driver();
        DelayDspTaskValue value = new DelayDspTaskValue();
        value.setBegin("00:00");
        value.setEnd("23:59");
        value.setHour(1);
        value.setDeadline(1);
        value.setType(DelayDspTaskType.KM.name());
        PowerMockito.when(driverGateway.query(Mockito.anyString())).thenReturn(driver);
        PowerMockito.when(delayTaskConfig.get(Mockito.any(), Mockito.any(), Mockito.any(),Mockito.anyInt())).thenReturn(value);
        PowerMockito.when(grayscaleQConfig.isGrayscaleCity(Mockito.any())).thenReturn(false);
        PowerMockito.when(grayscaleQConfig.isGrayscaleCityAndCarTypeId(Mockito.any(), Mockito.any())).thenReturn(false);
        ValidatorDTO code = taskValidator.validate(order);
        Assert.assertEquals(ValidatorCode.TASK_NOT_EXIST, code.getCode());
    }
}
