package com.ctrip.dcs.dsp.delay.qconfig

import spock.lang.Specification

/**
 * <AUTHOR>
 */
class GrayscaleQConfigTest extends Specification {

    GrayscaleQConfig grayscaleQConfig = new GrayscaleQConfig(grayscaleUid: "1,2,3", grayscaleCityRate: "1:100,2:200,3:300", "grayscaleCityCarTypeRate": "1:117:100,2:118:200,3:all:300")

    def "IsGrayscaleUid"() {

        when:
        boolean  flag = grayscaleQConfig.isGrayscaleUid(uid)
        then:
        flag == result

        where:
        uid || result
        "0"      || false
        "1"      || true
        "2"      || true
        "3"      || true
    }

    def "IsGrayscaleCity"() {

        when:
        boolean  flag = grayscaleQConfig.isGrayscaleCity(cityId)
        then:
        flag == result

        where:
        cityId || result
        0      || false
        1      || true
        2      || true
        3      || true
    }

    def "IsGrayscaleCityRateLimit"() {

        when:
        boolean  flag = grayscaleQConfig.isGrayscaleCityRateLimit(cityId, orderId)
        String code = orderId.hashCode()
        then:
        flag == result

        where:
        cityId | orderId || result
        0      | ""      || false
        1      | "1"     || true
        2      | "101"   || false
        3      | "401"   || false

    }

    def "IsGrayscaleCityAndCarTypeId"() {

        when:
        boolean  flag = grayscaleQConfig.isGrayscaleCityAndCarTypeId(cityId, carTypeId)
        then:
        flag == result

        where:
        cityId | carTypeId || result
        0      | 117       || false
        1      | 117       || true
        2      | 119       || false
        3      | 121       || true
    }

    def "IsGrayscaleCityAndCarTypeIdRateLimit"() {

        when:
        boolean  flag = grayscaleQConfig.isGrayscaleCityAndCarTypeIdRateLimit(cityId, carTypeId, orderId)
        then:
        flag == result

        where:
        cityId | carTypeId | orderId || result
        0      | 117       | ""      || false
        1      | 117       | "1"     || true
        2      | 118       | "101"   || false
        3      | 117       | "401"   || false

    }
}
