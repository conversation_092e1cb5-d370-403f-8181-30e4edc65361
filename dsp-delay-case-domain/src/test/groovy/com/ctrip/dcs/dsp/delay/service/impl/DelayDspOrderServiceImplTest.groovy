package com.ctrip.dcs.dsp.delay.service.impl

import com.ctrip.dcs.dsp.delay.carconfig.SpecialDateConfig
import com.ctrip.dcs.dsp.delay.carconfig.value.SpecialDateValue
import com.ctrip.dcs.dsp.delay.model.SupplyOrder
import com.ctrip.dcs.dsp.delay.repository.InsertPoolReasonRecordRepository
import com.ctrip.dcs.dsp.delay.validator.ValidatorCode
import com.ctrip.dcs.dsp.delay.validator.ValidatorDTO
import org.junit.Assert
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 * @since 2024/8/19 19:03
 */
class DelayDspOrderServiceImplTest extends Specification {
    def testObj = new DelayDspOrderServiceImpl()
    def insertDspPoolReasonRecordRepository = Mock(InsertPoolReasonRecordRepository)
    def specialDateConfig = Mock(SpecialDateConfig)

    def setup() {
        testObj.insertDspPoolReasonRecordRepository = insertDspPoolReasonRecordRepository
        testObj.specialDateConfig = specialDateConfig
    }

    @Unroll
    def "test recordInsertDspPoolReasonRecord success"() {
        given:
        when:
        testObj.recordInsertDspPoolReasonRecord(code, supplyOrder, startTime, endTime)

        then:
        Assert.assertTrue(Objects.nonNull(code))

        where:
        code                                     | supplyOrder                                                         | startTime | endTime || expectedResult
        new ValidatorDTO(code: ValidatorCode.OK) | new SupplyOrder(orderId: "orderId", sourceOrderId: "sourceOrderId") | 1L        | 2L      || null
    }


    def "test recordInsertDspPoolReasonRecord failed"() {
        given:
        insertDspPoolReasonRecordRepository.record(_) >> { throw new RuntimeException("") }

        when:
        testObj.recordInsertDspPoolReasonRecord(code, supplyOrder, startTime, endTime)

        then:
        Assert.assertTrue(Objects.equals(supplyOrder.getOrderId(), "orderId"))

        where:
        code                                     | supplyOrder                                                         | startTime | endTime || expectedResult
        new ValidatorDTO(code: ValidatorCode.OK) | new SupplyOrder(orderId: "orderId", sourceOrderId: "sourceOrderId") | 1L        | 2L      || null
    }

    @Unroll
    def "test checkSpecialDate"() {
        given:
        specialDateConfig.get(2, 117, _) >> specialDateValue1
        specialDateConfig.get(0, 0, _) >> specialDateValue4
        specialDateConfig.get(0, _, _) >> specialDateValue2
        specialDateConfig.get(_, 0, _) >> specialDateValue3


        when:
        def result = testObj.checkSpecialDate(2, 117, "2024-08-01 00:00:00")

        then:
        Objects.equals(result, expectedResult)

        where:
        specialDateValue1      | specialDateValue2      | specialDateValue3      | specialDateValue4      || expectedResult
        null                   | null                   | null                   | null                   || Boolean.FALSE
        new SpecialDateValue() | null                   | null                   | null                   || Boolean.TRUE
        null                   | new SpecialDateValue() | null                   | null                   || Boolean.TRUE
        null                   | null                   | new SpecialDateValue() | null                   || Boolean.TRUE
        null                   | null                   | null                   | new SpecialDateValue() || Boolean.TRUE
    }
}
