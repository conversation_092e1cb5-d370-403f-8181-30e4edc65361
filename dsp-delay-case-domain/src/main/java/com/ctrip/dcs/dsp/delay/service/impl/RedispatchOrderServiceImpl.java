package com.ctrip.dcs.dsp.delay.service.impl;

import com.ctrip.dcs.dsp.delay.enums.OutPoolWayEnum;
import com.ctrip.dcs.dsp.delay.enums.YesOrNo;
import com.ctrip.dcs.dsp.delay.gateway.SelfDispatcherOrderGateway;
import com.ctrip.dcs.dsp.delay.limit.OutPoolRateLimiter;
import com.ctrip.dcs.dsp.delay.model.DelayDspMatchTaskRecord;
import com.ctrip.dcs.dsp.delay.model.DelayDspOrder;
import com.ctrip.dcs.dsp.delay.model.DelayDspTask;
import com.ctrip.dcs.dsp.delay.model.DelayDspTaskRecord;
import com.ctrip.dcs.dsp.delay.model.SupplyOrder;
import com.ctrip.dcs.dsp.delay.qconfig.DelayDspCommonQConfig;
import com.ctrip.dcs.dsp.delay.repository.DelayDspMatchTaskRepository;
import com.ctrip.dcs.dsp.delay.repository.DelayDspOrderRepository;
import com.ctrip.dcs.dsp.delay.repository.DelayDspTaskRepository;
import com.ctrip.dcs.dsp.delay.service.PreOutPoolService;
import com.ctrip.dcs.dsp.delay.service.RedispatchOrderService;
import com.ctrip.dcs.dsp.delay.util.DateUtil;
import com.ctrip.dcs.dsp.delay.util.JsonUtil;
import com.ctrip.dcs.dsp.delay.util.MetricsUtil;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Component
public class RedispatchOrderServiceImpl implements RedispatchOrderService {

    private static final Logger logger = LoggerFactory.getLogger(RedispatchOrderServiceImpl.class);

    @Autowired
    private DelayDspTaskRepository delayDspTaskRepository;

    @Autowired
    private DelayDspOrderRepository dspOrderRepository;

    @Autowired
    private SelfDispatcherOrderGateway selfDispatcherOrderGateway;

    @Autowired
    private OutPoolRateLimiter outPoolRateLimiter;

    @Autowired
    private DelayDspMatchTaskRepository delayDspMatchTaskRepository;

    @Autowired
    private PreOutPoolService preOutPoolService;
    @Autowired
    private DelayDspCommonQConfig delayDspCommonQConfig;

    @Override
    public void redispatch(DelayDspTaskRecord record) {
        DelayDspTask task = delayDspTaskRepository.queryByTaskId(record.getTaskId());
        logger.info("RedispatchOrderServiceImpl_redispatch_forC", JsonUtil.toJson(task));
        DelayDspOrder order = dspOrderRepository.queryByOrderId(record.getOrderId());
        logger.info("RedispatchOrderServiceImpl_redispatch_forC", JsonUtil.toJson(order));
        if (Objects.isNull(task) || Objects.isNull(order) || Objects.equals(order.getIsDelay(), YesOrNo.NO.getCode())) {
            return;
        }
        try {
            MetricsUtil.recordValue("order.match.no", 1);
            try {
                if (isLimit(task)) {
                    double acquire = outPoolRateLimiter.getRedispatchRateLimiter(task).acquire();
                    logger.info("RedispatchOrderServiceImpl.redispatch", "get redispatch token.order: {}, time:{}", order.getOrderId(), acquire);
                }
            } catch (Exception e) {
                logger.error("RedispatchOrderServiceImpl.redispatch", "redispatch try acquire error! order id:{}", order.getOrderId());
            }
            redispatch(order, record);
        } catch (Exception e) {
            MetricsUtil.recordValue("self.redispatch.error", 1);
            logger.error("RedispatchOrderServiceImpl.redispatch", "redispatch error! order id:{}", order.getOrderId());
            outRecord(record.getId(), YesOrNo.NO.getCode());
        }
    }


    @Override
    public void redispatchForPreOutPool(String dspOrderId, Integer pressedDispatchSource) {
        DelayDspOrder order = dspOrderRepository.queryByOrderId(dspOrderId);
        logger.info("RedispatchOrderServiceImpl_redispatchForPreOutPool_forC", JsonUtil.toJson(order));
        if (Objects.isNull(order) || Objects.equals(order.getIsDelay(), YesOrNo.NO.getCode())) {
            return;
        }
        SupplyOrder supplyOrder = selfDispatcherOrderGateway.query(order.getOrderId());
        logger.info("RedispatchOrderServiceImpl_redispatchForPreOutPool_query", "order: {}, supplyOrder: {}", order.getOrderId(), JsonUtil.toJson(supplyOrder));
        if (Objects.isNull(supplyOrder)) {
            selfDispatcherOrderGateway.redispatch(order.getMainOrderId());
            preOutPoolService.sendMessageToWorkBenchLog(order.getMainOrderId(),  delayDspCommonQConfig.getOutReasonConfig(OutPoolWayEnum.PRESSED.getWay()));
            return;
        }
        if (!supplyOrder.isServiceProviderConfirm()) {    // 返查采购订单，若采购订单非服务商确认，则不进行改派
            logger.info("redispatchForPreOutPool.redispatch", "order is not service provider confirm.order: {}, driver: {}", order.getOrderId());
            return;
        }
        selfDispatcherOrderGateway.redispatch(order.getMainOrderId());
        preOutPoolService.sendMessageToWorkBenchLog(order.getMainOrderId(),  delayDspCommonQConfig.getOutReasonConfig(OutPoolWayEnum.PRESSED.getWay()));

    }

    @Override
    public void redispatch(DelayDspOrder order, DelayDspTaskRecord record) {
        Long matchRecordId = Optional.ofNullable(record).map(DelayDspTaskRecord::getMatchRecordId).orElse(0L);
        SupplyOrder supplyOrder = selfDispatcherOrderGateway.queryBase(order.getOrderId());
        logger.info("RedispatchOrderServiceImpl_redispatch_query", "order: {}, supplyOrder: {}", order.getOrderId(), JsonUtil.toJson(supplyOrder));
        if (Objects.isNull(supplyOrder)) {
            MetricsUtil.recordValue("RedispatchOrderServiceImpl.redispatch.null", 1);
            selfDispatcherOrderGateway.redispatch(order.getMainOrderId());
            updateDelayDspMatchTaskRecord(record);
            if(Objects.nonNull(matchRecordId) && matchRecordId > 0){
                preOutPoolService.sendMessageToWorkBenchLog(order.getMainOrderId(), delayDspCommonQConfig.getOutReasonConfig(OutPoolWayEnum.MATCH_NO_DRIVER.getWay()));
            }else{
                preOutPoolService.sendMessageToWorkBenchLog(order.getMainOrderId(), delayDspCommonQConfig.getOutReasonConfig(OutPoolWayEnum.ARRIVER_OUT_NO_DRIVER_TIME.getWay()));
            }
            return;
        }
        if (!supplyOrder.isServiceProviderConfirm()) {    // 返查采购订单，若采购订单非服务商确认，则不进行改派
            logger.info("SupplyOrderServiceImpl.redispatch", "order is not service provider confirm.order: {}, driver: {}", order.getOrderId());
            return;
        }
        selfDispatcherOrderGateway.redispatch(order.getMainOrderId());
        updateDelayDspMatchTaskRecord(record);
        if(Objects.nonNull(matchRecordId) && matchRecordId > 0){
            preOutPoolService.sendMessageToWorkBenchLog(order.getMainOrderId(), delayDspCommonQConfig.getOutReasonConfig(OutPoolWayEnum.MATCH_NO_DRIVER.getWay()));
        }else{
            preOutPoolService.sendMessageToWorkBenchLog(order.getMainOrderId(), delayDspCommonQConfig.getOutReasonConfig(OutPoolWayEnum.ARRIVER_OUT_NO_DRIVER_TIME.getWay()));
        }
    }


    private void updateDelayDspMatchTaskRecord(DelayDspTaskRecord record){
        if(Objects.isNull(record) || record.getPreOut().equals(YesOrNo.NO.getCode())){
            return;
        }
        DelayDspMatchTaskRecord record1 = new DelayDspMatchTaskRecord();
        record1.setId(record.getMatchRecordId());
        record1.setTakenCode(null);
        record1.setOutTime(new Date());
        delayDspMatchTaskRepository.update(record1);
    }



    private void outRecord(Long recordId, Integer out) {
        try {
            DelayDspTaskRecord record = new DelayDspTaskRecord();
            record.setId(recordId);
            record.setIsOut(out);
            delayDspTaskRepository.update(record);
        } catch (Exception e) {
            logger.error(e);
        }
    }

    /**
     * 是否需要限流
     * 当前时间与最晚出池时间的时间差，若小于30分钟，则不限流
     * @param task
     * @return
     */
    private boolean isLimit(DelayDspTask task) {
        double diff = DateUtil.minutesDiff(new Date(), task.getExecuteTimeDeadline());
        return diff > 30D;
    }
}
