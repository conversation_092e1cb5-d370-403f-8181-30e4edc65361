package com.ctrip.dcs.dsp.delay.qconfig.key;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;

@Getter
public class HeadTailLimitKeyVO implements CarConfigKeyVO {
    private Integer cityId;
    private Integer carTypeId;

    @JsonCreator
    public HeadTailLimitKeyVO(@JsonProperty("cityId") Integer cityId, @JsonProperty("carTypeId") Integer carTypeId) {
        this.cityId = cityId;
        this.carTypeId = carTypeId;
    }

    @Override
    public String toKey() {
        return cityId + PLACEHOLDER + carTypeId;
    }
}
