package com.ctrip.dcs.dsp.delay.infrastructure.trocks;

import com.google.common.collect.Lists;
import credis.java.client.sync.trocks.TRocksCacheProvider;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.List;

/**
 * <AUTHOR>
 */
@RunWith(PowerMockRunner.class)
public class TRocksProviderProxyTest {

    @InjectMocks
    private TRocksProviderProxy tRocksProviderProxy;

    @Mock
    private TRocksCacheProvider tRocksCacheProvider;

    @Test
    public void testMSet1() {
        String[] kv =  new String[]{"a", "b"};
        String result = tRocksProviderProxy.mset(6000, kv);
        Assert.assertNull(result);
    }

    @Test
    public void testMSet2() {
        String[] kv =  new String[5000];
        for (int i = 0; i < kv.length; i++) {
            kv[i] = i + "";
        }
        String result = tRocksProviderProxy.mset(6000, kv);
        Assert.assertEquals("ok", result);
    }

    @Test
    public void testMGet() {
        List<String> keys = Lists.newArrayList();
        for (int i = 0; i < 5000; i++) {
            keys.add(i + "");
        }
        List<String> list = tRocksProviderProxy.mget(keys);
        Assert.assertEquals(list.size(), 0);
    }
}
