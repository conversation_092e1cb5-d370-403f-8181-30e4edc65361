package com.ctrip.dcs.dsp.delay.validator;

import com.ctrip.dcs.dsp.delay.cache.DistributedCache;
import com.ctrip.dcs.dsp.delay.carconfig.CarConfig;
import com.ctrip.dcs.dsp.delay.carconfig.CarConfigCode;
import com.ctrip.dcs.dsp.delay.carconfig.value.VirtualDspTakenFailLimitValue;
import com.ctrip.dcs.dsp.delay.gateway.CarConfigGateway;
import com.ctrip.dcs.dsp.delay.model.SupplyOrder;
import com.ctrip.dcs.dsp.delay.qconfig.DelayDspCommonQConfig;
import com.ctrip.dcs.dsp.delay.qconfig.InventoryValidatorCityValueConfig;
import com.ctrip.dcs.dsp.delay.qconfig.VirtualDspTakenFailLimitConfig;
import com.ctrip.dcs.dsp.delay.qconfig.value.VirtualDspTakenFailLimitValueVO;
import com.ctrip.dcs.dsp.delay.util.DateUtil;
import com.ctrip.dcs.dsp.delay.validator.impl.VirtualDspValidator;
import com.google.common.collect.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.Date;

/**
 * <AUTHOR>
 */
@RunWith(PowerMockRunner.class)
public class VirtualDspValidatorTest {

    @InjectMocks
    private VirtualDspValidator virtualDspValidator;

    @Mock
    private CarConfigGateway carConfigGateway;

    @Mock
    private InventoryValidatorCityValueConfig inventoryValidatorCityValueConfig;

    @Mock
    private CarConfig carConfig;

    @Mock
    private DistributedCache distributedCache;

    @Mock
    private DelayDspCommonQConfig delayDspCommonQConfig;

    @Mock
    VirtualDspTakenFailLimitConfig virtualDspTakenFailLimitConfig;

    @Test
    public void testOff() {
        SupplyOrder order = new SupplyOrder();
        PowerMockito.when(delayDspCommonQConfig.getVirtualDspSwitch()).thenReturn(0);
        PowerMockito.when(inventoryValidatorCityValueConfig.getValue(Mockito.any())).thenReturn(false);
        ValidatorDTO code = virtualDspValidator.validate(order);
        Assert.assertEquals(ValidatorCode.OK, code.getCode());
    }

    @Test
    public void testOff1() {
        SupplyOrder order = new SupplyOrder();
        PowerMockito.when(delayDspCommonQConfig.getVirtualDspSwitch()).thenReturn(0);
        PowerMockito.when(inventoryValidatorCityValueConfig.getValue(Mockito.any())).thenReturn(true);
        ValidatorDTO code = virtualDspValidator.validate(order);
        Assert.assertEquals(ValidatorCode.OK, code.getCode());
    }

    @Test
    public void testOK() {
        SupplyOrder order = new SupplyOrder();
        order.setCityId(1);
        order.setCarTypeId(117);
        order.setSysExpectBookTime(new Date());
        VirtualDspTakenFailLimitValue config = new VirtualDspTakenFailLimitValue();
        config.setLimitedValue(10);
        config.setStartTime("00:00");
        config.setEndTime("23:59");
        PowerMockito.when(inventoryValidatorCityValueConfig.getValue(Mockito.any())).thenReturn(false);
        PowerMockito.when(delayDspCommonQConfig.getVirtualDspSwitch()).thenReturn(1);
        PowerMockito.when(distributedCache.scard(Mockito.anyString())).thenReturn(9L);
        PowerMockito.when(carConfigGateway.query(CarConfigCode.VIRTUAL_DSP_TAKEN_FAIL_LIMIT.getCode(), CarConfigCode.VIRTUAL_DSP_TAKEN_FAIL_LIMIT.getVersion())).thenReturn(carConfig);
        PowerMockito.when(carConfig.values(Mockito.any(), Mockito.any())).thenReturn(Lists.newArrayList(config));

        ValidatorDTO code = virtualDspValidator.validate(order);
        Assert.assertEquals(ValidatorCode.OK, code.getCode());
    }

    @Test
    public void testOK1() {
        SupplyOrder order = new SupplyOrder();
        order.setCityId(1);
        order.setCarTypeId(117);
        order.setSysExpectBookTime(DateUtil.parseDate("2022-04-28 11:00:00"));
        VirtualDspTakenFailLimitValue config = new VirtualDspTakenFailLimitValue();
        config.setLimitedValue(10);
        config.setStartTime("00:00");
        config.setEndTime("12:00");

        PowerMockito.when(inventoryValidatorCityValueConfig.getValue(Mockito.any())).thenReturn(false);
        PowerMockito.when(delayDspCommonQConfig.getVirtualDspSwitch()).thenReturn(1);
        PowerMockito.when(distributedCache.scard(Mockito.anyString())).thenReturn(9L);
        PowerMockito.when(carConfigGateway.query(CarConfigCode.VIRTUAL_DSP_TAKEN_FAIL_LIMIT.getCode(), CarConfigCode.VIRTUAL_DSP_TAKEN_FAIL_LIMIT.getVersion())).thenReturn(carConfig);
        PowerMockito.when(carConfig.values(Mockito.any(), Mockito.any())).thenReturn(Lists.newArrayList(config));

        ValidatorDTO code = virtualDspValidator.validate(order);
        Assert.assertEquals(ValidatorCode.OK, code.getCode());
    }

    @Test
    public void testOK2() {
        SupplyOrder order = new SupplyOrder();
        order.setCityId(1);
        order.setCarTypeId(117);
        order.setSysExpectBookTime(DateUtil.parseDate("2022-04-28 14:00:00"));
        VirtualDspTakenFailLimitValue config = new VirtualDspTakenFailLimitValue();
        config.setLimitedValue(10);
        config.setStartTime("00:00");
        config.setEndTime("12:00");

        PowerMockito.when(delayDspCommonQConfig.getVirtualDspSwitch()).thenReturn(1);
        PowerMockito.when(distributedCache.scard(Mockito.anyString())).thenReturn(9L);
        PowerMockito.when(carConfigGateway.query(CarConfigCode.VIRTUAL_DSP_TAKEN_FAIL_LIMIT.getCode(), CarConfigCode.VIRTUAL_DSP_TAKEN_FAIL_LIMIT.getVersion())).thenReturn(carConfig);
        PowerMockito.when(carConfig.values(Mockito.any(), Mockito.any())).thenReturn(Lists.newArrayList(config));
        PowerMockito.when(inventoryValidatorCityValueConfig.getValue(Mockito.any())).thenReturn(false);

        ValidatorDTO code = virtualDspValidator.validate(order);
        Assert.assertEquals(ValidatorCode.OK, code.getCode());
    }

    @Test
    public void testNotOK() {
        SupplyOrder order = new SupplyOrder();
        order.setCityId(1);
        order.setCarTypeId(117);
        order.setSysExpectBookTime(DateUtil.parseDate("2022-04-28 11:00:00"));
        VirtualDspTakenFailLimitValue config = new VirtualDspTakenFailLimitValue();
        config.setLimitedValue(10);
        config.setStartTime("00:00");
        config.setEndTime("12:00");

        PowerMockito.when(delayDspCommonQConfig.getVirtualDspSwitch()).thenReturn(1);
        PowerMockito.when(distributedCache.scard(Mockito.anyString())).thenReturn(11L);
        PowerMockito.when(carConfigGateway.query(CarConfigCode.VIRTUAL_DSP_TAKEN_FAIL_LIMIT.getCode(), CarConfigCode.VIRTUAL_DSP_TAKEN_FAIL_LIMIT.getVersion())).thenReturn(carConfig);
        PowerMockito.when(carConfig.values(Mockito.any(), Mockito.any())).thenReturn(Lists.newArrayList(config));
        PowerMockito.when(inventoryValidatorCityValueConfig.getValue(Mockito.any())).thenReturn(false);
        PowerMockito.when(virtualDspTakenFailLimitConfig.get(Mockito.anyList())).thenReturn(Lists.newArrayList(new VirtualDspTakenFailLimitValueVO("00:00", "12:00", 10)));

        ValidatorDTO code = virtualDspValidator.validate(order);
        Assert.assertEquals(ValidatorCode.VIRTUAL_DSP_LIMIT, code.getCode());
    }
}
