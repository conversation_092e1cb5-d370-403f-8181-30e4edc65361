package com.ctrip.dcs.dsp.delay.conflict.impl;

import com.ctrip.dcs.dsp.delay.conflict.Check;
import com.ctrip.dcs.dsp.delay.conflict.CheckCode;
import com.ctrip.dcs.dsp.delay.conflict.ConflictContext;
import com.ctrip.dcs.dsp.delay.consts.CommonConstant;
import com.ctrip.dcs.dsp.delay.gateway.GeoGateway;
import com.ctrip.dcs.dsp.delay.model.DelayDspOrder;
import com.ctrip.dcs.dsp.delay.model.DelayDspTask;
import com.ctrip.dcs.dsp.delay.model.DispatcherConfig;
import com.ctrip.dcs.dsp.delay.model.Driver;
import com.ctrip.dcs.dsp.delay.model.Position;
import com.ctrip.dcs.dsp.delay.model.Route;
import com.ctrip.dcs.dsp.delay.model.SupplyOrder;
import com.ctrip.dcs.dsp.delay.util.DateUtil;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 尾单检查
 * <AUTHOR>
 */
@Component("tailOrderCheck")
public class TailOrderCheck implements Check {

    @Autowired
    private GeoGateway geoGateway;

    @Override
    public CheckCode check(ConflictContext context) {

        if (CollectionUtils.isEmpty(context.getList())) {
            return CheckCode.OK;
        }

        DispatcherConfig config = context.getConfig();
        Double tailLimit = config.getTailLimit();
        if (tailLimit < 0D) {   // 配置限制时长为负数，则通过检查
            return CheckCode.OK;
        }

        List<DelayDspOrder> list = context.getList();
        DelayDspOrder order = list.get(list.size() - 1);
        if (order.hasTaken()) {
            return CheckCode.OK;
        }

        DelayDspTask task = context.getDelayDspTask();
        if (order.getSysExpectBookTime().before(DateUtil.addHours(task.getEndTime(), -CommonConstant.DRIVER_WORK_TIME_RANGE_HOUR))) {     // 订单不在尾单时段内，非尾单
            return CheckCode.OK;
        }

        List<SupplyOrder> supplyOrders = context.getDriverAggregation().getOutTimeOrders();
        if (CollectionUtils.isNotEmpty(supplyOrders)) {
            for (SupplyOrder outOrder : supplyOrders) {
                if (outOrder.getSysExpectBookTime().after(order.getSysExpectBookTime())) { // 司机时段外有抢到更晚的订单，则不进行尾单检查
                    return CheckCode.OK;
                }
            }
        }

        Driver driver = context.getDriverAggregation().getDriver();
        Route route = geoGateway.queryRoute(task.getTaskId(), new Position(order, driver));
        if (!order.getPredictServiceStopTime().after(task.getEndTime())) {   // 尾预估完成时间在司机结束工作时间之前
            double tailDuration = DateUtil.minutesDiff(order.getPredictServiceStopTime(), task.getEndTime());
            // 司机工作结束时间与尾单结束服务时间之差>尾单到司机家空驶+配置限制时长，则不通过检查
            if (route.getDuration() > tailDuration + tailLimit) {
                return CheckCode.TAIL_LIMIT_CONFLICT;
            }
        } else {    // 尾单预估完成时间在司机结束工作时间之后
            if (route.getDuration() > tailLimit) {    // 空驶时长>配置限制时长,则不通过检查
                return CheckCode.TAIL_LIMIT_CONFLICT;
            }
        }
        return CheckCode.OK;
    }
}
