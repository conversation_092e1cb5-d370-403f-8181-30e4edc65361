package com.ctrip.dcs.dsp.delay.interfaces.consumer

import com.ctrip.dcs.dsp.delay.enums.DelayDspTaskStatus
import com.ctrip.dcs.dsp.delay.model.DelayDspOrder
import com.ctrip.dcs.dsp.delay.model.DelayDspTask
import com.ctrip.dcs.dsp.delay.mq.MessageProducer
import com.ctrip.dcs.dsp.delay.qconfig.DelayDspCommonQConfig
import com.ctrip.dcs.dsp.delay.repository.DelayDspOrderRepository
import com.ctrip.dcs.dsp.delay.repository.DelayDspTaskRepository
import com.ctrip.igt.framework.common.clogging.Logger
import org.springframework.beans.factory.annotation.Autowired
import qunar.tc.qmq.NeedRetryException
import qunar.tc.qmq.base.BaseMessage
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

/**
 * <AUTHOR>
 */
class DelayDspTaskRunCheckConsumerTest extends Specification {
    @Mock
    Logger logger
    @Mock
    DelayDspTaskRepository delayDspTaskRepository
    @Mock
    DelayDspOrderRepository delayDspOrderRepository;
    @Mock
    MessageProducer messageProducer;
    @Mock
    DelayDspCommonQConfig delayDspCommonQConfig;
    @Mock
    BaseMessage message
    @Mock
    DelayDspTask task
    @Mock
    DelayDspOrder order
    @InjectMocks
    DelayDspTaskRunCheckConsumer delayDspTaskRunCheckConsumer

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test on Message"() {

        given:
        when(delayDspTaskRepository.queryByTaskId(anyLong())).thenReturn(task)
        when(message.getLongProperty(anyString())).thenReturn(1L)
        when(task.getTaskStatus()).thenReturn(DelayDspTaskStatus.EXECUTING.getCode())
        when(task.getTaskId()).thenReturn(1L)
        when(delayDspOrderRepository.queryByTaskId(1L, 1)).thenReturn([order])
        doNothing().when(logger).warn(anyString(), anyString(), anyLong())

        when:
        def result = delayDspTaskRunCheckConsumer.onMessage(message)

        then:
        result == null
    }

    def "test on Message 1"() {

        given:
        when(delayDspTaskRepository.queryByTaskId(anyLong())).thenReturn(task)
        when(message.getLongProperty(anyString())).thenReturn(0L)
        when(task.getTaskStatus()).thenReturn(DelayDspTaskStatus.EXECUTING.getCode())
        when(task.getTaskId()).thenReturn(1L)
        when(delayDspOrderRepository.queryByTaskId(1L, 1)).thenReturn([order])
        doNothing().when(logger).warn(anyString(), anyString(), anyLong())

        when:
        def result = delayDspTaskRunCheckConsumer.onMessage(message)

        then:
        result == null
    }

    def "test on Message 2"() {

        given:
        when(delayDspTaskRepository.queryByTaskId(anyLong())).thenReturn(null)
        when(message.getLongProperty(anyString())).thenReturn(1L)
        when(task.getTaskStatus()).thenReturn(DelayDspTaskStatus.EXECUTING.getCode())
        when(task.getTaskId()).thenReturn(1L)
        when(delayDspOrderRepository.queryByTaskId(1L, 1)).thenReturn([order])
        doNothing().when(logger).warn(anyString(), anyString(), anyLong())

        when:
        def result = delayDspTaskRunCheckConsumer.onMessage(message)

        then:
        result == null

    }

    def "test on Message 3"() {

        given:
        when(delayDspTaskRepository.queryByTaskId(anyLong())).thenReturn(task)
        when(message.getLongProperty(anyString())).thenReturn(1L)
        when(task.getTaskStatus()).thenReturn(DelayDspTaskStatus.EXECUTION_COMPLETE.getCode())
        when(task.getTaskId()).thenReturn(1L)
        when(delayDspOrderRepository.queryByTaskId(1L, 1)).thenReturn([order])
        doNothing().when(logger).warn(anyString(), anyString(), anyLong())

        when:
        def result = delayDspTaskRunCheckConsumer.onMessage(message)

        then:
        result == null
    }

    def "test is Complete"() {
        when:
        when(delayDspTaskRepository.queryByTaskId(anyLong())).thenReturn(null).thenReturn(task).thenReturn(task)
        when(task.getTaskId()).thenReturn(1L)
        when(delayDspOrderRepository.queryByTaskId(1L, 1)).thenReturn([order])
        when(task.getTaskStatus()).thenReturn(DelayDspTaskStatus.EXECUTING.getCode()).thenReturn(DelayDspTaskStatus.EXECUTING.getCode()).thenReturn(DelayDspTaskStatus.EXECUTION_COMPLETE.getCode())
        boolean r1 = delayDspTaskRunCheckConsumer.isComplete(task)
        boolean r2 = delayDspTaskRunCheckConsumer.isComplete(task)
        boolean r3 = delayDspTaskRunCheckConsumer.isComplete(task)

        then:
        !r1
        !r2
        r3
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme