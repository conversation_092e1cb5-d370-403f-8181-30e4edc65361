package com.ctrip.dcs.dsp.delay.enums;

import com.ctrip.igt.framework.common.exception.BizException;
import com.google.common.collect.Sets;
import lombok.Getter;

import java.util.Set;

/**
 * <AUTHOR>
 */
@Getter
public enum CategoryCode {
    airport_pickup(2), airport_dropoff(1), station_pickup(5), station_dropoff(4), day_rental(9), customize_day_rental(11), point_to_point(12);

    private int code;

    CategoryCode(int code) {
        this.code = code;
    }



    public static boolean isPickup(String categoryCode) {
        Set<String> set = Sets.newHashSet(airport_pickup.name(), station_pickup.name());
        return set.contains(categoryCode);
    }

    public static boolean isDropOff(String categoryCode) {
        Set<String> set = Sets.newHashSet(airport_dropoff.name(), station_dropoff.name());
        return set.contains(categoryCode);
    }

    public static boolean isJnt(String categoryCode) {
        Set<String> set = Sets.newHashSet(airport_pickup.name(), airport_dropoff.name(), station_pickup.name(), station_dropoff.name());
        return set.contains(categoryCode);
    }

    public static boolean isJntOrPtp(String categoryCode) {
        Set<String> set = Sets.newHashSet(airport_pickup.name(), airport_dropoff.name(), station_pickup.name(), station_dropoff.name(), point_to_point.name());
        return set.contains(categoryCode);
    }

    public static boolean isDayRental(String categoryCode) {
        Set<String> set = Sets.newHashSet(day_rental.name(), customize_day_rental.name());
        return set.contains(categoryCode);
    }

    public static String valueOf(int code) {
        for (CategoryCode category : CategoryCode.values()) {
            if (category.getCode() == code) {
                return category.name();
            }
        }
        throw new BizException("there is no category code is " + code);
    }

}
