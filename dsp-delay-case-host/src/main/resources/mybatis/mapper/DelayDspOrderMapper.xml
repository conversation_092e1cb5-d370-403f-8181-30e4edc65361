<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ctrip.dcs.dsp.delay.infrastructure.mapper.DelayDspOrderMapper">

    <resultMap id="BaseResultMap" type="com.ctrip.dcs.dsp.delay.model.DelayDspOrder">
        <result column="order_id" property="orderId" />
        <result column="main_order_id" property="mainOrderId" />
        <result column="task_id" property="taskId" />
        <result column="city_id" property="cityId" />
        <result column="city_code" property="cityCode" />
        <result column="car_type_id" property="carTypeId" />
        <result column="sku_id" property="skuId" />
        <result column="channel_id" property="channelId" />
        <result column="sys_expect_book_time" property="sysExpectBookTime" />
        <result column="predict_service_stop_time" property="predictServiceStopTime" />
        <result column="category_code" property="categoryCode" />
        <result column="is_ota" property="isOta" />
        <result column="kilo_length" property="kiloLength" />
        <result column="from_longitude" property="fromLongitude" />
        <result column="from_latitude" property="fromLatitude" />
        <result column="from_coordsys" property="fromCoordsys" />
        <result column="from_hash" property="fromHash" />
        <result column="to_longitude" property="toLongitude" />
        <result column="to_latitude" property="toLatitude" />
        <result column="to_coordsys" property="toCoordsys" />
        <result column="to_hash" property="toHash" />
        <result column="driver_id" property="driverId" />
        <result column="driver_order_fee" property="driverOrderFee" />
        <result column="is_cancel" property="isCancel" />
        <result column="is_delay" property="isDelay" />
        <result column="duid" property="duid" />
        <result column="order_source" property="orderSource" />
    </resultMap>

    <sql id="Base_Column_List">
        order_id,
        main_order_id,
        task_id,
        city_id,
        city_code,
        car_type_id,
        sku_id,
        channel_id,
        sys_expect_book_time,
        predict_service_stop_time,
        category_code,
        is_ota,
        kilo_length,
        from_longitude,
        from_latitude,
        from_coordsys,
        from_hash,
        to_longitude,
        to_latitude,
        to_coordsys,
        to_hash,
        driver_id,
        driver_order_fee,
        is_cancel,
        is_delay,
        duid,
        order_source
    </sql>

    <insert id="insertOrUpdate" parameterType="com.ctrip.dcs.dsp.delay.model.DelayDspOrder">
        INSERT INTO delay_dsp_order (
            order_id,
            main_order_id,
            task_id,
            city_id,
            city_code,
            car_type_id,
            sku_id,
            channel_id,
            sys_expect_book_time,
            predict_service_stop_time,
            category_code,
            is_ota,
            kilo_length,
            from_longitude,
            from_latitude,
            from_coordsys,
            from_hash,
            to_longitude,
            to_latitude,
            to_coordsys,
            to_hash,
            driver_id,
            driver_order_fee,
            is_cancel,
            is_delay,
            duid,
            order_source,
            datachange_lasttime,
            datachange_createtime
        )
        VALUES (
            #{orderId},
            #{mainOrderId},
            #{taskId},
            #{cityId},
            #{cityCode},
            #{carTypeId},
            #{skuId},
            #{channelId},
            #{sysExpectBookTime},
            #{predictServiceStopTime},
            #{categoryCode},
            #{isOta},
            #{kiloLength},
            #{fromLongitude},
            #{fromLatitude},
            #{fromCoordsys},
            #{fromHash},
            #{toLongitude},
            #{toLatitude},
            #{toCoordsys},
            #{toHash},
            #{driverId},
            #{driverOrderFee},
            #{isCancel},
            #{isDelay},
            #{duid},
            #{orderSource},
            NOW(),
            NOW()
        )
        ON duplicate KEY UPDATE
            sys_expect_book_time = #{sysExpectBookTime},
            predict_service_stop_time = #{predictServiceStopTime},
            driver_id = #{driverId},
            is_cancel = #{isCancel},
            is_delay = #{isDelay},
            duid = #{duid},
            datachange_lasttime = NOW()
    </insert>

    <select id="queryByOrderId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM delay_dsp_order
        WHERE
        order_id = #{orderId}
        limit 1
    </select>

    <select id="queryByMainOrderId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM delay_dsp_order
        WHERE
        main_order_id = #{mainOrderId}
        limit 1
    </select>

    <select id="queryDelayOrderByMainOrderId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM delay_dsp_order
        WHERE
        main_order_id = #{mainOrderId} and is_cancel = 0 and is_delay = 1
        limit 1
    </select>

    <select id="queryByTaskId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM delay_dsp_order
        WHERE
        task_id = #{taskId}
        AND is_cancel = 0
    </select>

    <select id="queryByType" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM delay_dsp_order
        WHERE
        task_id = #{taskId}
        AND is_delay = #{isDelay}
        AND is_cancel = 0
    </select>

    <select id="queryByOrderIds" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM delay_dsp_order
        WHERE
        order_id in
        <foreach collection="orderIds" item="orderId" open="(" separator="," close=")">
            #{orderId}
        </foreach>
    </select>


    <select id="queryByOrderIdsWithTaskId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM delay_dsp_order
        WHERE
        order_id in
        <foreach collection="orderIds" item="orderId" open="(" separator="," close=")">
            #{orderId}
        </foreach>
        and
        task_id = #{taskId}
        and is_cancel = 0
        AND is_delay = 1
        order by sys_expect_book_time asc limit #{limitNum}
    </select>


    <select id="queryByDrivers" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM delay_dsp_order
        WHERE
        driver_id in
        <foreach collection="driverIds" item="driverId" open="(" separator="," close=")">
            #{driverId}
        </foreach>
        AND is_cancel = 0
        AND is_delay = 0
        AND <![CDATA[predict_service_stop_time >= #{beginTime}]]>
        AND <![CDATA[sys_expect_book_time <= #{endTime}]]>
    </select>

    <update id="update" parameterType="com.ctrip.dcs.dsp.delay.model.DelayDspOrder">
        UPDATE delay_dsp_order
        <set>
            <if test="sysExpectBookTime != null">
                sys_expect_book_time=#{sysExpectBookTime},
            </if>
            <if test="predictServiceStopTime != null">
                predict_service_stop_time=#{predictServiceStopTime},
            </if>
            <if test="driverId != null">
                driver_id=#{driverId},
            </if>
            <if test="isCancel != null">
                is_cancel=#{isCancel},
            </if>
        </set>
        WHERE order_id = #{orderId}
    </update>


    <update id="updateForCancel" parameterType="com.ctrip.dcs.dsp.delay.model.DelayDspOrder">
        UPDATE delay_dsp_order
        <set>
            <if test="sysExpectBookTime != null">
                sys_expect_book_time=#{sysExpectBookTime},
            </if>
            <if test="predictServiceStopTime != null">
                predict_service_stop_time=#{predictServiceStopTime},
            </if>
            <if test="driverId != null">
                driver_id=#{driverId},
            </if>
            <if test="isCancel != null">
                is_cancel=#{isCancel},
            </if>
        </set>
        WHERE order_id = #{orderId}
    </update>

</mapper>