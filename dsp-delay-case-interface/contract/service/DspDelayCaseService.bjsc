include '..\BaijiCommonTypes.bjsc'

include 'message\DelayDispatchMessage.bjsc'

namespace java 'com.ctrip.dcs.dsp.delay.service.interfaces'

/* 延后派服务*/
@serviceName='DspDelayCaseService'
@serviceNamespace='http://soa.ctrip.com/24406'
service DspDelayCaseService {

    BaijiCommonTypes.CheckHealthResponseType checkHealth(BaijiCommonTypes.CheckHealthRequestType request);

    DelayDispatchMessage.QueryDelayDspOrderResponseType queryDelayDspOrder(DelayDispatchMessage.QueryDelayDspOrderRequestType request);

    DelayDispatchMessage.InsertDelayDspOrderResponseType insertDelayDspOrder(DelayDispatchMessage.InsertDelayDspOrderRequestType request);

    DelayDispatchMessage.IncreaseVirtualDspTakenFailCountResponseType increaseVirtualDspTakenFailCount(DelayDispatchMessage.IncreaseVirtualDspTakenFailCountRequestType request);

    DelayDispatchMessage.QueryDelayDspOrderBatchResponseType queryDelayDspOrderBatch(DelayDispatchMessage.QueryDelayDspOrderBatchRequestType request);

    DelayDispatchMessage.InsertDelayDspPoolResponseType insertDelayDspPool(DelayDispatchMessage.InsertDelayDspPoolRequestType request);

    DelayDispatchMessage.ExecuteDelayDspTaskResponseType executeDelayDspTask(DelayDispatchMessage.ExecuteDelayDspTaskRequestType request);

    DelayDispatchMessage.QueryIsDriverMatchingResponseType queryIsDriverMatching(DelayDispatchMessage.QueryIsDriverMatchingRequestType request);

    DelayDispatchMessage.QueryDelayDspConfigResInfoResponseType queryDelayDspConfigList(DelayDispatchMessage.QueryDelayDspConfigInfoRequestType request);

    DelayDispatchMessage.CheckSpecialDateResponseType checkSpecialDate(DelayDispatchMessage.CheckSpecialDateRequestType request);

    DelayDispatchMessage.UpdateTaskInfoResponseType updateTaskInfo(DelayDispatchMessage.UpdateTaskInfoRequestType request);

    DelayDispatchMessage.PreOutPoolResponseType preOutPool(DelayDispatchMessage.PreOutPoolRequestType request);

}