package com.ctrip.dcs.dsp.delay.interfaces.consumer;

import com.ctrip.dcs.dsp.application.request.UpdateDelayDspOrderRequest;
import com.ctrip.dcs.dsp.application.service.DelayDspOrderApplicationService;
import com.ctrip.dcs.dsp.delay.consts.CommonConstant;
import com.ctrip.dcs.dsp.delay.enums.OrderSource;
import com.ctrip.dcs.dsp.delay.enums.YesOrNo;
import com.ctrip.dcs.dsp.delay.model.DelayDspOrder;
import com.ctrip.dcs.dsp.delay.model.DelayDspTask;
import com.ctrip.dcs.dsp.delay.repository.DelayDspOrderRepository;
import com.ctrip.dcs.dsp.delay.repository.DelayDspTaskRepository;
import com.ctrip.dcs.dsp.delay.service.RedispatchOrderService;
import com.ctrip.dcs.dsp.delay.util.JsonUtil;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.qmq.annontation.QmqLogTag;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;

import java.util.Objects;

/**
 * <AUTHOR>
 */
@Component
public class OrderBookTimeChangeConsumer {

    private static final Logger logger = LoggerFactory.getLogger(OrderBookTimeChangeConsumer.class);

    @Autowired
    private DelayDspOrderApplicationService delayDspApplicationService;

    @Autowired
    private DelayDspOrderRepository delayDspOrderRepository;

    @Autowired
    private DelayDspTaskRepository delayDspTaskRepository;

    @Autowired
    private RedispatchOrderService redispatchOrderService;

    @QmqLogTag()
    @QmqConsumer(prefix = CommonConstant.DSP_ORDER_BOOK_TIME_CHANGE_TOPIC, consumerGroup = CommonConstant.APP_ID)
    public void onMessage1(Message message) {
        try {
            String dspOrderId = message.getStringProperty("dspOrderId");
            if (StringUtils.isBlank(dspOrderId)) {
                return;
            }
            handle(dspOrderId);

        } catch (Exception e) {
            logger.error("consume car.qbest.order.orderstate.ORDER_TAKEN! error!", e);
        }
    }

    private void handle(String orderId) {
        logger.info("OrderBookTimeChangeConsumer_handle", JsonUtil.toJson(orderId));
        delayDspApplicationService.updateDelayDspOrder(new UpdateDelayDspOrderRequest(orderId));
        DelayDspOrder order = delayDspOrderRepository.queryByOrderId(orderId);
        logger.info("OrderBookTimeChangeConsumer_handle_queryByOrderId", JsonUtil.toJson(order));
        if (isRedispatch(order)) {    // 判断订单用车时间是否早于任务的执行时间
            if (StringUtils.equalsIgnoreCase(order.getOrderSource(), OrderSource.CTRIP.name())) {
                redispatchOrderService.redispatch(order, null);
            }
        }
    }

    private boolean isRedispatch(DelayDspOrder order) {
        if (Objects.isNull(order) || Objects.equals(order.getIsDelay(), YesOrNo.NO.getCode()) || Objects.equals(order.getIsCancel(), YesOrNo.YES.getCode())) {
            // 非延后派订单
            return false;
        }

        DelayDspTask task = delayDspTaskRepository.queryByTaskId(order.getTaskId());
        if(Objects.isNull(task)){
            return false;
        }
        // 订单对应的任务未执行，并且订单的用车时间早于任务的执行时间
        return task.isUnExecuted() && order.getSysExpectBookTime().before(task.getExecuteTime());
    }
}
