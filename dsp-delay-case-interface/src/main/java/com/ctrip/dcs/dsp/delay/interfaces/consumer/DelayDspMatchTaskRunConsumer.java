package com.ctrip.dcs.dsp.delay.interfaces.consumer;

import com.ctrip.arch.distlock.DLock;
import com.ctrip.arch.distlock.redis.RedisDistributedLockService;
import com.ctrip.dcs.dsp.application.request.DispatchDelayDspTaskRequest;
import com.ctrip.dcs.dsp.application.service.DelayDspMatchTaskApplicationService;
import com.ctrip.dcs.dsp.delay.consts.CommonConstant;
import com.ctrip.dcs.dsp.delay.util.JsonUtil;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.qmq.annontation.QmqLogTag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.NeedRetryException;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;

/**
 * 定时匹配任务执行
 */
@Component
public class DelayDspMatchTaskRunConsumer {

    private static final Logger logger = LoggerFactory.getLogger(DelayDspMatchTaskRunConsumer.class);

    private static final String LOCK_KEY_PREFIX = "RUN_DELAY_DSP_TASK_%s_%s";

    @Autowired
    private DelayDspMatchTaskApplicationService delayDspMatchTaskApplicationService;

    @Autowired
    private RedisDistributedLockService distributedLockService;

    @QmqLogTag(tagKeys = {"taskId"})
    @QmqConsumer(prefix = CommonConstant.DELAY_DSP_MATCH_TASK_RUN_SUBJECT, consumerGroup = CommonConstant.APP_ID)
    public void onMessage(Message message) {
        Long taskId = message.getLongProperty("taskId");
        logger.info("DelayDspMatchTaskRunConsumer", JsonUtil.toJson(taskId));
        DLock lock = distributedLockService.getLock(getLockKey(taskId));
        try {
            if (!lock.tryLock()) {
                logger.warn("DelayDspMatchTaskRunConsumer_task_is_executing", JsonUtil.toJson(taskId));
                throw new NeedRetryException(System.currentTimeMillis() + 10 * 1000, "DelayDspTaskRunConsumerRetry");
            }
            DispatchDelayDspTaskRequest request = new DispatchDelayDspTaskRequest();
            request.setTaskId(taskId);
            delayDspMatchTaskApplicationService.dispatch(request);
        } catch (Exception e) {
            logger.error("DelayDspMatchTaskRunConsumer_error", e);
            // 分布式锁默认的续期时长是30秒，所以这里设置为60秒后重试
            // http://conf.ctripcorp.com/pages/viewpage.action?pageId=271843366#Minos%E7%94%A8%E6%88%B7%E4%BD%BF%E7%94%A8%E6%89%8B%E5%86%8C-%E5%85%B3%E4%BA%8E%E5%BC%82%E5%B8%B8%E5%A4%84%E7%90%86
            throw new NeedRetryException(System.currentTimeMillis() + 60 * 1000, "DelayDspMatchTaskRunConsumerRetry");
        } finally {
            lock.unlock();
        }
    }

    public String getLockKey(Long taskId) {
        return String.format(LOCK_KEY_PREFIX, CommonConstant.APP_ID, taskId);
    }
}
