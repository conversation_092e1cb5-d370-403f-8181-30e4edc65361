package com.ctrip.dcs.dsp.delay.interfaces.consumer;

import com.ctrip.dcs.dsp.application.request.UpdateDelayDspOrderRequest;
import com.ctrip.dcs.dsp.application.service.DelayDspOrderApplicationService;
import com.ctrip.dcs.dsp.delay.consts.CommonConstant;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.qmq.annontation.QmqLogTag;
import com.google.common.base.Splitter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;

import java.util.List;

/**
 * <AUTHOR>
 */
@Component
public class MainOrderCancelConsumer {

    private static final Logger logger = LoggerFactory.getLogger(MainOrderCancelConsumer.class);

    @Autowired
    private DelayDspOrderApplicationService delayDspApplicationService;

    @QmqLogTag()
    @QmqConsumer(prefix = CommonConstant.MAIN_ORDER_CANCEL_SUBJECT, consumerGroup = CommonConstant.APP_ID, idempotentChecker = "redisIdempotentChecker")
    public void onMessage(Message message) {
        try {
            String supplyOrderIds = message.getStringProperty("supplyOrderIds");
            if (StringUtils.isBlank(supplyOrderIds)) {
                return;
            }
            List<String> orderIds = Splitter.on(",").splitToList(supplyOrderIds);
            for (String orderId : orderIds) {
                delayDspApplicationService.updateDelayDspOrderForCancel(new UpdateDelayDspOrderRequest(orderId));
            }

        } catch (Exception e) {
            logger.error("consume car.qbest.order.orderstate.ORDER_CANCEL! error!", e);
        }
    }
}
