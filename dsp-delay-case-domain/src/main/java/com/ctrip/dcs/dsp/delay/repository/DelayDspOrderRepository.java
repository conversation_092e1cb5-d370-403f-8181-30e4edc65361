package com.ctrip.dcs.dsp.delay.repository;

import com.ctrip.dcs.dsp.delay.model.DelayDspOrder;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface DelayDspOrderRepository {

    void save(DelayDspOrder order);

    DelayDspOrder queryByOrderId(String orderId);

    DelayDspOrder queryByMainOrderId(String mainOrderId);

    DelayDspOrder queryDelayOrderByMainOrderId(String mainOrderId);

    List<DelayDspOrder> queryByTaskId(Long taskId);

    List<DelayDspOrder> queryByTaskId(Long taskId, Integer isDelay);

    void update(DelayDspOrder order);

    void updateForCancel(DelayDspOrder order);


    List<DelayDspOrder> queryByOrderIds(List<String> orderIds);

    List<DelayDspOrder> query(List<String> driverIds, Date beginTime, Date endTime);

    List<DelayDspOrder> queryByOrderIdsWithTaskId(Long taskId, List<String> collect, Integer limitNum);
}
