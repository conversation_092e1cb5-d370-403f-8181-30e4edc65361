package com.ctrip.dcs.dsp.delay.util;

import com.google.common.collect.Lists;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 */
@Component("springApplicationContext")
public class SpringApplicationContextUtil implements ApplicationContextAware {

    private static ApplicationContext context;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        setContext(applicationContext);
    }

    public static <T> T getBean(String name, Class<T> requiredType) {
        return context.getBean(name, requiredType);
    }

    public static <T> T getBean(Class<T> requiredType) {
        return context.getBean(requiredType);
    }

    public static <T> List<T> getBeansForType(Class<T> requiredType) throws BeansException {
        List<T> list = Lists.newArrayList();
        String[] beanNames = context.getBeanNamesForType(requiredType);
        for (int i = 0; i < beanNames.length; i++) {
            String beanName = beanNames[i];
            list.add(getBean(beanName, requiredType));
        }
        return list;
    }

    private static void setContext(ApplicationContext context) {
        SpringApplicationContextUtil.context = context;
    }
}
