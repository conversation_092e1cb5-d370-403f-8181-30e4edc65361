package com.ctrip.dcs.dsp.delay.qconfig


import com.ctrip.dcs.dsp.delay.qconfig.key.HeadTailLimitKeyVO
import com.ctrip.dcs.dsp.delay.qconfig.key.OrderMileageConfigKeyVO
import com.ctrip.dcs.dsp.delay.qconfig.value.HeadTailLimitValueVO
import com.ctrip.dcs.dsp.delay.qconfig.value.OrderMileageConfigValueVO
import com.google.common.collect.Lists
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 */
class OrderMileageValueConfigSpec extends Specification{

    def map = new HashMap();

    def config = new OrderMileageValueConfig(context: map)

    @Unroll
    def "test"() {
        given: "Mock数据"
        OrderMileageConfigKeyVO keyVO = new OrderMileageConfigKeyVO(cityId, carTypeId)
        OrderMileageConfigValueVO valueVO = new OrderMileageConfigValueVO("", "", "", "", value, "", "")
        map.put(key, new BaseCarConfig.Config(keyVO, Lists.newArrayList(valueVO)))

        when: "执行校验方法"
        def result = config.getValue(1, 1)

        then: "验证校验结果"
        result.getValue() == value

        where:
        key   | cityId | carTypeId || value
        "1#1" | 1      | 1         || "100"
        "1#2" | 1      | 2         || "0"
        "0#0" | 0      | 0         || "100"
        "1#0" | 1      | 0         || "100"
    }
}
