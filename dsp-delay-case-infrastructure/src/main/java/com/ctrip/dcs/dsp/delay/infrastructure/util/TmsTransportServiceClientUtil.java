package com.ctrip.dcs.dsp.delay.infrastructure.util;

import com.ctrip.dcs.tms.transport.api.TmsTransportServiceClient;
import com.ctrip.framework.ucs.client.ShardingKeyValue;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2025/6/3 19:01
 */
@Component
public class TmsTransportServiceClientUtil {
    
    public void setDrvUdl(String udl) {
        TmsTransportServiceClient.getInstance().setShardingKeyValues(ShardingKeyValue.ofUdl(udl), ShardingKeyValue.ofRequestFrom(UdlUtil.getRequestFrom(udl)));
    }
}
