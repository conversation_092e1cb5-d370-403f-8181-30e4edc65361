package com.ctrip.dcs.dsp.delay.carconfig.value;

import com.ctrip.dcs.dsp.delay.carconfig.Value;
import com.ctrip.dcs.dsp.delay.util.DateUtil;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class OrderMileageValue implements Value<OrderMileageValue.Parameter> {

    public static OrderMileageValue DEFAULT = new OrderMileageValue(0, 10000, "00:00", "23:59", 0.0);

    private Integer startKilo;
    private Integer endKilo;
    private String startTime;
    private String endTime;
    private Double value;

    public OrderMileageValue() {
    }

    public OrderMileageValue(Integer startKilo, Integer endKilo, String startTime, String endTime, Double value) {
        this.startKilo = startKilo;
        this.endKilo = endKilo;
        this.startTime = startTime;
        this.endTime = endTime;
        this.value = value;
    }

    @Override
    public boolean match(Parameter parameter) {
        return matchKilo(parameter.getKiloLength()) && matchTime(parameter.getDatetime());
    }

    private boolean matchKilo(double kilo) {
        return kilo >= Double.valueOf(this.getStartKilo()) && kilo < Double.valueOf(this.getEndKilo());
    }

    private boolean matchTime(Date bookTime) {
        String bookTimeStr = DateUtil.formatDate(bookTime, DateUtil.HOUR_MIN_FMT);
        return this.getEndTime().compareTo(this.getStartTime()) > 0 ?
                (bookTimeStr.compareTo(this.getStartTime()) >= 0 && bookTimeStr.compareTo(this.getEndTime()) <= 0) :
                (bookTimeStr.compareTo(this.getEndTime()) <= 0 || bookTimeStr.compareTo(this.getStartTime()) >= 0);

    }

    @Getter
    @Setter
    public static class Parameter {

        private Double kiloLength;
        private Date datetime;

        public Parameter(Double kiloLength, Date datetime) {
            this.kiloLength = kiloLength;
            this.datetime = datetime;
        }
    }
}
