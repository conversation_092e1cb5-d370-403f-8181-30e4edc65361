package com.ctrip.dcs.dsp.delay.enums;

import com.ctrip.igt.framework.common.exception.BizException;

/**
 * order withStatus enum
 */
public enum SupplyOrderStatus {
    ORDER(1),
    SEND_ORDER(2),
    TAKEN(3),
    DRIVER_ARRIVE(4),
    SERVICE_START(5),
    SERVICE_COMPLETE(6),
    PAYMENT_COMFIRMED(7),
    CANCEL(8),
    NO_PAYMENT(9),
    OVER(10),
    DEALING(11);

    private int code;

    public int getCode() {
        return code;
    }

    public static SupplyOrderStatus get(int code) {
        for (SupplyOrderStatus status : SupplyOrderStatus.values()) {
            if (status.code == code) {
                return status;
            }
        }
        throw new BizException("unknown order withStatus code:" + code);
    }

    SupplyOrderStatus(int code) {
        this.code = code;
    }
}
