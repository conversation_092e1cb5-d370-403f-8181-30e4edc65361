package com.ctrip.dcs.dsp.delay.context;


import com.ctrip.dcs.dsp.delay.model.DispatcherConfig;
import com.ctrip.dcs.dsp.delay.model.DelayDspOrder;
import com.ctrip.dcs.dsp.delay.model.DelayDspTask;
import com.ctrip.dcs.dsp.delay.model.DriverAggregation;
import com.ctrip.dcs.dsp.delay.util.JsonUtil;
import com.google.common.collect.Lists;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class DelayDspContext {

    private DelayDspTask delayDspTask;

    private List<DelayDspOrder> orders;

    private List<DriverAggregation> driverAggregations;

    private DispatcherConfig config;

    public DelayDspContext() {
    }

    public DelayDspContext(DelayDspTask delayDspTask, List<DelayDspOrder> orders, List<DriverAggregation> driverAggregations, DispatcherConfig config) {
        this.delayDspTask = delayDspTask;
        this.orders = orders;
        this.driverAggregations = driverAggregations;
        this.config = config;
    }

    @Override
    public String toString() {
        List<String> orderIds = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(orders)) {
            orderIds = orders.stream().map(DelayDspOrder::getOrderId).collect(Collectors.toList());
        }
        List<String> drivers = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(driverAggregations)) {
            drivers = driverAggregations.stream().map(DriverAggregation::toString).collect(Collectors.toList());
        }
        return "DelayDspContext{" +
                "delayDspTask=" + Optional.ofNullable(delayDspTask).map(DelayDspTask::getTaskId).orElse(0L) +
                ", orders=" + JsonUtil.toJson(orderIds) +
                ", driverAggregations=" + drivers +
                ", config=" + JsonUtil.toJson(config) +
                '}';
    }
}
