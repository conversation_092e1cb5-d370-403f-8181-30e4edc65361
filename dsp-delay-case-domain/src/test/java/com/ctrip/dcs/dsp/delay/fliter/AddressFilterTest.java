package com.ctrip.dcs.dsp.delay.fliter;

import com.ctrip.dcs.dsp.delay.filter.impl.AddressFilter;
import com.ctrip.dcs.dsp.delay.model.DelayDspTask;
import com.ctrip.dcs.dsp.delay.model.Driver;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.powermock.modules.junit4.PowerMockRunner;

/**
 * <AUTHOR>
 */
@RunWith(PowerMockRunner.class)
public class AddressFilterTest {

    @InjectMocks
    private AddressFilter filter;

    @Test
    public void test() {
        Driver driver = new Driver();
        driver.setAddressLongitude(1.1);
        driver.setAddressLatitude(1.1);
        DelayDspTask task = new DelayDspTask();
        boolean ok = this.filter.filter(driver, task);
        Assert.assertTrue(ok);
    }
}
