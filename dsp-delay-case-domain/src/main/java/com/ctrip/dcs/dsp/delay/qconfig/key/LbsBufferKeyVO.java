package com.ctrip.dcs.dsp.delay.qconfig.key;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;

@Getter
public class LbsBufferKeyVO implements CarConfigKeyVO {

    private Integer cityId;

    private Integer isWorkDay = 0;

    private Integer ifOutCity = 0;

    @JsonCreator
    public LbsBufferKeyVO(@JsonProperty("cityId") Integer cityId, @JsonProperty("isWorkDay") Integer isWorkDay, @JsonProperty("ifOutCity") Integer ifOutCity) {
        this.cityId = cityId;
        this.isWorkDay = isWorkDay;
        this.ifOutCity = ifOutCity;
    }

    public LbsBufferKeyVO(Integer cityId) {
        this.cityId = cityId;
    }

    @Override
    public String toKey() {
        return cityId + PLACEHOLDER + isWorkDay + PLACEHOLDER + ifOutCity;
    }
}
