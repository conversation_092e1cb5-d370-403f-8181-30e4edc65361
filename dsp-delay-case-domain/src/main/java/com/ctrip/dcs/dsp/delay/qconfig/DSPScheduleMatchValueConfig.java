package com.ctrip.dcs.dsp.delay.qconfig;

import com.ctrip.dcs.dsp.delay.qconfig.key.CarConfigKeyVO;
import com.ctrip.dcs.dsp.delay.qconfig.key.DSPScheduleMatchKeyVO;
import com.ctrip.dcs.dsp.delay.qconfig.value.DSPScheduleMatchValueVO;
import com.ctrip.dcs.dsp.delay.util.MetricsUtil;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import qunar.tc.qconfig.client.spring.QConfig;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Component
public class DSPScheduleMatchValueConfig extends BaseCarConfig<DSPScheduleMatchKeyVO, DSPScheduleMatchValueVO> {

    private static final Logger logger = LoggerFactory.getLogger(DSPScheduleMatchValueConfig.class);


    @QConfig("visualConfig_dspScheduleMatchValueConf_0.json")
    private HashMap<String, Config<DSPScheduleMatchKeyVO, DSPScheduleMatchValueVO>> context;


    @Override
    Map<String, Config<DSPScheduleMatchKeyVO, DSPScheduleMatchValueVO>> getContext() {
        return this.context;
    }
    public DSPScheduleMatchValueVO getValue(String cityId, Integer carTypeId) {
        if(Objects.isNull(cityId) || Objects.isNull(carTypeId)){
            return null;
        }
        List<DSPScheduleMatchValueVO> value = getList(new DSPScheduleMatchKeyVO(cityId, carTypeId));
        if (CollectionUtils.isEmpty(value)) {
            value = getList(new DSPScheduleMatchKeyVO(cityId, CarConfigKeyVO.ALL_CAR_TYPE));
        }
        if (CollectionUtils.isEmpty(value)) {
            value = getList(new DSPScheduleMatchKeyVO(CarConfigKeyVO.ALL_CITY_ID.toString(), carTypeId));
        }
        if (CollectionUtils.isEmpty(value)) {
            value = getList(new DSPScheduleMatchKeyVO(CarConfigKeyVO.ALL_CITY_ID.toString(), CarConfigKeyVO.ALL_CAR_TYPE));
        }
        if(CollectionUtils.isEmpty(value)){
            logger.info("DSPScheduleMatchValueConfig_getValue_null" ,"cityId:{}, carTypeId:{}, value:{}", cityId, carTypeId, value);
            MetricsUtil.recordValue("DSPScheduleMatchValueConfig.null", 1);
            return null;
        }
        return value.get(0);
    }
}
