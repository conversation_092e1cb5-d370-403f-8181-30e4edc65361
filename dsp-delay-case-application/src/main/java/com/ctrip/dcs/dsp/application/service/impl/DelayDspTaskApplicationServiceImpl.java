package com.ctrip.dcs.dsp.application.service.impl;

import com.ctrip.dcs.dsp.application.request.SaveAdjacencyRouteRequest;
import com.ctrip.dcs.dsp.application.service.DelayDspTaskApplicationService;
import com.ctrip.dcs.dsp.delay.cache.DistributedCache;
import com.ctrip.dcs.dsp.delay.enums.YesOrNo;
import com.ctrip.dcs.dsp.delay.factory.ModelFactory;
import com.ctrip.dcs.dsp.delay.gateway.GeoGateway;
import com.ctrip.dcs.dsp.delay.model.*;
import com.ctrip.dcs.dsp.delay.repository.DelayDspOrderRepository;
import com.ctrip.dcs.dsp.delay.repository.DelayDspTaskRepository;
import com.ctrip.dcs.dsp.delay.service.DriverService;
import com.ctrip.dcs.dsp.delay.util.DateUtil;
import com.ctrip.dcs.dsp.delay.util.JsonUtil;
import com.ctrip.dcs.dsp.delay.util.MetricsUtil;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Random;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
public class DelayDspTaskApplicationServiceImpl implements DelayDspTaskApplicationService {

    private static final Logger logger = LoggerFactory.getLogger(DelayDspTaskApplicationServiceImpl.class);


    @Autowired
    private DriverService driverService;

    @Autowired
    private DelayDspTaskRepository delayDspTaskRepository;

    @Autowired
    private DelayDspOrderRepository delayDspOrderRepository;

    @Autowired
    private GeoGateway geoGateway;

    @Autowired
    private DistributedCache distributedCache;

    @Override
    public void saveAdjacencyEdge(SaveAdjacencyRouteRequest request) {
        try {
            DelayDspTask task = delayDspTaskRepository.queryByTaskId(request.getTaskId());
            if (Objects.isNull(task) || !task.isUnExecuted()) {
                logger.info("DelayDispatchApplicationServiceImpl.saveAdjacencyRoute", "task has executed.order: {}, task:{}", request.getOrderId(), JsonUtil.toJson(task));
                return;
            }
            DelayDspOrder order = delayDspOrderRepository.queryByOrderId(request.getOrderId());
            if (Objects.isNull(order) || Objects.equals(order.getIsCancel(), YesOrNo.YES.getCode())) {
                logger.info("DelayDispatchApplicationServiceImpl.saveAdjacencyRoute", "order is cancel.order: {}, task:{}", request.getOrderId(), JsonUtil.toJson(task));
                return;
            }
            // 查询相同任务的邻接订单
            List<DelayDspOrder> orders = delayDspOrderRepository.queryByTaskId(task.getTaskId());
            List<Driver> drivers = driverService.queryDriver(task);
            saveAdjacencyEdge(task, orders, order, drivers);
        } catch (Exception e) {
            MetricsUtil.recordValue("save.adjacency.route.error", 1);
            logger.error("save route error", e);
        }
    }

    public void saveAdjacencyEdge(DelayDspTask task, List<DelayDspOrder> adjacency, DelayDspOrder order, List<Driver> drivers) {
        if (Objects.equals(order.getIsDelay(), YesOrNo.NO.getCode())) {
            drivers = drivers.stream()
                    .filter(d -> Objects.equals(d.getDriverId(), order.getDriverId()))
                    .collect(Collectors.toList());
        }
        List<Position> positions = ModelFactory.buildPositions(task, order, adjacency, drivers);
        // 过滤已经缓存了的点位
        positions = filter(task, positions);
        if (CollectionUtils.isEmpty(positions)) {
            return;
        }
        List<Route> routes = geoGateway.queryRoutes(task.getCityId(), order.getOrderId(), positions);
        cacheEdges(task, routes);
    }

    private void cacheEdges(DelayDspTask task, List<Route> routes) {
        String[] kv = new String[routes.size() * 2];
        for (int i = 0; i < routes.size(); i++) {
            Route route = routes.get(i);
            int j = 2 * i;
            kv[j] =  Route.toKey(task.getTaskId(), route.getHash());
            kv[j + 1] = Route.toValue(route.getDistance(), route.getDuration());
        }
        int expire = (int) DateUtil.secondsDiff(new Date(), task.getExecuteTimeDeadline()) + new Random().nextInt(3600);
        distributedCache.mset(expire, kv);
    }

    private List<Position> filter(DelayDspTask task, List<Position> positions) {
        if (CollectionUtils.isEmpty(positions)) {
            return Lists.newArrayList();
        }
        List<Position> result = Lists.newArrayList();

        List<String> key = positions.stream()
                .map(p -> Route.toKey(task.getTaskId(), p.hash()))
                .collect(Collectors.toList());

        List<String> values = distributedCache.mget(key);

        for (int i = 0; i < values.size(); i++) {
            String v = values.get(i);
            if (StringUtils.isBlank(v)) {
                result.add(positions.get(i));
            }
        }
        return result;
    }

}
