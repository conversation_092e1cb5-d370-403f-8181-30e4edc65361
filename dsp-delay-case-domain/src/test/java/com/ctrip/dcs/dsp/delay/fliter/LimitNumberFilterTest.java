package com.ctrip.dcs.dsp.delay.fliter;

import com.ctrip.dcs.dsp.delay.filter.impl.LimitNumberFilter;
import com.ctrip.dcs.dsp.delay.gateway.TrafficControlGateway;
import com.ctrip.dcs.dsp.delay.model.DelayDspTask;
import com.ctrip.dcs.dsp.delay.model.Driver;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@RunWith(PowerMockRunner.class)
public class LimitNumberFilterTest {

    @InjectMocks
    private LimitNumberFilter filter;

    @Mock
    private TrafficControlGateway trafficControlGateway;

    @Mock
    private Map<String, String> commonConf;

    @Test
    public void test() {
        Driver driver1 = new Driver();
        driver1.setDriverId("1");
        driver1.setIsEnergy(1);
        driver1.setCarLicense("6666");
        Driver driver2 = new Driver();
        driver2.setDriverId("2");
        driver2.setIsEnergy(0);
        driver2.setCarLicense("8888");
        Driver driver3 = new Driver();
        driver3.setDriverId("3");
        driver3.setIsEnergy(0);
        driver3.setCarLicense("8889");
        DelayDspTask task = new DelayDspTask();
        task.setCityId(1);
        task.setCarTypeId(117);
        task.setBeginTime(new Date());
        task.setEndTime(new Date());

        PowerMockito.when(trafficControlGateway.queryLimitLicense(Mockito.anyInt(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(Sets.newHashSet("8888"));
        List<Driver> drivers = this.filter.doFilter(Lists.newArrayList(driver1, driver2, driver3), task);

        Mockito.when(commonConf.get(Mockito.any())).thenReturn("1");
        drivers = this.filter.doFilter(Lists.newArrayList(driver1, driver2, driver3), task);
        Mockito.when(commonConf.get(Mockito.any())).thenReturn("2");
        drivers = this.filter.doFilter(Lists.newArrayList(driver1, driver2, driver3), task);
        Mockito.when(commonConf.get(Mockito.any())).thenReturn("0");
        drivers = this.filter.doFilter(Lists.newArrayList(driver1, driver2, driver3), task);
        Assert.assertTrue(drivers.size()>=0);


    }
}
