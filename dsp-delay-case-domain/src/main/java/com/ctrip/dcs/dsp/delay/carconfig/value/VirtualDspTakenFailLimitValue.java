package com.ctrip.dcs.dsp.delay.carconfig.value;

import com.ctrip.dcs.dsp.delay.carconfig.Value;
import com.ctrip.dcs.dsp.delay.util.DateUtil;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class VirtualDspTakenFailLimitValue implements Value<Date> {

    private String startTime;

    private String endTime;

    private Integer limitedValue;

    @Override
    public boolean match(Date date) {
        String bookTimeStr = DateUtil.formatDate(date,DateUtil.HOUR_MIN_FMT);
        return  this.getEndTime().compareTo(this.getStartTime()) > 0 ?
                (bookTimeStr.compareTo(this.getStartTime()) >= 0 && bookTimeStr.compareTo(this.getEndTime()) <= 0) :
                (bookTimeStr.compareTo(this.getEndTime()) <= 0 || bookTimeStr.compareTo(this.getStartTime()) >= 0);
    }
}
