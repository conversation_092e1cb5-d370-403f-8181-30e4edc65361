package com.ctrip.dcs.dsp.delay.validator;

import com.ctrip.dcs.dsp.delay.enums.SupplyOrderStatus;
import com.ctrip.dcs.dsp.delay.model.SupplyOrder;
import com.ctrip.dcs.dsp.delay.qconfig.DelayDspCommonQConfig;
import com.ctrip.dcs.dsp.delay.util.SpringApplicationContextUtil;
import com.ctrip.dcs.dsp.delay.validator.impl.DriverValidator;
import com.ctrip.dcs.dsp.delay.validator.impl.OrderValidator;
import com.google.common.collect.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

/**
 * <AUTHOR>
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest(value = {SpringApplicationContextUtil.class})
public class ValidatorChainTest {

    @InjectMocks
    private ValidatorChain chain;

    @Mock
    private DelayDspCommonQConfig delayDspCommonQConfig;

    @Mock
    private OrderValidator orderValidator;

    @Mock
    private DriverValidator driverValidator;

    @Test
    public void testOK() {
        SupplyOrder order = new SupplyOrder();
        order.setIsOtaBookOrder(0);
        order.setCategoryCode("airport_pickup");
        order.setOrderStatus(SupplyOrderStatus.SEND_ORDER.getCode());
        PowerMockito.when(delayDspCommonQConfig.getDelayDspValidatorsList()).thenReturn(Lists.newArrayList("orderValidator"));
        ValidatorDTO validatorDTO = new ValidatorDTO();
        validatorDTO.setCode(ValidatorCode.OK);
        PowerMockito.when(orderValidator.validate(order)).thenReturn(validatorDTO);
        PowerMockito.mockStatic(SpringApplicationContextUtil.class);
        PowerMockito.when(SpringApplicationContextUtil.getBean("orderValidator", Validator.class)).thenReturn(orderValidator);
        ValidatorDTO code = chain.validate(order);
        Assert.assertEquals(ValidatorCode.OK, code.getCode());
    }

    @Test
    public void testNotOK() {
        SupplyOrder order = new SupplyOrder();
        order.setOrderId("123");
        order.setIsOtaBookOrder(0);
        order.setCategoryCode("airport_pickup");
        order.setOrderStatus(SupplyOrderStatus.TAKEN.getCode());
        order.setDriverId("1");
        PowerMockito.when(delayDspCommonQConfig.getDelayTakenValidatorsList()).thenReturn(Lists.newArrayList("driverValidator"));
        ValidatorDTO validatorDTO = new ValidatorDTO();
        validatorDTO.setCode(ValidatorCode.NOT_FULL_TIME_DRIVER);
        PowerMockito.when(driverValidator.validate(order)).thenReturn(validatorDTO);
        PowerMockito.mockStatic(SpringApplicationContextUtil.class);
        PowerMockito.when(SpringApplicationContextUtil.getBean("driverValidator", Validator.class)).thenReturn(driverValidator);
        ValidatorDTO code = chain.validate(order);
        Assert.assertEquals(ValidatorCode.NOT_FULL_TIME_DRIVER, code.getCode());
    }
}
