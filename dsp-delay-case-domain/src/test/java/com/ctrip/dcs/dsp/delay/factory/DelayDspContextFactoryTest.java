package com.ctrip.dcs.dsp.delay.factory;

import com.ctrip.dcs.dsp.delay.context.DelayDspContext;
import com.ctrip.dcs.dsp.delay.model.DelayDspOrder;
import com.ctrip.dcs.dsp.delay.model.DelayDspTask;
import com.ctrip.dcs.dsp.delay.qconfig.DriverProfitBaselineConfig;
import com.ctrip.dcs.dsp.delay.qconfig.HeadTailLimitConfig;
import com.ctrip.dcs.dsp.delay.qconfig.LbsBufferConfig;
import com.ctrip.dcs.dsp.delay.qconfig.OrderMileageValueConfig;
import com.ctrip.dcs.dsp.delay.qconfig.value.HeadTailLimitValueVO;
import com.ctrip.dcs.dsp.delay.qconfig.value.LbsBufferValueVO;
import com.ctrip.dcs.dsp.delay.qconfig.value.OrderMileageConfigValueVO;
import com.ctrip.dcs.dsp.delay.service.DispatchConfigService;
import com.ctrip.dcs.dsp.delay.service.DriverService;
import com.google.common.collect.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 */
@RunWith(PowerMockRunner.class)
public class DelayDspContextFactoryTest {

    @InjectMocks
    private DelayDspContextFactory factory;

    @Mock
    private DriverService driverService;

    @Mock
    private DispatchConfigService dispatchConfigService;

    @Mock
    private DriverProfitBaselineConfig driverProfitBaselineConfig;

    @Mock
    private OrderMileageValueConfig orderMileageValueConfig;

    @Mock
    private LbsBufferConfig lbsBufferConfig;

    @Mock
    private HeadTailLimitConfig headTailLimitConfig;

    @Mock
    private DelayDspTask delayDspTask;

    @Mock
    private DelayDspOrder delayDspOrder;

    @Mock
    private OrderMileageConfigValueVO orderMileageConfigValueVO;

    @Mock
    private LbsBufferValueVO lbsBufferValueVO;

    @Mock
    private HeadTailLimitValueVO headTailLimitValueVO;

    @Test
    public void test() {
        PowerMockito.when(orderMileageValueConfig.getValue(Mockito.any(), Mockito.any())).thenReturn(orderMileageConfigValueVO);
        PowerMockito.when(orderMileageConfigValueVO.getStartKilo()).thenReturn("1");
        PowerMockito.when(orderMileageConfigValueVO.getEndKilo()).thenReturn("1");
        PowerMockito.when(orderMileageConfigValueVO.getValue()).thenReturn("1");
        PowerMockito.when(lbsBufferConfig.getValue(Mockito.any())).thenReturn(lbsBufferValueVO);
        PowerMockito.when(lbsBufferValueVO.getValue()).thenReturn(1D);
        PowerMockito.when(lbsBufferValueVO.getType()).thenReturn(1);
        PowerMockito.when(headTailLimitConfig.getHeadTailLimitVO(Mockito.any(), Mockito.any())).thenReturn(headTailLimitValueVO);
        PowerMockito.when(headTailLimitValueVO.getHeadLimit()).thenReturn(1D);
        PowerMockito.when(headTailLimitValueVO.getTailLimit()).thenReturn(1D);
        DelayDspContext context = factory.createDelayDspContext(delayDspTask, Lists.newArrayList(delayDspOrder));
        Assert.assertNotNull(context);
    }
}
