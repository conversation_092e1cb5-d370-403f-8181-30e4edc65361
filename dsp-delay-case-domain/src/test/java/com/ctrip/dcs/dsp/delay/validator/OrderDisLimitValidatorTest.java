package com.ctrip.dcs.dsp.delay.validator;

import com.ctrip.dcs.dsp.delay.carconfig.CarConfig;
import com.ctrip.dcs.dsp.delay.carconfig.CarConfigCode;
import com.ctrip.dcs.dsp.delay.carconfig.value.OrderDisLimitValue;
import com.ctrip.dcs.dsp.delay.gateway.CarConfigGateway;
import com.ctrip.dcs.dsp.delay.model.PredictPriceInfo;
import com.ctrip.dcs.dsp.delay.model.SupplyOrder;
import com.ctrip.dcs.dsp.delay.validator.impl.OrderDisLimitValidator;
import com.google.common.collect.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.modules.junit4.PowerMockRunner;

/**
 * <AUTHOR>
 */
@RunWith(PowerMockRunner.class)
public class OrderDisLimitValidatorTest {

    @InjectMocks
    private OrderDisLimitValidator orderDisLimitValidator;

    @Mock
    private CarConfigGateway carConfigGateway;

    @Mock
    private CarConfig carConfig;

    @Test
    public void testNotMatch() {
        PredictPriceInfo info = new PredictPriceInfo();
        info.setKiloLength(10D);
        SupplyOrder order = new SupplyOrder();
        order.setCityCode("beijing_city");
        order.setCarTypeId(117);
        order.setPredictPriceInfo(info);
        OrderDisLimitValue config = new OrderDisLimitValue();
        config.setInUse(1);
        config.setLimitDisLower(0);
        config.setLimitDisUpper(9);
        PowerMockito.when(carConfigGateway.query(CarConfigCode.ORDER_DIS_LIMIT.getCode(), CarConfigCode.ORDER_DIS_LIMIT.getVersion())).thenReturn(carConfig);
        PowerMockito.when(carConfig.values(Mockito.any(), Mockito.any())).thenReturn(Lists.newArrayList(config));
        ValidatorDTO code = orderDisLimitValidator.validate(order);
        Assert.assertEquals(ValidatorCode.OK, code.getCode());
    }

    @Test
    public void testMatch() {
        PredictPriceInfo info = new PredictPriceInfo();
        info.setKiloLength(10D);
        SupplyOrder order = new SupplyOrder();
        order.setCityCode("beijing_city");
        order.setCarTypeId(117);
        order.setPredictPriceInfo(info);
        OrderDisLimitValue config = new OrderDisLimitValue();
        config.setInUse(1);
        config.setLimitDisLower(0);
        config.setLimitDisUpper(99);
        PowerMockito.when(carConfigGateway.query(CarConfigCode.ORDER_DIS_LIMIT.getCode(), CarConfigCode.ORDER_DIS_LIMIT.getVersion())).thenReturn(carConfig);
        PowerMockito.when(carConfig.values(Mockito.any(), Mockito.any())).thenReturn(Lists.newArrayList(config));
        ValidatorDTO code = orderDisLimitValidator.validate(order);
        Assert.assertEquals(ValidatorCode.ORDER_DIS_LIMIT, code.getCode());
    }
}
