package com.ctrip.dcs.dsp.delay.qconfig.value;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;

@Getter
public class HeadTailLimitValueVO implements CarConfigValueVO {
    private Double headLimit;

    private Double tailLimit;

    private Double tailMileageValue;

    @JsonCreator
    public HeadTailLimitValueVO(@JsonProperty("headLimit") Double headLimit, @JsonProperty("tailLimit") Double tailLimit, @JsonProperty("tailMileageValue") Double tailMileageValue) {
        this.headLimit = headLimit;
        this.tailLimit = tailLimit;
        this.tailMileageValue = tailMileageValue;
    }
}
