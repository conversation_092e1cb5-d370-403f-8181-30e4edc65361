package com.ctrip.dcs.dsp.delay.infrastructure.redis;

import com.ctrip.dcs.dsp.delay.infrastructure.trocks.TRocksProviderProxy;
import com.ctrip.dcs.dsp.delay.lock.DistributedLock;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import credis.java.client.CacheProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
public class RedisProviderProxy implements DistributedLock {

    private static final Logger logger = LoggerFactory.getLogger(RedisProviderProxy.class);

    @Qualifier("redisCacheProvider")
    @Autowired
    private CacheProvider cacheProvider;

    @Override
    public boolean tryAcquire(String key, String value, long seconds) {
        try {
            Boolean ok = cacheProvider.setnx(key, value);
            if (ok) {
                cacheProvider.expire(key, seconds);
            }
            return ok;
        } catch (Exception e) {
            logger.warn("RedisProviderProxy", "tryAcquire error! key:{}, error:{}", key, e);
            return false;
        }
    }

    @Override
    public void release(String key) {
        cacheProvider.del(key);
    }
}
