package com.ctrip.dcs.dsp.delay.qconfig;

import com.ctrip.dcs.dsp.delay.qconfig.key.CarConfigKeyVO;
import com.ctrip.dcs.dsp.delay.qconfig.key.HeadTailLimitKeyVO;
import com.ctrip.dcs.dsp.delay.qconfig.value.HeadTailLimitValueVO;
import org.springframework.stereotype.Component;
import qunar.tc.qconfig.client.spring.QConfig;

import java.util.Map;
import java.util.Objects;

@Component
public class HeadTailLimitConfig extends BaseCarConfig<HeadTailLimitKeyVO, HeadTailLimitValueVO>{
    private static final HeadTailLimitValueVO DEFAULT_VALUE = new HeadTailLimitValueVO(-1D, -1D, -1D);

    @QConfig("visualConfig_headTailLimit_1.json")
    Map<String, Config<HeadTailLimitKeyVO, HeadTailLimitValueVO>> configMap;

    @Override
    Map<String, Config<HeadTailLimitKeyVO, HeadTailLimitValueVO>> getContext() { return configMap; }

    public HeadTailLimitValueVO getHeadTailLimitVO(Integer cityId, Integer carTypeId) {
        return get(cityId, carTypeId);
    }

    private HeadTailLimitValueVO get(Integer cityId, Integer carTypeId) {
        HeadTailLimitValueVO valueVO = get(new HeadTailLimitKeyVO(cityId, carTypeId));
        if (Objects.isNull(valueVO)) {
            valueVO = get(new HeadTailLimitKeyVO(cityId, CarConfigKeyVO.ALL_CAR_TYPE));
        }
        if (Objects.isNull(valueVO)) {
            valueVO = get(new HeadTailLimitKeyVO(CarConfigKeyVO.ALL_CITY_ID, CarConfigKeyVO.ALL_CAR_TYPE));
        }
        return Objects.isNull(valueVO) ? DEFAULT_VALUE : valueVO;
    }

}
