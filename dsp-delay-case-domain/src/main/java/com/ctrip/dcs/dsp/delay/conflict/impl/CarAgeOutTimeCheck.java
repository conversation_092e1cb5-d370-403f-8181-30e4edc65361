package com.ctrip.dcs.dsp.delay.conflict.impl;

import com.ctrip.dcs.dsp.delay.conflict.Check;
import com.ctrip.dcs.dsp.delay.conflict.CheckCode;
import com.ctrip.dcs.dsp.delay.conflict.ConflictContext;
import com.ctrip.dcs.dsp.delay.enums.YesOrNo;
import com.ctrip.dcs.dsp.delay.factory.DPFactory;
import com.ctrip.dcs.dsp.delay.model.DelayDspOrder;
import com.ctrip.dcs.dsp.delay.util.DateUtil;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Objects;

/**
 * 车辆超龄检查
 * <AUTHOR>
 */
@Component("carAgeOutTimeCheck")
public class CarAgeOutTimeCheck implements Check {

    private static final Logger logger = LoggerFactory.getLogger(DPFactory.class);

    @Override
    public CheckCode check(ConflictContext context) {
        try{
            DelayDspOrder order = context.getNext();
            if(Objects.isNull(order) || Objects.isNull(order.getIsDelay())){
                return CheckCode.OK;
            }
            if(YesOrNo.NO.getCode() == order.getIsDelay()){
                return CheckCode.OK;
            }
            Date sysExpectBookTime = order.getSysExpectBookTime();
            Date overAgeTime = DateUtil.parseDate(context.getDriverAggregation().getDriver().getOverAgeTime(), DateUtil.DATE_FMT);
            if(Objects.isNull(overAgeTime) || Objects.isNull(sysExpectBookTime)){
                return CheckCode.OK;
            }
            if(sysExpectBookTime.after(overAgeTime)){
                return CheckCode.CAR_OVER_AGE;
            }
        }catch (Exception ex){
            logger.error("CarAgeOutTimeCheck_error", ex);
        }
        return CheckCode.OK;
    }
}
