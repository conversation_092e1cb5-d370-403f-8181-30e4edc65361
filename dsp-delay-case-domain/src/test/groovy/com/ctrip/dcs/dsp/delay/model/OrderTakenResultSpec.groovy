package com.ctrip.dcs.dsp.delay.model

import com.ctrip.dcs.dsp.delay.qconfig.BaseCarConfig
import com.ctrip.dcs.dsp.delay.qconfig.key.DriverHighProfitLineDayKeyVO
import com.ctrip.dcs.dsp.delay.qconfig.value.DriverHighProfitLineDayValueVO
import com.google.common.collect.Lists
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 */
class OrderTakenResultSpec extends Specification {

    @Unroll
    def "test"() {
        given: "Mock数据"
        OrderTakenResult result = new OrderTakenResult(code: code, notTakenCode: notTakenCode)

        when: "执行校验方法"
        def res = result.getResultCode()

        then: "验证校验结果"
        res == value

        where:
        code | notTakenCode || value
        1    | 1            || 1
        3    | 2            || 2
    }
}
