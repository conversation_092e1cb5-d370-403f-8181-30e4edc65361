package com.ctrip.dcs.dsp.delay.model;

import com.ctrip.dcs.dsp.delay.enums.SupplyOrderStatus;
import com.ctrip.dcs.dsp.delay.enums.SupplyOrderStatusDetail;
import com.ctrip.dcs.dsp.delay.enums.YesOrNo;
import com.google.common.collect.Sets;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.Objects;
import java.util.Set;

/**
 * 订单
 * <AUTHOR>
 */
@Getter
@Setter
public class SupplyOrder {

    private String orderId;

    private String sourceOrderId;

    private String cityCode;

    private Integer cityId;

    private Integer carTypeId;

    private Integer serviceType;

    private Integer orderStatus;

    private Integer orderStatusDetail;

    private Date sysExpectBookTime;

    private Date predicServiceStopTime;

    private Integer isOtaBookOrder;

    private String driverId;

    private Long transportGroupId;

    private Long supplierId;

    private Long skuId;

    private String categoryCode;

    private Long distributionChannel;

    /**
     * 司机应获取金额
     */
    private double driverOrderFee;

    /**
     * 原始出发地经度
     */
    private Double actualFromLongitude;
    /**
     * 原始出发地纬度
     */
    private Double actualFromLatitude;
    /**
     * 原始出发地坐标系
     */
    private String actualFromCoordsys;
    /**
     * 原始目的地经度
     */
    private Double actualToLongitude;
    /**
     * 原始目的地纬度
     */
    private Double actualToLatitude;
    /**
     * 原始目的地坐标系
     */
    private String actualToCoordsys;

    private String uid;

    private PredictPriceInfo predictPriceInfo;

    // 附加服务
    private Set<String> orderPackageServiceCodes;

    /**
     * 高等级用户订单标记：1=是；0=否
     */
    private Integer highGradeOrder;

    private Integer bizAreaType;

    private Integer orderSysType;
    
    /**
     * 是否未同步的KM订单
     */
    private boolean isSyncKmOrder;

    private Integer isVipUserLevel;

    /**
     * 出发地carplaceId
     */
    private String deptCarPlaceId;
    /**
     * 目的地carplaceId
     */
    private String arriveCarPlaceId;

    private Integer isDowngradeCarType;

    /**
     * 是否短公里订单：0否1是
     */
    private Integer shortDisOrder;

    public boolean isCancel() {
        return Objects.equals(this.getOrderStatus(), SupplyOrderStatus.CANCEL.getCode());
    }

    public boolean isTaken() {
        Set<Integer> set = Sets.newHashSet(
                SupplyOrderStatus.TAKEN.getCode(),
                SupplyOrderStatus.DRIVER_ARRIVE.getCode(),
                SupplyOrderStatus.SERVICE_START.getCode(),
                SupplyOrderStatus.SERVICE_COMPLETE.getCode(),
                SupplyOrderStatus.PAYMENT_COMFIRMED.getCode()
        );
        if (!set.contains(this.getOrderStatus())) {
            return false;
        }
        if (StringUtils.isBlank(this.getDriverId()) || "0".equals(this.getDriverId())) {
            return false;
        }
        return true;
    }

    public boolean isServiceProviderConfirm() {
        return Objects.equals(this.orderStatus, SupplyOrderStatus.SEND_ORDER.getCode()) && Objects.equals(this.orderStatusDetail, SupplyOrderStatusDetail.SERVICE_CONFIRM.getCode());
    }

    public boolean isVip() {
        return Objects.equals(isVipUserLevel, YesOrNo.YES.getCode());
    }

    public boolean isDowngradeCarType() {
        return Objects.equals(isDowngradeCarType, YesOrNo.YES.getCode());
    }
}
