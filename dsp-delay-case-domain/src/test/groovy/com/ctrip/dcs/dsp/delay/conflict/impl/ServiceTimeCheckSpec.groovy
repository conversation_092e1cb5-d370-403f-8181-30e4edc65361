package com.ctrip.dcs.dsp.delay.conflict.impl

import com.ctrip.dcs.dsp.delay.conflict.CheckCode
import com.ctrip.dcs.dsp.delay.conflict.ConflictContext
import com.ctrip.dcs.dsp.delay.model.DelayDspOrder
import com.ctrip.dcs.dsp.delay.util.DateUtil
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 */
class ServiceTimeCheckSpec extends Specification {

    def check = new ServiceTimeCheck()

    @Unroll
    def "check"() {

        given: "Mock数据"
        def prev = new DelayDspOrder(sysExpectBookTime: DateUtil.parseDate(t1), predictServiceStopTime: DateUtil.parseDate(t2))
        def next = new DelayDspOrder(sysExpectBookTime: DateUtil.parseDate(t3), predictServiceStopTime: DateUtil.parseDate(t4))
        def context = new ConflictContext(prev: prev, next: next)

        when: "执行校验方法"
        def res = check.check(context)

        then: "验证校验结果"
        res == code

        where:
        t1                    | t2                    | t3                    | t4                    || code
        "2022-08-29 12:00:00" | "2022-08-29 12:30:00" | "2022-08-29 13:00:00" | "2022-08-29 12:30:00" || CheckCode.OK
        "2022-08-29 12:00:00" | "2022-08-29 13:30:00" | "2022-08-29 13:00:00" | "2022-08-29 14:30:00" || CheckCode.SERVICE_TIME_CONFLICT
        "2022-08-29 12:00:00" | "2022-08-29 13:30:00" | "2022-08-29 11:00:00" | "2022-08-29 12:30:00" || CheckCode.SERVICE_TIME_CONFLICT
        "2022-08-29 14:00:00" | "2022-08-29 14:30:00" | "2022-08-29 13:00:00" | "2022-08-29 12:30:00" || CheckCode.OK
        "2022-08-29 14:00:00" | "2022-08-29 15:30:00" | "2022-08-29 14:10:00" | "2022-08-29 14:30:00" || CheckCode.SERVICE_TIME_CONFLICT
        "2022-08-29 14:10:00" | "2022-08-29 14:30:00" | "2022-08-29 14:00:00" | "2022-08-29 54:30:00" || CheckCode.SERVICE_TIME_CONFLICT
    }
}
