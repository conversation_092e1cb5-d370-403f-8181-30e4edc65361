package com.ctrip.dcs.dsp.delay.interfaces.consumer;

import com.ctrip.dcs.dsp.application.request.UpdateDelayDspOrderRequest;
import com.ctrip.dcs.dsp.application.service.DelayDspOrderApplicationService;
import com.ctrip.dcs.dsp.delay.consts.CommonConstant;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.qmq.annontation.QmqLogTag;
import com.google.common.base.Splitter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;

import java.util.List;

/**
 * <AUTHOR>
 */
@Component
public class OrderCancelConsumer {

    private static final Logger logger = LoggerFactory.getLogger(OrderCancelConsumer.class);

    @Autowired
    private DelayDspOrderApplicationService delayDspApplicationService;

    @QmqLogTag()
    @QmqConsumer(prefix = CommonConstant.ORDER_CANCEL_SUBJECT, consumerGroup = CommonConstant.APP_ID, idempotentChecker = "redisIdempotentChecker")
    public void onMessage(Message message) {
        try {
            String supplyOrderIds = message.getStringProperty("supplyOrderIds");
            if (StringUtils.isBlank(supplyOrderIds)) {
                return;
            }
            List<String> orderIds = Splitter.on(",").splitToList(supplyOrderIds);
            for (String orderId : orderIds) {
                delayDspApplicationService.updateDelayDspOrderForCancel(new UpdateDelayDspOrderRequest(orderId));
            }

        } catch (Exception e) {
            logger.error("consume car.qbest.order.orderstate.SUPPLY_ORDER_CANCEL! error!", e);
        }
    }

    @QmqLogTag()
    @QmqConsumer(prefix = CommonConstant.DSP_ORDER_CANCEL_TOPIC, consumerGroup = CommonConstant.APP_ID)
    public void onMessage1(Message message) {
        try {
            String dspOrderId = message.getStringProperty("dspOrderId");
            if (StringUtils.isBlank(dspOrderId)) {
                return;
            }
            delayDspApplicationService.updateDelayDspOrderForCancel(new UpdateDelayDspOrderRequest(dspOrderId));

        } catch (Exception e) {
            logger.error("consume car.qbest.order.orderstate.ORDER_TAKEN! error!", e);
        }
    }
}
