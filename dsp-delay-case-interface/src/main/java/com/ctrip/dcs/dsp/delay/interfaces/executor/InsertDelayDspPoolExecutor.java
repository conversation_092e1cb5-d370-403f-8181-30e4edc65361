package com.ctrip.dcs.dsp.delay.interfaces.executor;

import com.ctrip.dcs.dsp.application.service.InsertDelayDspPoolApplicationService;
import com.ctrip.dcs.dsp.delay.service.interfaces.message.InsertDelayDspPoolRequestType;
import com.ctrip.dcs.dsp.delay.service.interfaces.message.InsertDelayDspPoolResponseType;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.annontation.ServiceLogTag;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@ServiceLogTag(tagKeys = {"orderId"})
public class InsertDelayDspPoolExecutor extends AbstractRpcExecutor<InsertDelayDspPoolRequestType, InsertDelayDspPoolResponseType> implements Validator<InsertDelayDspPoolRequestType> {

    @Autowired
    private InsertDelayDspPoolApplicationService insertDelayDspPoolApplicationService;

    @Override
    public InsertDelayDspPoolResponseType execute(InsertDelayDspPoolRequestType request) {
        InsertDelayDspPoolResponseType response = insertDelayDspPoolApplicationService.insertDelayDspPool(request);
        return ServiceResponseUtils.success(response);
    }

    @Override
    public void validate(AbstractValidator<InsertDelayDspPoolRequestType> validator) {
        validator.ruleFor("orderId").notNull().notEmpty();
    }
}
