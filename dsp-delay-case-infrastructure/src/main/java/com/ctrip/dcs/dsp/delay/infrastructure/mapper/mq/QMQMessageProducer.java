package com.ctrip.dcs.dsp.delay.infrastructure.mapper.mq;

import com.ctrip.dcs.dsp.delay.mq.MessageProducer;
import com.ctrip.dcs.dsp.delay.util.JsonUtil;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.qmq.producer.ProducerProviderSingleton;
import com.ctrip.igt.framework.qmq.producer.PropertiesMapCopier;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.producer.MessageProducerProvider;

import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Component("qmqMessageProducer")
public class QMQMessageProducer implements MessageProducer {

    private static final Logger logger = LoggerFactory.getLogger(QMQMessageProducer.class);

    @Override
    public void sendMessage(String subject, Map<String, Object> data) {
        sendDelayMessage(subject, data, 0);
    }

    @Override
    public void sendDelayMessage(String subject, Map<String, Object> data, long delay) {
        MessageProducerProvider producerProvider = ProducerProviderSingleton.getInstance();
        Message message = producerProvider.generateMessage(subject);
        data.forEach((k, v) -> PropertiesMapCopier.copy(message, k, v));
        if (delay > 0) {
            message.setDelayTime(delay, TimeUnit.MILLISECONDS);
        }
        logger.info("qmq producer send message", "subject is {}, messageId is {},data is {}, delay is {}", subject, message.getMessageId(), JsonUtil.toJson(data), delay);
        producerProvider.sendMessage(message);
    }
}
