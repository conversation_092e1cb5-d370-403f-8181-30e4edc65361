package com.ctrip.dcs.dsp.delay.model;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class PredictPriceInfo {

    /**
     * 订单号
     */
    private String orderId;

    /**
     * 基础报价id
     */
    private Integer standPriceId;

    /**
     * 标准计价总费用
     */
    private Double standTotalFee;

    /**
     * 用户预估应支付金额
     */
    private Double userOrderFee;

    /**
     * 预估公里数
     */
    private Double kiloLength;

    /**
     * 预估时长
     */
    private Integer timeLength;

    /**
     * 缓行时长
     */
    private Double lowSpeedTime;

    /**
     * 超公里费用 (文艺复兴对应报价的里程费)
     */
    private Double extraDistanceFee;

    /**
     * 缓行费
     */
    private Double extraLowspeedFee;

    /**
     * 远程行驶费
     */
    private Double remoteFee;

    /**
     * 夜间服务费
     */
    private Double nightFee;

    /**
     * 停车费
     */
    private Double parkingFee;

    /**
     * 过路过桥费
     */
    private Double tollFee;

    /**
     * 对用户奖励策略id串
     */
    private String userRewardIds;

    /**
     * 对用户奖励金额
     */
    private Double userRewardAmmont;
    // 运价
    private double activityDeductPrice;

    // 券抵扣金额
    private double couponDeductPrice;

    private double startPrice;
    /** 溢价详情 */
//    private OverFeeDetail overFeeDetail;
    /** 时长费 */
    private double extraTimeFee;
    /** 最低消费 */
    private double minimumCharge;
    /** 是否命中最低消费 0-否 1-是 */
    private int isMininumCharge;

    /** 司机时长费 */
    private double drivExtraTimeFee;
    /** 司机是否命中最低消费 */
    private int drivIsMinimumCharge;
    /** 司机最低消费 */
    private double drivMinimumCharge;
    /** 停车费 */
    private double drivParkingFee;
    /** 司机过路过桥费 */
    private double drivTollFee;
    /** 里程费（供应币种） */
    private double drivExtraDistanceFee;
    /** 司机夜间服务费 */
    private double drivNightFee;
    /** 司机预估总费用 */
    private double drivTotalFee;

    /**
     * 分成加价（供应币种）
     */
    private BigDecimal settleOverfee;
    /**
     * 非分成加价（供应币种）
     */
    private BigDecimal extraOverfee;
    /**
     * 分成加价（用户币种）
     */
    private BigDecimal userSettleOverfee;
    /**
     * 非分成加价（用户币种）
     */
    private BigDecimal userExtraOverfee;
    /**
     * 服务商原价（供应币种）
     */
    private BigDecimal oriPrice;
    /**
     * 服务商原价（用户币种）
     */
    private BigDecimal userOriPrice;
    /**
     * 溢价详情（供应币种）
     */
    private String drivOverFeeDetail;
    /**
     * 必接单奖励（供应币种）
     */
    private BigDecimal driverTakenRewardAmount;
    /**
     * 用户币种
     */
    private String userCurrency;
    /**
     * 供应币种
     */
    private String vendorCurrency;
    /**
     * 起步价（供应币种）
     */
    private BigDecimal drivStartPrice;
}
