package com.ctrip.dcs.dsp.delay.qconfig;

import com.ctrip.dcs.dsp.delay.qconfig.key.CarConfigKeyVO;
import com.ctrip.dcs.dsp.delay.qconfig.key.LbsBufferKeyVO;
import com.ctrip.dcs.dsp.delay.qconfig.key.OrderMileageConfigKeyVO;
import com.ctrip.dcs.dsp.delay.qconfig.value.LbsBufferValueVO;
import com.ctrip.dcs.dsp.delay.qconfig.value.OrderMileageConfigValueVO;
import org.springframework.stereotype.Component;
import qunar.tc.qconfig.client.spring.QConfig;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Component
public class LbsBufferConfig extends BaseCarConfig<LbsBufferKeyVO, LbsBufferValueVO> {

    private static final LbsBufferValueVO DEFAULT = new LbsBufferValueVO("00:00", "23:59", "fixValue", 0D);

    @QConfig("visualConfig_car.dsp.lbs_buffer_config_0.json")
    private HashMap<String, Config<LbsBufferKeyVO, LbsBufferValueVO>> context;


    @Override
    Map<String, Config<LbsBufferKeyVO, LbsBufferValueVO>> getContext() {
        return this.context;
    }

    public LbsBufferValueVO getValue(Integer cityId) {
        LbsBufferValueVO value = get(new LbsBufferKeyVO(cityId));
        if (Objects.isNull(value)) {
            value = get(new LbsBufferKeyVO(CarConfigKeyVO.ALL_CITY_ID));
        }
        return value == null ? DEFAULT : value;
    }
}
