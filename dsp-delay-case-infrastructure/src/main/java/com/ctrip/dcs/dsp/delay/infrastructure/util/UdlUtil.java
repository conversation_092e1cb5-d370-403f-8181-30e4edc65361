package com.ctrip.dcs.dsp.delay.infrastructure.util;

import com.ctrip.dcs.dsp.delay.consts.CommonConstant;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.google.common.collect.Maps;
import org.apache.commons.lang.StringUtils;

/**
 * <AUTHOR>
 * @since 2025/6/6 10:58
 */
public class UdlUtil {
    private static Logger logger = LoggerFactory.getLogger(UdlUtil.class);
    
    public static String getRequestFrom(String udl) {
        try {
            return StringUtils.isNotBlank(udl) ? udl.split(CommonConstant.UNDERLINE)[1] : null;
        } catch (Exception ex) {
            logger.error("udlUtil_getRequestFrom_error", "udl:" + udl, ex, Maps.newHashMap());
            return null;
        }
    }
}
