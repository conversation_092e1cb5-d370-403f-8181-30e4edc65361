package com.ctrip.dcs.dsp.delay.infrastructure.geteway;

import com.ctrip.dcs.dsp.delay.enums.YesOrNo;
import com.ctrip.dcs.dsp.delay.gateway.TrafficControlGateway;
import com.ctrip.dcs.dsp.delay.infrastructure.soa.ShoppingServiceProxy;
import com.ctrip.dcs.dsp.delay.qconfig.DelayDspCommonQConfig;
import com.ctrip.dcs.dsp.delay.util.DateUtil;
import com.ctrip.dcs.dsp.delay.util.MetricsUtil;
import com.ctrip.dcs.price.interfaces.dto.trafficcontrol.CheckDriverTrafficControlRequestType;
import com.ctrip.dcs.price.interfaces.dto.trafficcontrol.CheckDriverTrafficControlResponseType;
import com.ctrip.dcs.price.interfaces.dto.trafficcontrol.DriverInfoDTO;
import com.ctrip.igt.RestResponseResult;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.exception.BizException;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
public class TrafficControlGatewayImpl implements TrafficControlGateway {

    private static final Logger logger = LoggerFactory.getLogger(TrafficControlGatewayImpl.class);

    @Autowired
    private DelayDspCommonQConfig delayDspCommonQConfig;

    @Autowired
    private ShoppingServiceProxy shoppingServiceProxy;

    public Set<String> queryLimitLicense(Integer cityId, Date beginTime, Date endTime, Set<String> license) {
        try {
            List<DriverInfoDTO> dtos = license.stream()
                    .map(s -> {
                        DriverInfoDTO dto = new DriverInfoDTO();
                        dto.setLicensePlateNumbers(s);
                        return dto;
                    })
                    .collect(Collectors.toList());
            CheckDriverTrafficControlRequestType request = new CheckDriverTrafficControlRequestType();
            request.setCompatibilityMark(YesOrNo.YES.getCode());
            request.setServiceProviderId(delayDspCommonQConfig.getTrafficControlServiceProviderId());  // 携程自营服务商Id
            request.setCityId(cityId);
            request.setUseTime(DateUtil.formatDate(beginTime, DateUtil.DATE_FMT));
            request.setUseEndTime(DateUtil.formatDate(endTime, DateUtil.DATE_FMT));
            request.setDriverList(dtos);
            CheckDriverTrafficControlResponseType response = shoppingServiceProxy.checkDriverTrafficControl(request);
            String code = Optional.ofNullable(response).map(CheckDriverTrafficControlResponseType::getResstatus).map(RestResponseResult::getRcode).orElse("500");
            if (!"200".equals(code)) {
                response = shoppingServiceProxy.checkDriverTrafficControl(request);
                code = Optional.ofNullable(response).map(CheckDriverTrafficControlResponseType::getResstatus).map(RestResponseResult::getRcode).orElse("500");
                if (!"200".equals(code)) {
                    throw new BizException("check driver traffic control error");
                }
            }
            List<String> limit = Optional.ofNullable(response).map(CheckDriverTrafficControlResponseType::getLimitLicensePlateNumberList).orElse(Lists.newArrayList());
            return Sets.newHashSet(limit);
        } catch (Exception e) {
            MetricsUtil.recordValue("check.driver.traffic.control.soa.error", 1);
            logger.error(e);
        }
        return license;
    }
}
