package com.ctrip.dcs.dsp.delay.conflict.impl

import com.ctrip.dcs.dsp.delay.conflict.CheckCode
import com.ctrip.dcs.dsp.delay.conflict.ConflictContext
import com.ctrip.dcs.dsp.delay.gateway.GeoGateway
import com.ctrip.dcs.dsp.delay.model.DelayDspOrder
import com.ctrip.dcs.dsp.delay.model.DelayDspTask
import com.ctrip.dcs.dsp.delay.model.DispatcherConfig
import com.ctrip.dcs.dsp.delay.model.Route
import com.ctrip.dcs.dsp.delay.util.DateUtil
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 */
class EmptyDurationCheckSpec extends Specification {

    def geoGateway = Mock(GeoGateway)

    def config = Mock(DispatcherConfig)

    def check = new EmptyDurationCheck(geoGateway: geoGateway)

    @Unroll
    def "check"() {

        given: "Mock数据"
        def task = new DelayDspTask(taskId: 1)
        def prev = new DelayDspOrder(predictServiceStopTime: DateUtil.parseDate(t1))
        def next = new DelayDspOrder(sysExpectBookTime: DateUtil.parseDate(t2))
        def context = new ConflictContext(prev: prev, next: next, delayDspTask: task, config: config)

        geoGateway.queryRoute(_, _) >> new Route(duration: dur)
        config.getLbsBuffer(_) >> buffer

        when: "执行校验方法"
        def res = check.check(context)

        then: "验证校验结果"
        res == code

        where:
        t1                    | t2                    | dur | buffer || code
        "2022-08-29 12:00:00" | "2022-08-29 12:30:00" | 15D | 10D    || CheckCode.OK
        "2022-08-29 12:00:00" | "2022-08-29 12:30:00" | 20D | 10D    || CheckCode.EMPTY_DURATION_CONFLICT
        "2022-08-29 12:00:00" | "2022-08-29 12:30:00" | 15D | 15D    || CheckCode.EMPTY_DURATION_CONFLICT
    }
}
