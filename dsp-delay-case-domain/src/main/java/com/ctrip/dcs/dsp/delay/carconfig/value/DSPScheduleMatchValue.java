package com.ctrip.dcs.dsp.delay.carconfig.value;

import com.ctrip.dcs.dsp.delay.carconfig.Value;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class DSPScheduleMatchValue implements Value<String> {

    private Integer cityId;

    private Integer carTypeId;

    private Integer timeRange;

    private Integer minInterval;

    private Integer maxInterval;

    private Integer isOut;

    private Integer outLimit;

    private Integer score = 0;

    @Override
    public boolean match(String str) {
        return true;
    }
}
