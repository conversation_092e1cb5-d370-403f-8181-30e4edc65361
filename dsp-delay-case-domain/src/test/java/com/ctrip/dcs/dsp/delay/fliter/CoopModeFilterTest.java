package com.ctrip.dcs.dsp.delay.fliter;

import com.ctrip.dcs.dsp.delay.enums.CoopMode;
import com.ctrip.dcs.dsp.delay.filter.impl.CoopModeFilter;
import com.ctrip.dcs.dsp.delay.model.DelayDspTask;
import com.ctrip.dcs.dsp.delay.model.Driver;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.powermock.modules.junit4.PowerMockRunner;

/**
 * <AUTHOR>
 */
@RunWith(PowerMockRunner.class)
public class CoopModeFilterTest {

    @InjectMocks
    private CoopModeFilter filter;

    @Test
    public void test() {
        Driver driver = new Driver();
        driver.setCoopMode(CoopMode.FULL_TIME_ASSIGN.getCode());
        DelayDspTask task = new DelayDspTask();
        boolean ok = this.filter.filter(driver, task);
        Assert.assertTrue(ok);
    }
}
