<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <!-- super pom -->
  <parent>
    <groupId>com.ctrip.dcs.dsp</groupId>
    <artifactId>dsp-delay-case</artifactId>
    <version>1.0.0</version>
  </parent>

  <!-- domain module -->
  <groupId>com.ctrip.dcs.dsp</groupId>
  <artifactId>dsp-delay-case-domain</artifactId>
  <version>1.0.0</version>
  <name>dsp-delay-case-domain</name>
  <url>http://www.ctrip.com</url>
  <modelVersion>4.0.0</modelVersion>

  <!-- dependencies -->
  <dependencies>
    <!-- framwork common -->
    <dependency>
      <groupId>com.ctrip.igt.framework</groupId>
      <artifactId>common</artifactId>
    </dependency>
    <!-- qconfig -->
    <dependency>
      <groupId>com.ctrip.igt.framework</groupId>
      <artifactId>qconfig</artifactId>
    </dependency>
    <!-- qschedule -->
    <dependency>
      <groupId>com.ctrip.igt.framework</groupId>
      <artifactId>qschedule</artifactId>
    </dependency>
    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-tx</artifactId>
      <version>5.2.4.RELEASE</version>
    </dependency>
    <dependency>
      <groupId>org.mapstruct</groupId>
      <artifactId>mapstruct</artifactId>
    </dependency>
  </dependencies>

</project>
