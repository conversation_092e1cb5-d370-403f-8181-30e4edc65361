package com.ctrip.dcs.dsp.delay.carconfig.value;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class HeadTailLimitValue {

    public static final HeadTailLimitValue DEFAULT = new HeadTailLimitValue(-1D, -1D);

    private Double headLimit;

    private Double tailLimit;
}
