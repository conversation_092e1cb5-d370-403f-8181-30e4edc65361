package com.ctrip.dcs.dsp.delay.factory;

import com.ctrip.dcs.dsp.delay.conflict.CheckCode;
import com.ctrip.dcs.dsp.delay.context.DelayDspContext;
import com.ctrip.dcs.dsp.delay.core.DP;
import com.ctrip.dcs.dsp.delay.gateway.GeoGateway;
import com.ctrip.dcs.dsp.delay.model.*;
import com.ctrip.dcs.dsp.delay.qconfig.DelayDspCommonQConfig;
import com.ctrip.dcs.dsp.delay.service.ConflictService;
import com.ctrip.dcs.dsp.delay.util.MetricsUtil;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 */
@Component
public class DPFactory {

    private static final Logger logger = LoggerFactory.getLogger(DPFactory.class);

    @Autowired
    private ConflictService conflictService;

    @Autowired
    private GeoGateway geoGateway;

    @Autowired
    private DelayDspCommonQConfig delayDspCommonQConfig;

    public DP create(DriverAggregation driverAggregation, List<DelayDspOrder> orders, DelayDspContext context) {

        DelayDspTask task = context.getDelayDspTask();
        Driver driver = driverAggregation.getDriver();
        DP.ProfitFunction profitFunction = createProfitFunction(task);
        DP dp = new DP(orders.size(), context.getConfig().getIncomeStandardLine(), profitFunction);
        for (int i = 1; i <= orders.size(); i++) {
            try {
                DelayDspOrder order = orders.get(i - 1);
                // 已接订单
                dp.taken()[i - 1] = order.hasTaken();
                // 订单预估用车时间超出了任务时间范围
                dp.ignore()[i - 1] = !order.hasTaken() && task.outOfTime(order.getSysExpectBookTime());
                dp.profit()[i - 1] = order.getDriverOrderFee();
                dp.kilo()[i - 1] = order.getKiloLength();
                for (int j = i - 1; j > 0; j--) {
                    DelayDspOrder prev = orders.get(j - 1);
                    Route route = geoGateway.queryRoute(task.getTaskId(), new Position(prev, order));
                    dp.distance()[j][i] = route.getDistance();
                    CheckCode checkCode = conflictService.checkConflict(context, driverAggregation, prev, order);
                    dp.conflict()[j][i] = checkCode.isConflict();
                }
                Route route = geoGateway.queryRoute(task.getTaskId(), new Position(driver, order));
                dp.distance()[0][i] = route.getDistance();
                CheckCode checkCode = conflictService.checkConflict(context, driverAggregation, order);
                dp.conflict()[0][i] = checkCode.isConflict();
            } catch (Exception e) {
                logger.error("dispatch error", e);
                MetricsUtil.recordValue("dispatch.error", 1);
            }
        }
        return dp;
    }

    public DP.ProfitFunction createProfitFunction(DelayDspTask task) {
        if (delayDspCommonQConfig.isOrderProfitCity(task.getCityId())) {
            return new DP.OrderProfitFunction();
        }
        return new DP.OrderNetProfitFunction();
    }
}
