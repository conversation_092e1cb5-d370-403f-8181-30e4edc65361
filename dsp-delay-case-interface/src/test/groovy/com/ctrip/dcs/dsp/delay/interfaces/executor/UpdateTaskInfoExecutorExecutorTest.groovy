package com.ctrip.dcs.dsp.delay.interfaces.executor

import com.ctrip.dcs.dsp.application.service.DelayDspOrderApplicationService
import com.ctrip.dcs.dsp.delay.service.interfaces.message.CheckSpecialDateRequestType
import com.ctrip.dcs.dsp.delay.service.interfaces.message.UpdateTaskInfoRequestType
import com.ctrip.igt.framework.common.clogging.Logger
import com.ctrip.igt.framework.common.exception.BizException
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator
import org.junit.Assert
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 * @since 2024/8/20 10:30
 */
class UpdateTaskInfoExecutorExecutorTest extends Specification {
    def testObj = new UpdateTaskInfoExecutor()
    def logger = Mock(Logger)
    def delayDspApplicationService = Mock(DelayDspOrderApplicationService)

    def setup() {
        testObj.delayDspApplicationService = delayDspApplicationService
        testObj.logger = logger
    }

    @Unroll
    def "test execute"() {
        given:

        when:
        def result = testObj.execute(requestType)

        then:
        Objects.equals(result.getResponseResult().getReturnCode(), expectedResult);

        where:
        requestType                                                                                                                                    || expectedResult
        new UpdateTaskInfoRequestType(taskId: 1L, taskStatus: 1, matchStatus: 1, executeTime: "2014-11-11 11:11:11", matchTime: "2023-11-12 12:12:21") || "200"
    }

    def "test execute failed"() {
        given:
        delayDspApplicationService.updateTaskInfo(_) >> { throw new RuntimeException("") }
        UpdateTaskInfoRequestType requestType = new UpdateTaskInfoRequestType();
        requestType.setTaskId(1L)
        when:
        def result = testObj.execute(requestType)

        then:
        Assert.assertTrue(result.getResponseResult().getReturnCode() == "500")
    }
}
