package com.ctrip.dcs.dsp.delay.infrastructure.gateway;

import com.ctrip.dcs.dsp.delay.enums.OrderTakenCode;
import com.ctrip.dcs.dsp.delay.infrastructure.converter.SupplyOrderConverter;
import com.ctrip.dcs.dsp.delay.infrastructure.exception.SOANeedRetryException;
import com.ctrip.dcs.dsp.delay.infrastructure.geteway.SelfDispatcherOrderGatewayImpl;
import com.ctrip.dcs.dsp.delay.infrastructure.soa.SelfDispatcherOrderServiceProxy;
import com.ctrip.dcs.dsp.delay.infrastructure.soa.SelfOrderQueryServiceProxy;
import com.ctrip.dcs.dsp.delay.model.SupplyOrder;
import com.ctrip.dcs.dsp.delay.qconfig.DelayDspCommonQConfig;
import com.ctrip.dcs.self.dispatchorder.interfaces.ConfirmDelayDspOrderResponseType;
import com.ctrip.dcs.self.order.query.api.QueryOrderDetailForScheduleResponseType;
import com.ctrip.dcs.self.order.query.api.QueryOrderRedispatchRecordsResponseType;
import com.ctrip.igt.ResponseResult;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.modules.junit4.PowerMockRunner;

/**
 * <AUTHOR>
 */
@RunWith(PowerMockRunner.class)
public class SelfDispatcherOrderGatewayImplTest {

    @InjectMocks
    private SelfDispatcherOrderGatewayImpl gateway;

    @Mock
    private SelfDispatcherOrderServiceProxy dispatcherOrderServiceProxy;

    @Mock
    private SelfOrderQueryServiceProxy orderQueryServiceProxy;

    @Mock
    private DelayDspCommonQConfig delayDspCommonQConfig;

    @Mock
    private SupplyOrderConverter supplyOrderConverter;

    @Mock
    private SupplyOrder supplyOrder;

    @Mock
    private ConfirmDelayDspOrderResponseType confirmDelayDspOrderResponseType;

    @Test
    public void testQuery() {
        SupplyOrder supplyOrder = new SupplyOrder();
        supplyOrder.setOrderId("1");
        Mockito.when(supplyOrderConverter.toSupplyOrder(Mockito.any())).thenReturn(supplyOrder);
        SupplyOrder order = gateway.query("1");
        Assert.assertEquals("1", order.getOrderId());
    }


    @Test
    public void testQueryBase() {
        SupplyOrder supplyOrder = new SupplyOrder();
        supplyOrder.setOrderId("1");
        Mockito.when(orderQueryServiceProxy.queryOrderDetailForSchedule(Mockito.any())).thenReturn(new QueryOrderDetailForScheduleResponseType());
        SupplyOrder order = gateway.queryBase("1");
        Assert.assertNull(order);
    }



    @Test
    public void testQueryByUserOrderId() {
        SupplyOrder supplyOrder = new SupplyOrder();
        supplyOrder.setOrderId("1");
        Mockito.when(supplyOrderConverter.toSupplyOrder(Mockito.any())).thenReturn(supplyOrder);
        SupplyOrder order = gateway.queryByUserOrderId("1");
        Assert.assertEquals("1", order.getOrderId());
    }

    @Test
    public void testRedispatch() {
        Mockito.when(supplyOrderConverter.toSupplyOrder(Mockito.any())).thenReturn(new SupplyOrder());
        Exception ex = null;
        try {
            gateway.redispatch("1");
        } catch (Exception e) {
            ex = e;
        }
        Assert.assertTrue(ex instanceof SOANeedRetryException);
    }

    @Test
    public void testTaken() {
        ResponseResult responseResult = new ResponseResult();
        responseResult.setSuccess(true);
        Mockito.when(dispatcherOrderServiceProxy.confirmDelayDspOrder(Mockito.any())).thenReturn(confirmDelayDspOrderResponseType);
        Mockito.when(confirmDelayDspOrderResponseType.getResponseResult()).thenReturn(responseResult);
        Integer code = gateway.taken("1", "1", 1, "1");
        Assert.assertEquals(code, Integer.valueOf(OrderTakenCode.SUCCESS.getCode()));
    }

    @Test
    public void testIsRedispatch() {
        QueryOrderRedispatchRecordsResponseType responseResult = new QueryOrderRedispatchRecordsResponseType();
        Mockito.when(orderQueryServiceProxy.queryOrderRedispatchRecords(Mockito.any())).thenReturn(responseResult);
        Boolean result = gateway.isRedispatch("1");
        Assert.assertEquals(result, false);
    }
}
