package com.ctrip.dcs.dsp.delay.conflict;

import com.ctrip.dcs.dsp.delay.model.DelayDspOrder;
import com.ctrip.dcs.dsp.delay.model.DelayDspTask;
import com.ctrip.dcs.dsp.delay.model.DispatcherConfig;
import com.ctrip.dcs.dsp.delay.model.DriverAggregation;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public final class ConflictContext {

    private DelayDspTask delayDspTask;

    private DriverAggregation driverAggregation;

    private DelayDspOrder prev;

    private DelayDspOrder next;

    private List<DelayDspOrder> list;

    private DispatcherConfig config;

    private CheckType checkType;

    private ConflictContext(Builder builder) {
        setDelayDspTask(builder.delayDspTask);
        setDriverAggregation(builder.driverAggregation);
        setPrev(builder.prev);
        setNext(builder.next);
        setConfig(builder.config);
        setCheckType(builder.checkType);
        setList(builder.list);
    }


    public static final class Builder {
        private DelayDspTask delayDspTask;
        private DriverAggregation driverAggregation;
        private DelayDspOrder prev;
        private DelayDspOrder next;
        private List<DelayDspOrder> list;
        private DispatcherConfig config;
        private CheckType checkType;

        public Builder() {
        }

        public Builder withDelayDspTask(DelayDspTask val) {
            delayDspTask = val;
            return this;
        }

        public Builder withDriverAggregation(DriverAggregation val) {
            driverAggregation = val;
            return this;
        }

        public Builder withPrev(DelayDspOrder val) {
            prev = val;
            return this;
        }

        public Builder withNext(DelayDspOrder val) {
            next = val;
            return this;
        }

        public Builder withList(List<DelayDspOrder> val) {
            list = val;
            return this;
        }

        public Builder withConfig(DispatcherConfig val) {
            config = val;
            return this;
        }

        public Builder withCheckType(CheckType val) {
            checkType = val;
            return this;
        }

        public ConflictContext build() {
            return new ConflictContext(this);
        }
    }
}
