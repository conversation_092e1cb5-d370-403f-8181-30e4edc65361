package com.ctrip.dcs.dsp.delay.service;

import com.ctrip.dcs.dsp.delay.model.DelayDspOrder;
import com.ctrip.dcs.dsp.delay.model.DelayDspTaskRecord;

/**
 * <AUTHOR>
 */
public interface RedispatchOrderService {

    void redispatch(DelayDspTaskRecord record);

    void redispatch(DelayDspOrder order, DelayDspTaskRecord record);

    void redispatchForPreOutPool(String dspOrderId,Integer pressedDispatchSource);
}
