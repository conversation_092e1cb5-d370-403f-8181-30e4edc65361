package com.ctrip.dcs.dsp.delay.interfaces.consumer;

import com.ctrip.arch.distlock.DLock;
import com.ctrip.arch.distlock.redis.RedisDistributedLockService;
import com.ctrip.dcs.dsp.delay.carconfig.value.DelayDspTaskValue;
import com.ctrip.dcs.dsp.delay.consts.CommonConstant;
import com.ctrip.dcs.dsp.delay.enums.DelayDspTaskStatus;
import com.ctrip.dcs.dsp.delay.enums.MatchTaskStatus;
import com.ctrip.dcs.dsp.delay.model.DelayDspTask;
import com.ctrip.dcs.dsp.delay.repository.DelayDspTaskRepository;
import com.ctrip.dcs.dsp.delay.util.DateUtil;
import com.ctrip.dcs.dsp.delay.util.JsonUtil;
import com.ctrip.dcs.dsp.delay.util.MetricsUtil;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.codehaus.jackson.type.TypeReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.NeedRetryException;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 修改出池时间的消费者
 */
@Component
public class DelayDspOutPoolTimeChangeConsumer {

    private static final Logger logger = LoggerFactory.getLogger(DelayDspOutPoolTimeChangeConsumer.class);

    @Autowired
    private DelayDspTaskRepository delayDspTaskRepository;
    @Autowired
    private RedisDistributedLockService distributedLockService;

    @QmqConsumer(prefix = CommonConstant.DELAY_DSP_OUT_POOL_TIME_UPDATE, consumerGroup = CommonConstant.APP_ID)
    public void onMessage(Message message) {
        DLock lock = distributedLockService.getLock("DelayDspOutPoolTimeChangeConsumer");
        try {
            if (!lock.tryLock()) {
                logger.info("DelayDspOutPoolTimeChangeConsumerInfo", "task is executing");
                throw new NeedRetryException(System.currentTimeMillis() + 10 * 1000, "DelayDspTaskRunConsumerRetry");
            }
            String stringProperty = message.getStringProperty("list");
            if (StringUtils.isBlank(stringProperty)) {
                logger.error("DelayDspOutPoolTimeChangeConsumer", "list is empty");
                return;
            }
            Map<String, List<DelayDspTaskValue>> stringListMap = JsonUtil.fromJson(stringProperty, new TypeReference<Map<String, List<DelayDspTaskValue>>>() {
            });
            logger.info("DelayDspOutPoolTimeChangeConsumer", JsonUtil.toJson(stringListMap));
            for (Map.Entry<String, List<DelayDspTaskValue>> entry : stringListMap.entrySet()) {
                logger.info("DelayDspOutPoolTimeChangeConsumer_changeOutPoolTime_entry", JsonUtil.toJson(entry));
                entry.getValue().forEach(v -> changeOutPoolTime(entry.getKey(), v));
            }
        }catch (Exception e){
            MetricsUtil.recordValue("DelayDspOutPoolTimeChangeConsumer_onMessage_error", 1);
            logger.error("DelayDspOutPoolTimeChangeConsumer_onMessage_error", e);
            throw new NeedRetryException("DelayDspOutPoolTimeChangeConsumer");
        } finally {
            lock.unlock();
        }
    }


    public void changeOutPoolTime(String cityId, DelayDspTaskValue DelayDspTaskValue) {
        try {
            Date nowDate = new Date();
            changeTimeForIncompleteMatchTask(nowDate, DelayDspTaskValue, cityId);

            changeTimeForCompleteMatchTask(nowDate, DelayDspTaskValue, cityId);
        } catch (Exception e) {
            logger.error("DelayDspMatchTaskRunConsumer_error", e);
            MetricsUtil.recordValue("DelayDspOutPoolTimeChangeConsumer_updateTask_error", 1);
            throw new NeedRetryException("DelayDspOutPoolTimeChangeConsumer");
        }
    }


    //定时匹配任务未结束,任务状态未完成的任务
    public void changeTimeForIncompleteMatchTask(Date nowDate, DelayDspTaskValue DelayDspTaskValue, String cityId) {
        List<DelayDspTask> list = delayDspTaskRepository.batchQueryForChangeOutPoolTime(cityId, DelayDspTaskValue.getBegin(), DelayDspTaskValue.getEnd(), null, MatchTaskStatus.START.getCode());
        logger.info("DelayDspOutPoolTimeChangeConsumer_changeTimeForIncompleteMatchTask_list", JsonUtil.toJson(list));
        if(CollectionUtils.isNotEmpty(list)){
            List<DelayDspTask> extracted = extracted(nowDate, DelayDspTaskValue, list);
            logger.info("DelayDspOutPoolTimeChangeConsumer_changeTimeForIncompleteMatchTask_extracted", JsonUtil.toJson(extracted));
            if(CollectionUtils.isNotEmpty(extracted)){
                //处理定时匹配任务
                extracted.forEach(task -> {
                    Date executeTime = task.getExecuteTime();
                    Date matchTime = task.getMatchTime();
                    logger.info("DelayDspOutPoolTimeChangeConsumer_changeTimeForIncompleteMatchTask_updateMatchInfo", "taskId: {}, executeTime: {}, matchTime: {}", task.getTaskId(), executeTime, matchTime);
                    //如果定时匹配任务的执行时间不在出池时间的前面，那么无需再执行定時匹配任务了
                    if(!matchTime.before(executeTime)){
                        delayDspTaskRepository.updateMatchInfo(task.getTaskId(),null, null, null, MatchTaskStatus.END.getCode());
                    }
                });
            }
        }
    }


    //执行完定时配置任务的任务，直接将任务的出池时间修改为任务的开始时间-配置的小时
    public void changeTimeForCompleteMatchTask(Date nowDate, DelayDspTaskValue DelayDspTaskValue, String cityId) {
        List<DelayDspTask> list = delayDspTaskRepository.batchQueryForChangeOutPoolTime(cityId, DelayDspTaskValue.getBegin(), DelayDspTaskValue.getEnd(), DelayDspTaskStatus.UN_EXECUTED.getCode(), MatchTaskStatus.END.getCode());
        logger.info("DelayDspOutPoolTimeChangeConsumer_changeTimeForFinishMatchTask_list", JsonUtil.toJson(list));
        if(CollectionUtils.isNotEmpty(list)){
            extracted(nowDate, DelayDspTaskValue, list);
        }
    }


    private List<DelayDspTask> extracted(Date nowDate, DelayDspTaskValue DelayDspTaskValue, List<DelayDspTask> list) {
        List<DelayDspTask> delayDspTaskList = new ArrayList<>();
        list.forEach(task -> {
            // 执行时间=任务开发时间范围-配置的小时
            Date realOutPoolTime = null;
            Date outPoolTime = DateUtil.addHours(task.getBeginTime(), -DelayDspTaskValue.getHour());
            if(outPoolTime.before(nowDate)){
                realOutPoolTime = nowDate;
            }else {
                Date executeTime = task.getExecuteTime();
                if(outPoolTime.before(executeTime)) {
                    realOutPoolTime = outPoolTime;
                }
            }
            logger.info("DelayDspOutPoolTimeChangeConsumer_changeTimeForFinishMatchTask", "taskId: {}, outPoolTime: {}, realOutPoolTime: {}, nowDate:{}", task.getTaskId(), outPoolTime, realOutPoolTime, nowDate);
            if(realOutPoolTime != null){
                delayDspTaskRepository.updateOutPoolTime(task.getTaskId(), realOutPoolTime);
                task.setExecuteTime(realOutPoolTime);
                delayDspTaskList.add(task);
            }
        });
        return delayDspTaskList;
    }
}
