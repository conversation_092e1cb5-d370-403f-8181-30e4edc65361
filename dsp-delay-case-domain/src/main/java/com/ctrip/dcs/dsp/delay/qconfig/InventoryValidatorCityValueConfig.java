package com.ctrip.dcs.dsp.delay.qconfig;

import com.ctrip.dcs.dsp.delay.qconfig.key.CarConfigKeyVO;
import com.ctrip.dcs.dsp.delay.qconfig.key.InventoryValidatorCityKeyVO;
import com.ctrip.dcs.dsp.delay.qconfig.value.CityValueVO;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import qunar.tc.qconfig.client.spring.QConfig;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
public class InventoryValidatorCityValueConfig extends BaseCarConfig<InventoryValidatorCityKeyVO, CityValueVO> {

    @QConfig("visualConfig_inventoryValidatorCityValue_0.json")
    private HashMap<String, Config<InventoryValidatorCityKeyVO, CityValueVO>> context;


    @Override
    Map<String, Config<InventoryValidatorCityKeyVO, CityValueVO>> getContext() {
        return this.context;
    }

    public boolean getValue(Integer cityId) {
        if(Objects.isNull(cityId)){
            return false;
        }
        List<CityValueVO> value = getList(new InventoryValidatorCityKeyVO());
        if (CollectionUtils.isEmpty(value)) {
            return false;
        }
        List<Integer> collect = value.stream().map(CityValueVO::getCityId).collect(Collectors.toList());
        return collect.contains(cityId) || collect.contains(CarConfigKeyVO.ALL_CITY_ID);
    }
}
