package com.ctrip.dcs.dsp.delay.interfaces.schedule;

import com.ctrip.dcs.dsp.delay.consts.CommonConstant;
import com.ctrip.dcs.dsp.delay.enums.OutType;
import com.ctrip.dcs.dsp.delay.enums.YesOrNo;
import com.ctrip.dcs.dsp.delay.infrastructure.mapper.DelayDspTaskMapper;
import com.ctrip.dcs.dsp.delay.infrastructure.mapper.DelayDspTaskRecordMapper;
import com.ctrip.dcs.dsp.delay.model.DelayDspTaskRecord;
import com.ctrip.dcs.dsp.delay.mq.MessageProducer;
import com.ctrip.dcs.dsp.delay.qconfig.DelayDspCommonQConfig;
import com.ctrip.dcs.dsp.delay.util.DateUtil;
import com.ctrip.dcs.dsp.delay.util.JsonUtil;
import com.ctrip.dcs.dsp.delay.util.MetricsUtil;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerContext;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.dianping.cat.Cat;
import com.google.common.collect.ImmutableMap;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qschedule.config.QSchedule;
import qunar.tc.schedule.Parameter;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;


/**
 * <AUTHOR>
 */
@Component
public class DelayDspOrderOutSchedule {

    private static final Logger logger = LoggerFactory.getLogger(DelayDspOrderOutSchedule.class);

    @Autowired
    private DelayDspTaskRecordMapper delayDspTaskRecordMapper;

    @Autowired
    private DelayDspTaskMapper delayDspTaskMapper;
    @Autowired
    private MessageProducer messageProducer;

    @Autowired
    private DelayDspCommonQConfig delayDspCommonQConfig;

    /**
     * 出池接单
     * @param parameter
     */
    @QSchedule("dcs.dsp.delay.task.order.out.taken")
    public void taken(Parameter parameter) {
        LoggerContext.newContext().withGlobalTraceId(UUID.randomUUID().toString()).init();
        Integer pageSize = pageSize(parameter);
        List<DelayDspTaskRecord> records = delayDspTaskRecordMapper.queryNotOut(YesOrNo.YES.getCode(), pageSize);
        logger.info("DelayDspOrderOutSchedule_taken_records", JsonUtil.toJson(records));
        if (CollectionUtils.isEmpty(records)) {
            return;
        }
        long delay = 0;
        for (DelayDspTaskRecord record : records) {
            try {
                logger.info("OutDelayDspTaskRecordTakenInfo", JsonUtil.toJson(record));
                updateRecord(record);
                Map<String, Object> data = ImmutableMap.<String, Object>builder()
                        .put("recorde_id", record.getId())
                        .put("out_type", OutType.TAKEN.getCode())
                        .build();
                messageProducer.sendDelayMessage(CommonConstant.DELAY_DSP_ORDER_OUT_SUBJECT, data, delay);
                messageProducer.sendDelayMessage(CommonConstant.DELAY_DSP_ORDER_OUT_CHECK_SUBJECT, data, delay + delayDspCommonQConfig.getCheckOrderOutMillisecond());
                delay += 500;
            } catch (Exception e) {
                logger.error("OutDelayDspTaskRecordError", e);
            }
        }
    }

    /**
     * 出池改派
     * @param parameter
     */
    @QSchedule("dcs.dsp.delay.task.order.out.redispatch")
    public void redispatch(Parameter parameter) {
        Cat.getOrCreateTraceContext();
        List<DelayDspTaskRecord> records;
        Integer pageSize = pageSize(parameter);
        if(delayDspCommonQConfig.getOutRedispatchSwitch() == 1){
            Integer countNotOut = delayDspTaskRecordMapper.queryCountNotOut(YesOrNo.YES.getCode());
            logger.info("DelayDspOrderOutSchedule_redispatch_countNotOut", JsonUtil.toJson(countNotOut));
            //出池接单还未完成
            if (countNotOut != null && countNotOut > 0) {
                MetricsUtil.recordValue("out.redispatch.countNotOut", 1);
                //查询出所有要改派的去重任务id
                List<Long> taskIds = delayDspTaskRecordMapper.getDistinctTaskIdNotRedispatch();
                logger.info("DelayDspOrderOutSchedule_getDistinctTaskIdNotRedispatch", JsonUtil.toJson(taskIds));
                if(CollectionUtils.isEmpty(taskIds)){
                    return;
                }
                Integer minutesBeforeDeadTime = delayDspCommonQConfig.getMinutesBeforeDeadTime();
                logger.info("DelayDspOrderOutSchedule_minutesBeforeDeadTime", JsonUtil.toJson(minutesBeforeDeadTime));
                Date date = DateUtil.addMinutes(new Date(), minutesBeforeDeadTime);
                logger.info("DelayDspOrderOutSchedule_addMinutes", JsonUtil.toJson(date));
                List<Long> redispatchTaskIds = delayDspTaskMapper.queryByTaskIdsAndDeadTime(taskIds, date);
                logger.info("DelayDspOrderOutSchedule_queryByTaskIdsAndDeadTime", JsonUtil.toJson(redispatchTaskIds));
                if(CollectionUtils.isEmpty(redispatchTaskIds)){
                    return;
                }
                records = delayDspTaskRecordMapper.queryNotOutByTask(redispatchTaskIds, pageSize);
            }else{
                records = delayDspTaskRecordMapper.queryNotOut(YesOrNo.NO.getCode(), pageSize);
            }
        }else{
            records = delayDspTaskRecordMapper.queryNotOut(YesOrNo.NO.getCode(), pageSize);
        }
        logger.info("DelayDspOrderOutSchedule_redispatch_records", JsonUtil.toJson(records));
        if (CollectionUtils.isEmpty(records)) {
            return;
        }
        long delay = 0;
        for (DelayDspTaskRecord record : records) {
            try {
                logger.info("OutDelayDspTaskRecordRedispatchInfo", JsonUtil.toJson(record));
                updateRecord(record);
                Map<String, Object> data = ImmutableMap.<String, Object>builder()
                        .put("recorde_id", record.getId())
                        .put("out_type", OutType.REDISPATCH.getCode())
                        .build();
                messageProducer.sendDelayMessage(CommonConstant.DELAY_DSP_ORDER_OUT_SUBJECT, data, delay);
                messageProducer.sendDelayMessage(CommonConstant.DELAY_DSP_ORDER_OUT_CHECK_SUBJECT, data, delay + delayDspCommonQConfig.getCheckOrderOutMillisecond());
                delay += 500;
            } catch (Exception e) {
                logger.error("OutDelayDspTaskRecordError", e);
            }
        }
    }

    private Integer pageSize(Parameter parameter) {
        Integer pageSize = parameter.getProperty("pageSize", Integer.class);
        return Objects.nonNull(pageSize) ? pageSize : 15;
    }

    private void updateRecord(DelayDspTaskRecord record) {
        record.setIsOut(YesOrNo.YES.getCode());
        delayDspTaskRecordMapper.update(record);
    }
}
