package com.ctrip.dcs.dsp.delay.infrastructure.aspect;

import com.ctrip.dcs.dsp.delay.infrastructure.annotation.Retryable;
import com.ctrip.dcs.dsp.delay.infrastructure.exception.SOANeedRetryException;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.exception.BizException;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Sets;
import org.apache.http.conn.ConnectTimeoutException;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import java.net.SocketTimeoutException;
import java.util.Set;


/**
 * 调用 SOA 服务切面
 *
 * <AUTHOR> Zhang<PERSON>hen
 * @create 2023/2/12 19:52
 */
@Aspect
@Component
public class RetryableAspect {

    private static final Logger logger = LoggerFactory.getLogger(RetryableAspect.class);

    private static final Set<Class<? extends Throwable>> NEED_RETRY_CLASS = Sets.newHashSet(SOANeedRetryException.class, SocketTimeoutException.class, ConnectTimeoutException.class);

    @Around("@annotation(com.ctrip.dcs.dsp.delay.infrastructure.annotation.Retryable)")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        String methodName = signature.getName();
        Retryable annotation = signature.getMethod().getAnnotation(Retryable.class);
        if (annotation == null) {
            return joinPoint.proceed();
        }
        Throwable error = null;
        for (int i = 0; i < annotation.maxAttempts(); i++) {
            try {
                return joinPoint.proceed();
            } catch (Throwable e) {
                error = e;
                logger.warn("soaExecuteError", "methodName:{}", methodName, e);
                if (!include(e, annotation.include())) {
                    break;
                }
            }
        }
        logger.error("soaExecuteError", "methodName:{} error:{}", ImmutableMap.of("methodName", methodName), methodName, error == null ? "" : error.getMessage());
        throw new BizException(error);
    }

    private boolean include(Throwable e, Class<? extends Throwable>[] classes) {
        for (Class<? extends Throwable> c : NEED_RETRY_CLASS) {
            if (e.getClass().equals(c)) {
                return true;
            }
        }
        for (Class<? extends Throwable> c : classes) {
            if (e.getClass().equals(c)) {
                return true;
            }
        }
        return false;
    }
}