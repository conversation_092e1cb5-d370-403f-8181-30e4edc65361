package com.ctrip.dcs.dsp.delay.qconfig.key;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version V1.0
 * @date 2023/3/21 11:00
 */
@Getter
public class OrderDisLimitKeyVO implements CarConfigKeyVO {

    private Integer cityId;

    private Integer carTypeId;

    @JsonCreator
    public OrderDisLimitKeyVO(@JsonProperty("cityId") Integer cityId, @JsonProperty("carTypeId") Integer carTypeId) {
        this.cityId = cityId;
        this.carTypeId = carTypeId;
    }

    @Override
    public String toKey() {
        return this.cityId + PLACEHOLDER + this.carTypeId;
    }

}
