package com.ctrip.dcs.dsp.delay.service;

import com.ctrip.dcs.dsp.delay.model.DelayDspConfigInfoDTO;
import com.ctrip.dcs.dsp.delay.model.DelayDspConfigResInfoDTO;
import com.ctrip.dcs.dsp.delay.model.DelayDspTask;
import com.ctrip.dcs.dsp.delay.model.SupplyOrder;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface DelayTaskService {

    /**
     * 查询一个任务，若任务不存在，则新创建一个
     * 并发调用时，同城市同车型同时段内，只能有一个生效的任务
     * @param order
     * @return
     */
    DelayDspTask queryOrCreate(SupplyOrder order);
    
    /**
     * 查询一个任务
     * @param order
     * @return
     */
    DelayDspTask buildTaskTemp(SupplyOrder order);


    List<DelayDspConfigResInfoDTO> queryDelayDspConfigList(List<DelayDspConfigInfoDTO> delayDspConfigInfoList);

    Integer getMatchTaskTime(DelayDspTask task);

    void updateTaskInfo(DelayDspTask delayDspTask);
}
