package com.ctrip.dcs.dsp.delay.model;

import com.ctrip.dcs.dsp.delay.carconfig.value.DriverProfitDayBaselineValue;
import com.ctrip.dcs.dsp.delay.carconfig.value.HeadTailLimitValue;
import com.ctrip.dcs.dsp.delay.carconfig.value.LBSBufferValue;
import com.ctrip.dcs.dsp.delay.carconfig.value.OrderMileageValue;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@NoArgsConstructor
@AllArgsConstructor
@Getter
public class DispatcherConfig {

    private List<OrderMileageValue> orderMileageValues;

    private DriverProfitDayBaselineValue driverProfitDayBaselineValue;

    private List<LBSBufferValue> lbsBufferValues;

    private HeadTailLimitValue headTailLimitValue;

    public Integer getIncomeStandardLine() {
        return driverProfitDayBaselineValue.getHighProfit();
    }

    public Double getOrderMileageValueRate(Date datetime, Double kiloLength) {
        OrderMileageValue.Parameter parameter = new OrderMileageValue.Parameter(kiloLength, datetime);
        for (OrderMileageValue value : this.orderMileageValues) {
            if (value.match(parameter)) {
                return value.getValue();
            }
        }
        return OrderMileageValue.DEFAULT.getValue();
    }

    public Double getLbsBuffer(Date datetime) {
        for (LBSBufferValue value : this.lbsBufferValues) {
            if (value.match(datetime)) {
                return value.getValue();
            }
        }
        return LBSBufferValue.DEFAULT.getValue();
    }

    public Double getHeadLimit() {
        return headTailLimitValue.getHeadLimit();
    }

    public Double getTailLimit() {
        return headTailLimitValue.getTailLimit();
    }

}
