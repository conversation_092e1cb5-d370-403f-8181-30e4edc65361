package com.ctrip.dcs.dsp.delay.infrastructure.http.dto;

import lombok.Getter;
import lombok.Setter;

import java.util.Objects;

/**
 * <AUTHOR>
 */
@Setter
@Getter
public class QunarResResult<T> {

    private BStatus bstatus;
    private T data;

    public boolean success() {
        if (Objects.isNull(bstatus)) {
            return false;
        }
        return Objects.equals(bstatus.getCode(), 0);
    }
}
