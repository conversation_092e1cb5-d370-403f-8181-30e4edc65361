package com.ctrip.dcs.dsp.delay.service.impl;

import com.ctrip.dcs.dsp.delay.enums.OrderTakenCode;
import com.ctrip.dcs.dsp.delay.enums.OutPoolWayEnum;
import com.ctrip.dcs.dsp.delay.enums.YesOrNo;
import com.ctrip.dcs.dsp.delay.gateway.DriverGateway;
import com.ctrip.dcs.dsp.delay.gateway.SelfDispatcherOrderGateway;
import com.ctrip.dcs.dsp.delay.limit.OutPoolRateLimiter;
import com.ctrip.dcs.dsp.delay.model.DelayDspMatchTaskRecord;
import com.ctrip.dcs.dsp.delay.model.DelayDspOrder;
import com.ctrip.dcs.dsp.delay.model.DelayDspTask;
import com.ctrip.dcs.dsp.delay.model.DelayDspTaskRecord;
import com.ctrip.dcs.dsp.delay.model.Driver;
import com.ctrip.dcs.dsp.delay.model.TransportGroupInfo;
import com.ctrip.dcs.dsp.delay.mq.MessageProducer;
import com.ctrip.dcs.dsp.delay.qconfig.DelayDspCommonQConfig;
import com.ctrip.dcs.dsp.delay.repository.DelayDspMatchTaskRepository;
import com.ctrip.dcs.dsp.delay.repository.DelayDspOrderRepository;
import com.ctrip.dcs.dsp.delay.repository.DelayDspTaskRepository;
import com.ctrip.dcs.dsp.delay.service.PreOutPoolService;
import com.ctrip.dcs.dsp.delay.service.TakenOrderService;
import com.ctrip.dcs.dsp.delay.util.DateUtil;
import com.ctrip.dcs.dsp.delay.util.MetricsUtil;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * <AUTHOR>
 */
@Component
public class TakenOrderServiceImpl implements TakenOrderService {

    private static final Logger logger = LoggerFactory.getLogger(TakenOrderServiceImpl.class);

    @Autowired
    private DelayDspTaskRepository delayDspTaskRepository;

    @Autowired
    private DelayDspOrderRepository dspOrderRepository;

    @Autowired
    private DelayDspMatchTaskRepository delayDspMatchTaskRepository;

    @Autowired
    private DriverGateway driverGateway;

    @Autowired
    private OutPoolRateLimiter outPoolRateLimiter;

    @Autowired
    private SelfDispatcherOrderGateway selfDispatcherOrderGateway;

    @Autowired
    private MessageProducer messageProducer;

    @Autowired
    private PreOutPoolService preOutPoolService;
    @Autowired
    private DelayDspCommonQConfig delayDspCommonQConfig;

    @Override
    public void taken(DelayDspTaskRecord record) {
        DelayDspTask task = delayDspTaskRepository.queryByTaskId(record.getTaskId());
        DelayDspOrder order = dspOrderRepository.queryByOrderId(record.getOrderId());
        if (Objects.isNull(task) || Objects.isNull(order) || Objects.equals(order.getIsDelay(), YesOrNo.NO.getCode())) {
            return;
        }
        if (StringUtils.isBlank(record.getDriverId()) || Objects.equals("0", record.getDriverId()) || StringUtils.isBlank(record.getDriverOrderId())) {
            //此处不需要考虑提前出池，因为提前出池的订单一定会有司机单和司机，无以上信息，走改派逻辑
            delayDspTaskRepository.updateRecordMatchStatus(YesOrNo.NO.getCode(), YesOrNo.NO.getCode(), record.getId());
            return;
        }
        Driver driver = driverGateway.query(record.getDriverId());
        if (Objects.isNull(driver)) {
            if(record.getPreOut().equals(YesOrNo.NO.getCode())){
                delayDspTaskRepository.updateRecordMatchStatus(YesOrNo.NO.getCode(), YesOrNo.NO.getCode(), record.getId());
            }else {
                DelayDspMatchTaskRecord record1 = new DelayDspMatchTaskRecord();
                record1.setId(record.getMatchRecordId());
                record1.setTakenCode(OrderTakenCode.NO_DRIVER.getCode());
                record1.setOutTime(new Date());
                delayDspMatchTaskRepository.update(record1);
            }
            return;
        }
        taken(task, order, driver, record);
    }

    private void taken(DelayDspTask task, DelayDspOrder order, Driver driver, DelayDspTaskRecord record) {
        try {
            try {
                if (isLimit(task)) {
                    // 限流
                    double acquire = outPoolRateLimiter.getConfirmRateLimiter(task).acquire();
                    logger.info("TakenOrderServiceImpl_taken", "get taken token.order: {}, time:{}", order.getOrderId(), acquire);
                }
            } catch (Exception e) {
                logger.error("TakenOrderServiceImpl_taken", "taken try acquire error! order id:{}", order.getOrderId());
            }
            Integer transportGroupId = driver.getRegisterTransportGroupId();
            if (transportGroupId == null) {
                transportGroupId = Optional.ofNullable(driver.getTransportGroups()).orElse(Collections.emptyList()).stream().findFirst().map(TransportGroupInfo::getTransportGroupId).orElse(0);
            }
            // 匹配到司机，并接单
            Integer code;
            try {
                code = selfDispatcherOrderGateway.taken(order.getOrderId(), driver.getDriverId(), transportGroupId, order.getDuid());
            } catch (Exception e) {
                logger.error("TakenOrderServiceImpl_taken_selfDispatcherOrderGateway",e);
                //todo 出现一次就告警
                MetricsUtil.recordValue("self.confirm.error", 1);
                outRecord(record.getId(), YesOrNo.NO.getCode());
                return;
            }
            delayDspTaskRepository.updateTakenCode(code, record);
            if (!Objects.equals(code, OrderTakenCode.SUCCESS.getCode())) {
                // 发送应单失败消息，取消司机单
                Map<String, Object> data = Maps.newHashMap();
                data.put("dspOrderId", record.getOrderId());
                data.put("driverId", record.getDriverId());
                data.put("driverOrderId", record.getDriverOrderId());
                messageProducer.sendDelayMessage("dcs.self.dsp.order.schedule.driver.confirm.fail.cancel", data, 15 * 1000L);
                // 接单失败
                MetricsUtil.recordValue("order.match.taken.fail", 1);
            }else{
                Long matchRecordId = record.getMatchRecordId();
                if(Objects.nonNull(matchRecordId) && matchRecordId > 0){
                    preOutPoolService.sendMessageToWorkBenchLog(order.getMainOrderId(), delayDspCommonQConfig.getOutReasonConfig(OutPoolWayEnum.MATCH_DRIVER.getWay()));
                }else{
                    preOutPoolService.sendMessageToWorkBenchLog(order.getMainOrderId(), delayDspCommonQConfig.getOutReasonConfig(OutPoolWayEnum.ARRIVER_OUT_DRIVER_TIME.getWay()));
                }
            }
            MetricsUtil.recordValue("order.match.ok", 1);
        } catch (Exception e) {
            logger.error("TakenOrderServiceImpl_taken_error!", e);
            outRecord(record.getId(), YesOrNo.NO.getCode());
        }
    }

    private void outRecord(Long recordId, Integer out) {
        try {
            DelayDspTaskRecord record = new DelayDspTaskRecord();
            record.setId(recordId);
            record.setIsOut(out);
            delayDspTaskRepository.update(record);
        } catch (Exception e) {
            logger.error("TakenOrderServiceImpl_outRecord_error", e);
        }
    }

    private boolean isLimit(DelayDspTask task) {
        double diff = DateUtil.minutesDiff(new Date(), task.getExecuteTimeDeadline());
        return diff > 30D;
    }
}
