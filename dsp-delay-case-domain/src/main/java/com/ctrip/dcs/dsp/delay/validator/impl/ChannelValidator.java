package com.ctrip.dcs.dsp.delay.validator.impl;

import com.ctrip.dcs.dsp.delay.gateway.ChannelGateway;
import com.ctrip.dcs.dsp.delay.model.ChannelNumber;
import com.ctrip.dcs.dsp.delay.model.SupplyOrder;
import com.ctrip.dcs.dsp.delay.qconfig.DelayDspCommonQConfig;
import com.ctrip.dcs.dsp.delay.util.JsonUtil;
import com.ctrip.dcs.dsp.delay.validator.Validator;
import com.ctrip.dcs.dsp.delay.validator.ValidatorCode;
import com.ctrip.dcs.dsp.delay.validator.ValidatorDTO;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.google.common.collect.Sets;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.Set;

/**
 * 订单渠道号校验
 * <AUTHOR>
 */
@Component("channelValidator")
public class ChannelValidator implements Validator {

    private static final Logger logger = LoggerFactory.getLogger(ChannelValidator.class);

    @Autowired
    private ChannelGateway channelGateway;

    @Autowired
    private DelayDspCommonQConfig delayDspCommonQConfig;

    @Override
    public ValidatorDTO validate(SupplyOrder order) {
        ChannelNumber channel = channelGateway.queryChannelNumber(order.getDistributionChannel());
        if (Objects.isNull(channel)) {
            logger.info("ChannelValidator", "query channel number is empty!, order id: {}", order.getOrderId());
            return new ValidatorDTO(ValidatorCode.ERROR);
        }
        Set<Long> channels = Sets.newHashSet(
                channel.getChannelNumberId(),
                channel.getPrimaryChannelGroupId(),
                channel.getSecondaryChannelGroupId(),
                channel.getTertiaryChannelGroupId()
        );
        Set<Long> notDelayDspChannel = delayDspCommonQConfig.getNotDelayDspChannelSet();
        channels.retainAll(notDelayDspChannel);
        if (CollectionUtils.isNotEmpty(channels)) {
            // 在配置的不允许进入延后派的渠道中
            logger.info("ChannelValidator", "channel on the black list! order id: {}, channel is {}", order.getOrderId(), JsonUtil.toJson(channel));
            return new ValidatorDTO(ValidatorCode.CHANNEL);
        }
        Set<Long> delayDspChannel = delayDspCommonQConfig.getDelayDspChannelSet();
        if (!delayDspChannel.contains(channel.getPrimaryChannelGroupId())) {
            // 不在配置的延后派的主渠道中
            logger.info("ChannelValidator", "channel not on the white list!, order id: {}, channel is {}", order.getOrderId(), JsonUtil.toJson(channel));
            return new ValidatorDTO(ValidatorCode.CHANNEL);
        }
        return new ValidatorDTO(ValidatorCode.OK);
    }
}
