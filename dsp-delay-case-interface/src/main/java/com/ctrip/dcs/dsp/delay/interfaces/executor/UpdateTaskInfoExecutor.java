package com.ctrip.dcs.dsp.delay.interfaces.executor;

import com.ctrip.dcs.dsp.application.service.DelayDspOrderApplicationService;
import com.ctrip.dcs.dsp.delay.model.DelayDspTask;
import com.ctrip.dcs.dsp.delay.service.interfaces.message.CheckSpecialDateRequestType;
import com.ctrip.dcs.dsp.delay.service.interfaces.message.CheckSpecialDateResponseType;
import com.ctrip.dcs.dsp.delay.service.interfaces.message.InsertDelayDspOrderRequestType;
import com.ctrip.dcs.dsp.delay.service.interfaces.message.UpdateTaskInfoRequestType;
import com.ctrip.dcs.dsp.delay.service.interfaces.message.UpdateTaskInfoResponseType;
import com.ctrip.dcs.dsp.delay.util.DateUtil;
import com.ctrip.dcs.dsp.delay.util.JsonUtil;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.exception.BizException;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.annontation.ServiceLogTagPair;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;


@Component
@ServiceLogTagPair(key = "taskId")
public class UpdateTaskInfoExecutor extends AbstractRpcExecutor<UpdateTaskInfoRequestType, UpdateTaskInfoResponseType> implements Validator<UpdateTaskInfoRequestType> {

    private static Logger logger = LoggerFactory.getLogger(UpdateTaskInfoExecutor.class);
    @Resource
    private DelayDspOrderApplicationService delayDspApplicationService;

    @Override
    public UpdateTaskInfoResponseType execute(UpdateTaskInfoRequestType requestType) {
        UpdateTaskInfoResponseType responseType = new UpdateTaskInfoResponseType();
        try {
            DelayDspTask delayDspTask = new DelayDspTask();
            delayDspTask.setTaskId(requestType.getTaskId());
            delayDspTask.setMatchStatus(requestType.getMatchStatus());
            delayDspTask.setTaskStatus(requestType.getTaskStatus());
            String executeTime = requestType.getExecuteTime();
            delayDspTask.setExecuteTime(Objects.isNull(executeTime) ? null : DateUtil.parseDate(executeTime));
            String matchTime = requestType.getMatchTime();
            delayDspTask.setMatchTime(Objects.isNull(matchTime) ? null : DateUtil.parseDate(matchTime));
            logger.info("UpdateTaskInfoExecutor_delayDspTask", JsonUtil.toJson(requestType));
            delayDspApplicationService.updateTaskInfo(delayDspTask);
            return ServiceResponseUtils.success(responseType);
        } catch (Exception ex) {
            logger.error("UpdateTaskInfoExecutor", ex);
            return ServiceResponseUtils.error(responseType);
        }
    }

    @Override
    public void validate(AbstractValidator<UpdateTaskInfoRequestType> validator) {
        validator.ruleFor("taskId").notNull().notEmpty();
    }
}
