package com.ctrip.dcs.dsp.delay.carconfig.value;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class DriverProfitDayBaselineValue {

    public static DriverProfitDayBaselineValue DEFAULT = new DriverProfitDayBaselineValue();

    private Integer standardProfit;

    private Integer highProfit;

    public DriverProfitDayBaselineValue() {
        this.standardProfit = Integer.MAX_VALUE;
        this.highProfit = Integer.MAX_VALUE;
    }

    public DriverProfitDayBaselineValue(Integer standardProfit, Integer highProfit) {
        this.standardProfit = standardProfit;
        this.highProfit = highProfit;
    }
}
