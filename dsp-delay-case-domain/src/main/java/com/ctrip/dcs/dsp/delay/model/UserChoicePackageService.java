package com.ctrip.dcs.dsp.delay.model;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class UserChoicePackageService {

    // 订单号
    private String orderId;
    // 附加服务类型
    private Integer packageServiceType;
    // 附加服务码
    private String packageServiceCode;
    // 附加服务名称
    private String packageServiceName;
    // 附加服务描述
    private String packageServiceDesc;
    // 份数
    private Integer quantity;
    // 总价
    private BigDecimal totalPrice;
    // 创建时间
    private Date createTime;
    // 更新时间
    private Date updateTime;
    // 服务类型（国际车融合兼容使用）
    private String serviceType = "";
    // 附加服务状态
    private Integer packageServiceStatus = 0;
    // x商品id
    private Integer xSkuId;
    // 单价（供应币种）
    private BigDecimal vendorTotalPrice;
    // 用户币种
    private String userCurrency;
    // 供应币种
    private String vendorCurrency;
    //二级品类
    private String categoryCode;
    //首个免费
    private Boolean firstFree;
    //单价（用户币种）
    private BigDecimal unitPrice;
    //采购子单
    private String supplyOrderId;
}
