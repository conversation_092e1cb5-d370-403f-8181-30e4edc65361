package com.ctrip.dcs.dsp.delay.carconfig;

import com.ctrip.dcs.dsp.delay.carconfig.key.SpecialDateKey;
import com.ctrip.dcs.dsp.delay.carconfig.value.SpecialDateValue;
import com.ctrip.dcs.dsp.delay.util.JsonUtil;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.dianping.cat.utils.Pair;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Component;
import qunar.tc.qconfig.client.spring.QConfig;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2024/8/12 11:41
 */
@Component
public class SpecialDateConfig {
    
    private static final Logger logger = LoggerFactory.getLogger(SpecialDateConfig.class);
    private HashMap<String, SpecialDateConfig.Config> map = new HashMap<>();
    
    @QConfig("visualConfig_specialDateNoDelayTask_0.json")
    public void onChange(HashMap<String, SpecialDateConfig.Config> newMap) {//监听模式
        HashMap<String, SpecialDateConfig.Config> originMap = map;
        if(Optional.ofNullable(map).orElse(Maps.newHashMap()).size() == 0) {
            //应用刚启动的时候，map为空，直接赋值
            logger.info("SpecialDateConfig_onChange_configs_init", JsonUtil.toJson(newMap));
            map = newMap;
            return;
        }
        //获取新老不一致的配置
        Map<String, Pair<Config, Config>> diffMap  = diff(newMap, originMap);
        logger.info("SpecialDateConfig_onChange_configs", JsonUtil.toJson(diffMap));
        //新配置替换老配置
        map = newMap;
    }
    
    public Map<String, Pair<SpecialDateConfig.Config, SpecialDateConfig.Config>> diff(HashMap<String, Config> newMap, HashMap<String, Config> originMap) {
        if (Objects.isNull(newMap) || Objects.isNull(originMap)) {
            logger.info("SpecialDateConfig_onChange_configs_empty", "newMap or originMap is empty. newMap:" + JsonUtil.toJson(newMap) + ", originMap:" + JsonUtil.toJson(originMap));
            return Maps.newHashMap();
        }
        Map<String, Pair<SpecialDateConfig.Config, SpecialDateConfig.Config>> diffMap = Maps.newHashMap();
        for (Map.Entry<String, Config> entry : newMap.entrySet()) {
            Config originValue = originMap.get(entry.getKey());
            String newValueJson = JsonUtil.toJson(entry.getValue());
            String originValueJson = JsonUtil.toJson(originValue);
            if (Objects.equals(newValueJson, originValueJson)) {
                continue;
            }
            diffMap.put(entry.getKey(), new Pair<>(entry.getValue(), originValue));
        }
        return diffMap;
    }
    
    public SpecialDateValue get(Integer cityId, Integer carTypeId, Date date) {
        HashMap<String, SpecialDateConfig.Config> configMap = Optional.ofNullable(map).orElse(Maps.newHashMap());
        SpecialDateKey specialDateKey = new SpecialDateKey();
        specialDateKey.setCityId(cityId);
        specialDateKey.setCarTypeId(carTypeId);
        SpecialDateConfig.Config config = configMap.get(specialDateKey.toKey());
        if (Objects.isNull(config)) {
            return null;
        }
        SpecialDateValue specialDateValue = Optional.ofNullable(config.getValues()).orElse(Lists.newArrayList()).stream().filter(t -> t.match(date)).findFirst().orElse(null);
        if (Objects.isNull(specialDateValue)) {
            return null;
        }
        //命中了将cityId和carTypeId进行赋值
        specialDateValue.setCityId(cityId);
        specialDateValue.setCarTypeId(carTypeId);
        return specialDateValue;
    }
    
    
    public static class Config {
        
        private SpecialDateKey key;
        
        private List<SpecialDateValue> values;
        
        public SpecialDateKey getKey() {
            return key;
        }
        
        public void setKey(SpecialDateKey key) {
            this.key = key;
        }
        
        public List<SpecialDateValue> getValues() {
            return values;
        }
        
        public void setValues(List<SpecialDateValue> values) {
            this.values = values;
        }
    }
    
}
