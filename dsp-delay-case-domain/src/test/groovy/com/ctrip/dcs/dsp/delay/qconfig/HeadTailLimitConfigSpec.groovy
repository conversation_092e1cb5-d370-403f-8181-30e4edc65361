package com.ctrip.dcs.dsp.delay.qconfig


import com.ctrip.dcs.dsp.delay.qconfig.key.DriverHighProfitLineDayKeyVO
import com.ctrip.dcs.dsp.delay.qconfig.key.HeadTailLimitKeyVO
import com.ctrip.dcs.dsp.delay.qconfig.value.DriverHighProfitLineDayValueVO
import com.ctrip.dcs.dsp.delay.qconfig.value.HeadTailLimitValueVO
import com.google.common.collect.Lists
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 */
class HeadTailLimitConfigSpec extends Specification{

    def map = new HashMap();

    def config = new HeadTailLimitConfig (configMap: map)

    @Unroll
    def "test"() {
        given: "Mock数据"
        HeadTailLimitKeyVO keyVO = new HeadTailLimitKeyVO(cityId, carTypeId)
        HeadTailLimitValueVO valueVO = new HeadTailLimitValueVO(head, tail, 1D)
        map.put(key, new BaseCarConfig.Config(keyVO, Lists.newArrayList(valueVO)))

        when: "执行校验方法"
        def result = config.get(1, 1)

        then: "验证校验结果"
        result.getHeadLimit() == head
        result.getTailLimit() == tail

        where:
        key   | cityId | carTypeId || head | tail
        "1#1" | 1      | 1         || 100  | 10
        "1#2" | 1      | 2         || -1   | -1
        "0#0" | 0      | 0         || 100  | 10
        "1#0" | 1      | 0         || 100  | 10
    }
}
