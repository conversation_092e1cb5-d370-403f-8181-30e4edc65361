package com.ctrip.dcs.dsp.delay.infrastructure.soa;

import com.ctrip.dcs.dsp.self.order.query.api.SelfOrderQueryServiceClient;
import com.ctrip.dcs.self.order.query.api.*;
import com.ctrip.igt.framework.soa.client.ServiceClient;

/**
 * <AUTHOR>
 */
@ServiceClient(value = SelfOrderQueryServiceClient.class, format = "json")
public interface SelfOrderQueryServiceProxy {
    /**
     * 查询订单信息
     * @param queryOrderDetailRequestType
     * @return
     */
    QueryOrderDetailResponseType queryOrderDetail(QueryOrderDetailRequestType queryOrderDetailRequestType);

    QueryOrderDetailForScheduleResponseType queryOrderDetailForSchedule(QueryOrderDetailForScheduleRequestType queryOrderDetailRequestType);


    QueryOrderRedispatchRecordsResponseType queryOrderRedispatchRecords(QueryOrderRedispatchRecordsRequestType request);

}
