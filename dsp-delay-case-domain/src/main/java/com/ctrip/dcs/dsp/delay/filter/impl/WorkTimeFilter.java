package com.ctrip.dcs.dsp.delay.filter.impl;

import com.ctrip.dcs.dsp.delay.model.DelayDspTask;
import com.ctrip.dcs.dsp.delay.model.Driver;
import com.ctrip.dcs.dsp.delay.util.DateUtil;
import com.google.common.base.Splitter;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Component("workTimeFilter")
public class WorkTimeFilter extends CommonFilter {

    private static Splitter WORK_TIME_SPLITTER = Splitter.on('~').trimResults().omitEmptyStrings();

    @Override
    public boolean filter(Driver driver, DelayDspTask task) {
        if (CollectionUtils.isEmpty(driver.getWorkTimes())) {
            return false;
        }
        String date = DateUtil.formatDate(task.getBeginTime(), DateUtil.DAY_FMT);
        for (String s : driver.getWorkTimes()) {
            List<String> startAndEndWorkTime = WORK_TIME_SPLITTER.splitToList(s);

            Date begin = DateUtil.parseDate(date + " " + startAndEndWorkTime.get(0) + ":00");
            Date end = DateUtil.parseDate(date + " " + startAndEndWorkTime.get(1) + ":59");
            if (begin == null || end == null) {
                return false;
            }
            if (end.before(begin)) {
                end = DateUtil.addDays(end, 1);
            }
            if (task.getBeginTime() == null || task.getEndTime() == null || task.getBeginTime().compareTo(begin) < 0 || task.getEndTime().compareTo(end) > 0) {
                return false;
            }
        }
        return true;
    }

    @Override
    String reason() {
        return "work time out of range";
    }
}
