package com.ctrip.dcs.dsp.delay.model

import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 */
class AvailableDriverTest extends Specification {

    @Unroll
    def "test"() {
        given: "Mock数据"
        AvailableDriver available = new AvailableDriver([new AvailableDriverDetail(order: new DelayDspOrder(orderId: "1"), driver: new Driver(driverId: "2"), value: "1")])

        when: "执行校验方法"
        boolean result = available.isAvailable(orderId, driverId)

        then: "验证校验结果"
        result == flag

        where:
        orderId | driverId || flag
        "1"     | 2L       || true
        "1"     | 1L       || false
        "2"     | 2L       || false
    }
}
