package com.ctrip.dcs.dsp.application.service.impl;

import com.ctrip.dcs.dsp.application.request.DispatchDelayDspTaskRequest;
import com.ctrip.dcs.dsp.delay.context.DelayDspContext;
import com.ctrip.dcs.dsp.delay.context.DispatcherManager;
import com.ctrip.dcs.dsp.delay.enums.DelayDspTaskStatus;
import com.ctrip.dcs.dsp.delay.enums.DelayDspTaskType;
import com.ctrip.dcs.dsp.delay.enums.YesOrNo;
import com.ctrip.dcs.dsp.delay.factory.DelayDspContextFactory;
import com.ctrip.dcs.dsp.delay.model.DelayDspOrder;
import com.ctrip.dcs.dsp.delay.model.DelayDspTask;
import com.ctrip.dcs.dsp.delay.model.DispatchResult;
import com.ctrip.dcs.dsp.delay.repository.DelayDspOrderRepository;
import com.ctrip.dcs.dsp.delay.repository.DelayDspTaskRepository;
import com.ctrip.dcs.dsp.delay.service.DispatchTaskService;
import com.ctrip.dcs.dsp.delay.util.JsonUtil;
import com.ctrip.dcs.dsp.delay.util.MetricsUtil;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Component("shortOrderDspTaskApplicationServiceImpl")
public class ShortOrderDispatchTaskApplicationServiceImpl extends DispatchTaskApplicationServiceImpl {

    private static final Logger logger = LoggerFactory.getLogger(ShortOrderDispatchTaskApplicationServiceImpl.class);
    @Autowired
    private DelayDspTaskRepository delayDspTaskRepository;

    @Autowired
    private DelayDspOrderRepository delayDspOrderRepository;

    @Autowired
    private DelayDspContextFactory delayDspContextFactory;

    @Override
    public void dispatch(DispatchDelayDspTaskRequest request) {
        DelayDspTask task = delayDspTaskRepository.queryByTaskId(request.getTaskId());
        if (Objects.isNull(task)) {
            logger.info("shortOrderDspTaskApplicationServiceImpl.dispatch", "task is null! task id: {}", request.getTaskId());
            return;
        }
        if (!Objects.equals(task.getTaskStatus(), DelayDspTaskStatus.EXECUTING.getCode())) {
            logger.info("shortOrderDspTaskApplicationServiceImpl.dispatch", "task is complete! task id: {}", request.getTaskId());
            return;
        }
        List<DelayDspOrder> orders = delayDspOrderRepository.queryByTaskId(task.getTaskId(), YesOrNo.YES.getCode());
        if (CollectionUtils.isEmpty(orders)) {
            logger.info("shortOrderDspTaskApplicationServiceImpl.dispatch", "order is empty! task id: {}", request.getTaskId());
            return;
        }
        long start = System.currentTimeMillis();
        DispatchResult result = new DispatchResult(orders);
        try {
            DelayDspContext delayDspContext = delayDspContextFactory.createSDDelayDspContext(task, orders);

            DispatchTaskService dispatchTaskService = DispatcherManager.get(DelayDspTaskType.of(task.getTaskType()));
            // 派单
            result = dispatchTaskService.dispatch(delayDspContext);
            logger.info("shortOrderDspTaskApplicationServiceImpl_dispatch", JsonUtil.toJson(result));
            // 创建司机单
            batchCreateDriverOrder(result.getMatchDetails());
            // 更新任务状态
            delayDspTaskRepository.update(task.getTaskId(), DelayDspTaskStatus.EXECUTION_COMPLETE.getCode());
        } catch (Exception e) {
            MetricsUtil.recordValue("sd.dsp.task.error", 1);
            logger.error("shortOrderDspTaskApplicationServiceImpl.dispatch.error", e);
            // 派发异常，将全部订单都改派出去
        } finally {
            long end = System.currentTimeMillis();
            MetricsUtil.recordTime("sd.dsp.total.time", end - start);
            // 保存派单结果
            saveDispatchResult(task, result.getDetails());
        }
    }

}
