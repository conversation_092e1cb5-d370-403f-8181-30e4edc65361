package com.ctrip.dcs.dsp.delay.service;

import com.ctrip.dcs.dsp.delay.model.SupplyOrder;
import com.ctrip.dcs.dsp.delay.validator.ValidatorDTO;

/**
 * <AUTHOR>
 * @since 2024/8/15 16:14
 */
public interface DelayDspOrderService {
    
    /**
     * 记录延后派入池原因
     * @param code
     * @param supplyOrder
     * @param startTime
     * @param endTime
     */
    void recordInsertDspPoolReasonRecord(ValidatorDTO code, SupplyOrder supplyOrder, long startTime, long endTime);
    
    /**
     * 校验是否命中特殊日期
     * @param cityId
     * @param carTypeId
     * @param sysExpectBookTime
     * @return
     */
    Boolean checkSpecialDate(Integer cityId, Integer carTypeId, String sysExpectBookTime);
    
    
}
