package com.ctrip.dcs.dsp.delay.carconfig.value;

import com.ctrip.dcs.dsp.delay.carconfig.Value;
import com.ctrip.dcs.dsp.delay.enums.YesOrNo;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class OrderDisLimitValue implements Value<Double> {

    private Integer coopMode;

    private Integer highPriorityDriver;

    private Integer limitDisLower;

    private Integer limitDisUpper;

    private Integer takenLimit;

    private Integer inUse;

    @Override
    public boolean match(Double kiloLength) {
        return this.getInUse() == YesOrNo.YES.getCode() && kiloLength >= this.getLimitDisLower() && kiloLength <= this.getLimitDisUpper() ;
    }
}
