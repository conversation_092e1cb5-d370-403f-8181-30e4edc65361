package com.ctrip.dcs.dsp.delay.conflict.impl;

import com.ctrip.dcs.dsp.delay.conflict.Check;
import com.ctrip.dcs.dsp.delay.conflict.CheckCode;
import com.ctrip.dcs.dsp.delay.conflict.ConflictContext;
import com.ctrip.dcs.dsp.delay.gateway.GeoGateway;
import com.ctrip.dcs.dsp.delay.model.DelayDspOrder;
import com.ctrip.dcs.dsp.delay.model.DelayDspTask;
import com.ctrip.dcs.dsp.delay.model.DispatcherConfig;
import com.ctrip.dcs.dsp.delay.model.Driver;
import com.ctrip.dcs.dsp.delay.model.Position;
import com.ctrip.dcs.dsp.delay.model.Route;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 */
@Component("prevMileageCheck")
public class PrevMileageCheck implements Check {

    @Autowired
    protected GeoGateway geoGateway;

    @Override
    public CheckCode check(ConflictContext context) {
        DelayDspTask task = context.getDelayDspTask();
        DelayDspOrder prev = context.getPrev();
        DelayDspOrder next = context.getNext();
        Driver driver = context.getDriverAggregation().getDriver();
        Position position;
        if (Objects.nonNull(prev)) {
            position = new Position(prev, next);
        } else {
            position = new Position(driver, next);
        }
        Route route = geoGateway.queryRoute(task.getTaskId(), position);
        DispatcherConfig config = context.getConfig();
        // 后向单的前向里程价值是否达标
        boolean ok = (next.getDriverOrderFee() / (next.getKiloLength() + route.getDistance())) >= config.getOrderMileageValueRate(next.getSysExpectBookTime(), next.getKiloLength());
        return ok ? CheckCode.OK : CheckCode.PREV_MILEAGE_CONFLICT;
    }
}
