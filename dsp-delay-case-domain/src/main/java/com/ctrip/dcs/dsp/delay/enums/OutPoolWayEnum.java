package com.ctrip.dcs.dsp.delay.enums;

public enum OutPoolWayEnum {

    ARRIVER_OUT_NO_DRIVER_TIME("arriverOutNoDriverTime"),
    ARRIVER_OUT_DRIVER_TIME("arriverOutDriverTime"),
    PRESSED("pressed" ),
    MATCH_NO_DRIVER("matchNoDriver" ),
    MATCH_DRIVER("matchDriver" );
    ;

    private String way;

    OutPoolWayEnum(String way) {
        this.way = way;
    }

    public String getWay() {
        return way;
    }
}