namespace java com.ctrip.igt
namespace csharp IGT.API

/*服务请求头*/
class RequestHeader {
    /*语言【zh-cn：中文（简体），en-us：英文(美国)】*/
    string language,
    /*币种【cny-人民币,hkd-港币,eur-欧元,usd-美元,jpy-日元,krw-韩币,twd-台币,sgd-新币,gbp-英镑】*/
    string currency,
    /*渠道ID*/
    int channelId,
    /*产品形态 【17-接机，18-送机，55-点对点，23-按天包车，66-线路包车，88-定制包车】*/
    int patternType,
    /*产品形态组【1718-接送机，55-点对点，23-按天包车，66-线路包车，88-定制包车】*/
    int patternGroup,
    /*业务形态【32-国内，33-海外】*/
    int businessType,
    /*业务来源【online,h5,app】*/
    string severFrom,
    /*客户端ID【online-客户端IP地址，无线-框架生成的客户端ID】*/
    string cid,
    /*ubt相关参数*/
    CommonUBTDTO ubt,
    /*【用户明文UID】*/
    string uid,
    /*客户端IP地址*/
    string ip,
    /*手机系统【ios，android】*/
    string os,
    /*技术模式【h5,hybrid,crn,mini(小程序)*/
    string mode,
    /*无线版本号(如7月31号发布7.6.2版本，【online：0，H5：0.0731,APP:7062.0731】)*/
    decimal wirelessVersion,
    /*全局TraceId*/
    string globalTraceId;
    /*风控所需token*/
    string rmsToken;
}

/*服务响应结果*/
class ResponseResult {
    /*是否成功*/
    bool success,
    /*响应Code*/
    string returnCode,
    /*响应信息*/
    string returnMessage,
}

/*rest服务请求头*/
class RestRequestHeader {
    /*语言【zh-cn：中文（简体），en-us：英文(美国)】*/
    string lang,
    /*币种【cny-人民币,hkd-港币,eur-欧元,usd-美元,jpy-日元,krw-韩币,twd-台币,sgd-新币,gbp-英镑】*/
    string cury,
    /*渠道ID*/
    int channelid,
    /*产品形态 【17-接机，18-送机，55-点对点，23-按天包车，66-线路包车，88-定制包车】*/
    int pttype,
    /*产品形态组【1718-接送机，55-点对点，23-按天包车，66-线路包车，88-定制包车】*/
    int ptgroup,
    /*业务形态【32-国内，33-海外】*/
    int biztype,
    /*业务来源【online,h5,app】*/
    string sf,
    /*客户端ID【online-客户端IP地址，无线-框架生成的客户端ID】*/
    string cid,
    /*ubt相关参数*/
    CommonUBTDTO ubt,
    /*分销信息*/
    CommonAllianceDTO union,
    /*【用户明文UID】*/
    string uid,
    /*客户端IP地址*/
    string ip,
    /*online登录态*/
    string ticket,
    /*用户定位信息*/
    CommonGPSDTO gps,
    /*手机系统【ios，android】*/
    string os,
    /*技术模式【h5,hybrid,crn,mini(小程序)*/
    string mode,
    /*无线版本号(如7月31号发布7.6.2版本，【online：0，H5：0.0731,APP:7062.0731】)*/
    decimal wlver,
    /*鉴权标示*/
    string token,
    /*全局TraceId*/
    string globalTraceId;
    /*风控所需token*/
    string rmstoken;
}

/*rest服务响应结果*/
class RestResponseResult {
    /*20X-成功，50X-失败*/
    string rcode,
    /*响应信息*/
    string rmsg,
    /*响应扩展信息*/
    map<string,string> rext,
}

class CommonUBTDTO {
    /* abtest版本，例如：M:4,160608_ind_phnum:A; */
    map<string,string> abtest;
    /* 页面id */
    string pageid;
    /*pageview标识 */
    string pvid,
    /*session 标识*/
    string sid,
    /*网站用户身份标识*/
    string vid
}

class CommonAllianceDTO {
    /*主渠道ID*/
    string aid,
    /*副渠道ID*/
    string sid,
    /*分销用户id*/
    string ouid
}

class CommonGPSDTO {
    /*定位经纬度*/
    string lat,
    /*定位经纬度*/
    string lng,
    /*定位城市id*/
    int cid,
    /*定位城市名称*/
    string cnm,
}
