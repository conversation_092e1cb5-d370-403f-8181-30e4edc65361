package com.ctrip.dcs.dsp.delay.infrastructure.thread;

import com.ctrip.igt.framework.common.concurrent.threadpool.CThreadPool;
import org.springframework.stereotype.Component;

import java.util.concurrent.ExecutorService;

/**
 * <AUTHOR>
 * @since 2025/6/3 22:00
 */
@Component
public class EnableConfigThreadPool {
    
    
    public ExecutorService get(String threadName) {
        return CThreadPool.pool(threadName);
    }
    
}
