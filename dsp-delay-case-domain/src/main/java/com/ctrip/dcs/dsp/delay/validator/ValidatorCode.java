package com.ctrip.dcs.dsp.delay.validator;

/**
 * <AUTHOR>
 */
public enum ValidatorCode {

    OK,
    ERROR,
    OFF,
    ILLEGAL_STATUS,
    OTA,
    MAIN_ORDER_EXIST,
    NOT_JNT,
    CHANNEL,
    SKU,
    ORDER_DIS_LIMIT,
    VIRTUAL_DSP_LIMIT,
    TASK_EXECUTED,
    TASK_NOT_EXIST,
    NOT_FULL_TIME_DRIVER,
    NO_DRIVER,
    UID,
    PICK_UP_CARD_LIMIT,
    HIGH_LEVEL_ORDER_NOT_GOLD_DRIVER_LIMIT,
    IGT,
    T,
    SPECIAL_DATE,
    GRAYSCALE_UID,
    GRAYSCALE_KM,
    GRAY<PERSON><PERSON>E_CITY_RATE,
    VIP,
    DOWNGRADE_CAR_TYPE,
    TRANSPORT_GROUP_EMPTY,
    TRANSPORT_GROUP_MODE_NOT_MATCH,
    REDISPATCH,
    ;
}
