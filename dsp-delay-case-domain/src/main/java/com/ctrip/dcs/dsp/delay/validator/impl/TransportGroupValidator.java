package com.ctrip.dcs.dsp.delay.validator.impl;

import com.ctrip.dcs.dsp.delay.carconfig.DelayTaskConfig;
import com.ctrip.dcs.dsp.delay.carconfig.value.DelayDspTaskValue;
import com.ctrip.dcs.dsp.delay.enums.DelayDspTaskType;
import com.ctrip.dcs.dsp.delay.enums.TransportGroupMode;
import com.ctrip.dcs.dsp.delay.gateway.TransportGateway;
import com.ctrip.dcs.dsp.delay.model.SupplyOrder;
import com.ctrip.dcs.dsp.delay.model.TransportGroupInfo;
import com.ctrip.dcs.dsp.delay.qconfig.DelayDspCommonQConfig;
import com.ctrip.dcs.dsp.delay.validator.Validator;
import com.ctrip.dcs.dsp.delay.validator.ValidatorCode;
import com.ctrip.dcs.dsp.delay.validator.ValidatorDTO;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 运力组模式拦截器
 * <AUTHOR>
 */
@Component("transportGroupValidator")
public class TransportGroupValidator implements Validator {

    private static final Logger logger = LoggerFactory.getLogger(TransportGroupValidator.class);

    @Autowired
    private TransportGateway transportGateway;

    @Autowired
    private DelayTaskConfig delayTaskConfig;


    @Override
    public ValidatorDTO validate(SupplyOrder order) {
        DelayDspTaskValue value = delayTaskConfig.get(order.getCityId(), order.getCarTypeId(), order.getSysExpectBookTime(),order.getShortDisOrder());
        if (value == null) {
            logger.info("TransportGroupValidator", "task not exist.order: {}", order.getOrderId());
            return new ValidatorDTO(ValidatorCode.TASK_NOT_EXIST);
        }
        List<TransportGroupInfo> transportGroupList = transportGateway.queryTransportGroups(order);
        if (CollectionUtils.isEmpty(transportGroupList)) {
            logger.info("TransportGroupValidator", "transport group is empty! order id: {}", order.getOrderId());
            return new ValidatorDTO(ValidatorCode.TRANSPORT_GROUP_EMPTY);
        }
        for (TransportGroupInfo transportGroup : transportGroupList) {
            if (DelayDspTaskType.isDP(value.getType()) && TransportGroupMode.isRegisterMode(transportGroup.getTransportGroupMode())) {
                // DP类型，必须包含报名制运力组
                return new ValidatorDTO(ValidatorCode.OK);
            }
            if (DelayDspTaskType.isKM(value.getType()) && TransportGroupMode.isFullTimeOrRegisterMode(transportGroup.getTransportGroupMode())) {
                // KM类型，必须包含报名制运力组或全职运力组
                return new ValidatorDTO(ValidatorCode.OK);
            }
        }
        logger.info("TransportGroupValidator", "transport group mode not match! order id: {}", order.getOrderId());
        return new ValidatorDTO(ValidatorCode.TRANSPORT_GROUP_MODE_NOT_MATCH);
    }
}
