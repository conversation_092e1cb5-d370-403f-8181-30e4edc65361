package com.ctrip.dcs.dsp.delay.conflict.impl

import com.ctrip.dcs.dsp.delay.conflict.CheckCode
import com.ctrip.dcs.dsp.delay.conflict.ConflictContext
import com.ctrip.dcs.dsp.delay.gateway.GeoGateway
import com.ctrip.dcs.dsp.delay.model.*
import com.ctrip.dcs.dsp.delay.util.DateUtil
import com.google.common.collect.Lists
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 */
class TailOrderCheckSpec extends Specification {

    def geoGateway = Mock(GeoGateway)

    def config = Mock(DispatcherConfig)

    def check = new TailOrderCheck(geoGateway: geoGateway)

    @Unroll
    def "check"() {

        given: "Mock数据"
        def task = new DelayDspTask(taskId: 1, endTime: DateUtil.parseDate("2022-08-29 13:00:00"))
        def next = new DelayDspOrder(sysExpectBookTime: DateUtil.parseDate(t1), predictServiceStopTime: DateUtil.parseDate(t2), driverId: driverId,isDelay: 1)
        def driver = new Driver(addressLongitude: 100, addressLatitude: 31)
        def context = new ConflictContext(list: Lists.newArrayList(next), delayDspTask: task, config: config, driverAggregation: new DriverAggregation(driver: driver, outTimeOrders: orders))

        geoGateway.queryRoute(_, _) >> new Route(duration: dur)
        config.getTailLimit() >> limit

        when: "执行校验方法"
        def res = check.check(context)

        then: "验证校验结果"
        res == code

        where:
        driverId | orders                                | t1                    | t2                    | dur | limit || code
        null     | null                                  | "2022-08-29 10:30:00" | "2022-08-29 12:30:00" | 15D | 10D   || CheckCode.OK
        null     | null                                  | "2022-08-29 11:30:00" | "2022-08-29 12:30:00" | 45D | 10D   || CheckCode.TAIL_LIMIT_CONFLICT
        "1"      | null                                  | "2022-08-29 11:30:00" | "2022-08-29 13:30:00" | 15D | 30D   || CheckCode.OK
        null     | null                                  | "2022-08-29 11:30:00" | "2022-08-29 13:30:00" | 45D | 30D   || CheckCode.TAIL_LIMIT_CONFLICT
        null     | null                                  | "2022-08-29 11:30:00" | "2022-08-29 13:30:00" | 15D | 30D   || CheckCode.OK
        null     | null                                  | "2022-08-29 11:30:00" | "2022-08-29 13:30:00" | 15D | -1D   || CheckCode.OK
        null     | newOrders("2022-08-29 13:30:00") | "2022-08-29 11:30:00" | "2022-08-29 13:30:00" | 15D | 10D   || CheckCode.OK
    }

    def newPrev() {
        return new DelayDspOrder(fromLongitude: 100, fromLatitude: 31, fromCoordsys: "", fromHash: "")
    }

    def newOrders(String time) {
        return Lists.newArrayList(new SupplyOrder(sysExpectBookTime: DateUtil.parseDate(time)))
    }
}
