package com.ctrip.dcs.dsp.delay.infrastructure.converter;

import com.ctrip.dcs.dsp.delay.enums.YesOrNo;
import com.ctrip.dcs.dsp.delay.infrastructure.util.OrderExtendAttributeUtil;
import com.ctrip.dcs.dsp.delay.model.OrderPackageServiceDTO;
import com.ctrip.dcs.dsp.delay.model.PredictPriceInfo;
import com.ctrip.dcs.dsp.delay.model.SupplyOrder;
import com.ctrip.dcs.dsp.delay.util.DateUtil;
import com.ctrip.dcs.dsp.delay.util.JsonUtil;
import com.ctrip.dcs.self.order.query.api.QueryOrderDetailForScheduleResponseType;
import com.ctrip.dcs.self.order.query.api.QueryOrderDetailResponseType;
import com.ctrip.dcs.self.order.query.dto.BaseDetail;
import com.ctrip.dcs.self.order.query.dto.EstimatedCostInfo;
import com.ctrip.dcs.self.order.query.dto.OrderConfirmRecord;
import com.ctrip.dcs.self.order.query.dto.OrderDetail;
import com.ctrip.dcs.self.order.query.dto.OrderExtendAttributeDTO;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.codehaus.jackson.type.TypeReference;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
public class SupplyOrderConverter {

    private static final Logger logger = LoggerFactory.getLogger(SupplyOrderConverter.class);

    public SupplyOrder toSupplyOrderForSchedule(QueryOrderDetailForScheduleResponseType response) {
        if (Objects.isNull(response) || Objects.isNull(response.getOrderDetail())) {
            return null;
        }
        SupplyOrder supplyOrder = new SupplyOrder();
        supplyOrder.setPredictPriceInfo(new PredictPriceInfo());
        BaseDetail baseDetail = response.getOrderDetail().getBaseDetail();
        if (Objects.nonNull(baseDetail)) {
            supplyOrder.setOrderId(baseDetail.getDspOrderId());
            supplyOrder.setSourceOrderId(baseDetail.getUserOrderId());
            if (Objects.nonNull(baseDetail.getCityId())) {
                supplyOrder.setCityCode(baseDetail.getCityId().toString());
            }
            supplyOrder.setCityId(baseDetail.getCityId());
            supplyOrder.setCarTypeId(baseDetail.getVehicleGroupId());
            supplyOrder.setServiceType(baseDetail.getServiceType());
            supplyOrder.setOrderStatus(baseDetail.getOldOrderStatus());
            supplyOrder.setOrderStatusDetail(baseDetail.getOldOrderStatusDetail());
            if (Objects.nonNull(baseDetail.getEstimatedUseTime())) {
                supplyOrder.setSysExpectBookTime(DateUtil.parseDate(baseDetail.getEstimatedUseTime()));
            }
            if (Objects.nonNull(baseDetail.getPredicServiceStopTimeBj())) {
                supplyOrder.setPredicServiceStopTime(DateUtil.parseDate(baseDetail.getPredicServiceStopTimeBj()));
            }
            supplyOrder.setIsOtaBookOrder(YesOrNo.NO.getCode());
            if (Objects.nonNull(baseDetail.getSkuId())) {
                supplyOrder.setSkuId(baseDetail.getSkuId().longValue());
            }
            if (Objects.nonNull(baseDetail.getSupplierId())) {
                supplyOrder.setSupplierId(baseDetail.getSupplierId().longValue());
            }
            supplyOrder.setCategoryCode(baseDetail.getCategoryCode());
            supplyOrder.setActualFromLongitude(baseDetail.getFromLongitude());
            supplyOrder.setActualFromLatitude(baseDetail.getFromLatitude());
            supplyOrder.setActualFromCoordsys(baseDetail.getCoordType());
            supplyOrder.setActualToLongitude(baseDetail.getToLongitude());
            supplyOrder.setActualToLatitude(baseDetail.getToLatitude());
            supplyOrder.setActualToCoordsys(baseDetail.getCoordType());
            supplyOrder.setUid(baseDetail.getUid());
            supplyOrder.setHighGradeOrder(baseDetail.getHighGradeOrder());
            if (Objects.nonNull(baseDetail.getDistributionChannelId())) {
                supplyOrder.setDistributionChannel(baseDetail.getDistributionChannelId().longValue());
            }
            if (Objects.nonNull(baseDetail.getEstimatedKm())) {
                supplyOrder.getPredictPriceInfo().setKiloLength(baseDetail.getEstimatedKm().doubleValue());

            }
            if (Objects.nonNull(baseDetail.getEstimatedMin())) {
                supplyOrder.getPredictPriceInfo().setTimeLength(baseDetail.getEstimatedMin().intValue());
            }
            supplyOrder.setBizAreaType(baseDetail.getBizAreaType());
            supplyOrder.setOrderSysType(baseDetail.getOrderSysType());
            if (Objects.nonNull(baseDetail.getFromPoiDTO())) {
                supplyOrder.setDeptCarPlaceId(baseDetail.getFromPoiDTO().getDeptCarPlaceId());
            }
            if (Objects.nonNull(baseDetail.getToPoiDTO())) {
                supplyOrder.setArriveCarPlaceId(baseDetail.getToPoiDTO().getArriveCarPlaceId());
            }
        }
        return supplyOrder;
    }


    public SupplyOrder toSupplyOrder(QueryOrderDetailResponseType response) {
        if (Objects.isNull(response) || Objects.isNull(response.getOrderDetail())) {
            return null;
        }
        SupplyOrder supplyOrder = new SupplyOrder();
        supplyOrder.setPredictPriceInfo(new PredictPriceInfo());
        BaseDetail baseDetail = response.getOrderDetail().getBaseDetail();
        if (Objects.nonNull(baseDetail)) {
            supplyOrder.setOrderId(baseDetail.getDspOrderId());
            supplyOrder.setSourceOrderId(baseDetail.getUserOrderId());
            if (Objects.nonNull(baseDetail.getCityId())) {
                supplyOrder.setCityCode(baseDetail.getCityId().toString());
            }
            supplyOrder.setCityId(baseDetail.getCityId());
            supplyOrder.setCarTypeId(baseDetail.getVehicleGroupId());
            supplyOrder.setServiceType(baseDetail.getServiceType());
            supplyOrder.setOrderStatus(baseDetail.getOldOrderStatus());
            supplyOrder.setOrderStatusDetail(baseDetail.getOldOrderStatusDetail());
            if (Objects.nonNull(baseDetail.getEstimatedUseTime())) {
                supplyOrder.setSysExpectBookTime(DateUtil.parseDate(baseDetail.getEstimatedUseTime()));
            }
            if (Objects.nonNull(baseDetail.getPredicServiceStopTimeBj())) {
                // fixme 接口没返回当地时间，但是延后派都是境内的订单，所以暂时可以使用北京时间
                supplyOrder.setPredicServiceStopTime(DateUtil.parseDate(baseDetail.getPredicServiceStopTimeBj()));
            }
            supplyOrder.setIsOtaBookOrder(YesOrNo.NO.getCode());
            if (Objects.nonNull(baseDetail.getSkuId())) {
                supplyOrder.setSkuId(baseDetail.getSkuId().longValue());
            }
            supplyOrder.setCategoryCode(baseDetail.getCategoryCode());
            supplyOrder.setActualFromLongitude(baseDetail.getFromLongitude());
            supplyOrder.setActualFromLatitude(baseDetail.getFromLatitude());
            supplyOrder.setActualFromCoordsys(baseDetail.getCoordType());
            supplyOrder.setActualToLongitude(baseDetail.getToLongitude());
            supplyOrder.setActualToLatitude(baseDetail.getToLatitude());
            supplyOrder.setActualToCoordsys(baseDetail.getCoordType());
            supplyOrder.setUid(baseDetail.getUid());
            supplyOrder.setHighGradeOrder(baseDetail.getHighGradeOrder());
            if (Objects.nonNull(baseDetail.getDistributionChannelId())) {
                supplyOrder.setDistributionChannel(baseDetail.getDistributionChannelId().longValue());
            }
            if (Objects.nonNull(baseDetail.getEstimatedKm())) {
                supplyOrder.getPredictPriceInfo().setKiloLength(baseDetail.getEstimatedKm().doubleValue());

            }
            if (Objects.nonNull(baseDetail.getEstimatedMin())) {
                supplyOrder.getPredictPriceInfo().setTimeLength(baseDetail.getEstimatedMin().intValue());
            }
            supplyOrder.setBizAreaType(baseDetail.getBizAreaType());
            supplyOrder.setOrderSysType(baseDetail.getOrderSysType());
            supplyOrder.setOrderPackageServiceCodes(toOrderPackageServiceCodes(baseDetail));
            if (Objects.nonNull(baseDetail.getFromPoiDTO())) {
                supplyOrder.setDeptCarPlaceId(baseDetail.getFromPoiDTO().getDeptCarPlaceId());
            }
            if (Objects.nonNull(baseDetail.getToPoiDTO())) {
                supplyOrder.setArriveCarPlaceId(baseDetail.getToPoiDTO().getArriveCarPlaceId());
            }
        }
        EstimatedCostInfo estimatedCostInfo = response.getOrderDetail().getEstimatedCostInfo();
        if (Objects.nonNull(estimatedCostInfo)) {
            if (Objects.nonNull(estimatedCostInfo.getCostAmount())) {
                supplyOrder.setDriverOrderFee(estimatedCostInfo.getCostAmount().doubleValue());
                supplyOrder.getPredictPriceInfo().setDrivTotalFee(estimatedCostInfo.getCostAmount().doubleValue());
            }
        }
        OrderConfirmRecord orderConfirmRecord = response.getOrderDetail().getOrderConfirmRecord();
        if (Objects.nonNull(orderConfirmRecord)) {
            if (Objects.nonNull(orderConfirmRecord.getDriverId())) {
                supplyOrder.setDriverId(orderConfirmRecord.getDriverId().toString());
            }
            supplyOrder.setTransportGroupId(orderConfirmRecord.getTransportGroupId());
            supplyOrder.setSupplierId(orderConfirmRecord.getSupplierId());
        }
        supplyOrder.setShortDisOrder(baseDetail.getShortDisOrder());
        setIsVipUserLevel(supplyOrder, response.getOrderDetail());
        setIsDowngradeCarType(supplyOrder, response.getOrderDetail());
        return supplyOrder;
    }


    public List<SupplyOrder> toSupplyOrderList(List<OrderDetail> orderDetails) {
        return orderDetails.stream().map(x -> {
            SupplyOrder supplyOrder = new SupplyOrder();
            supplyOrder.setPredictPriceInfo(new PredictPriceInfo());
            BaseDetail baseDetail = x.getBaseDetail();
            if (Objects.nonNull(baseDetail)) {
                supplyOrder.setOrderId(baseDetail.getDspOrderId());
                supplyOrder.setSourceOrderId(baseDetail.getUserOrderId());
                if (Objects.nonNull(baseDetail.getCityId())) {
                    supplyOrder.setCityCode(baseDetail.getCityId().toString());
                }
                supplyOrder.setCityId(baseDetail.getCityId());
                supplyOrder.setCarTypeId(baseDetail.getVehicleGroupId());
                supplyOrder.setOrderStatus(baseDetail.getOldOrderStatus());
                supplyOrder.setOrderStatusDetail(baseDetail.getOldOrderStatusDetail());
                if (Objects.nonNull(baseDetail.getEstimatedUseTime())) {
                    supplyOrder.setSysExpectBookTime(DateUtil.parseDate(baseDetail.getEstimatedUseTime()));
                }
                if (Objects.nonNull(baseDetail.getPredicServiceStopTimeBj())) {
                    // fixme 接口没返回当地时间，但是延后派都是境内的订单，所以暂时可以使用北京时间
                    supplyOrder.setPredicServiceStopTime(DateUtil.parseDate(baseDetail.getPredicServiceStopTimeBj()));
                }
            }
            EstimatedCostInfo estimatedCostInfo = x.getEstimatedCostInfo();
            if (Objects.nonNull(estimatedCostInfo)) {
                if (Objects.nonNull(estimatedCostInfo.getCostAmount())) {
                    supplyOrder.setDriverOrderFee(estimatedCostInfo.getCostAmount().doubleValue());
                }
            }
            setIsVipUserLevel(supplyOrder, x);
            setIsDowngradeCarType(supplyOrder, x);
            return supplyOrder;
        }).collect(Collectors.toList());
    }

    public Set<String> toOrderPackageServiceCodes(BaseDetail baseDetail) {
        try {
            if (StringUtils.isNotBlank(baseDetail.getXproductInfo())) {
                logger.info("SupplyOrderConverter", "xproductInfo:{}", baseDetail.getXproductInfo());
                List<OrderPackageServiceDTO> list = JsonUtil.fromJson(baseDetail.getXproductInfo(), new TypeReference<List<OrderPackageServiceDTO>>() {
                });
                if (CollectionUtils.isNotEmpty(list)) {
                    return list.stream().map(OrderPackageServiceDTO::getCategoryCode).collect(Collectors.toSet());
                }
            }
        } catch (Exception e) {
            logger.error("ToOrderPackageServiceCodesError", e);
        }
        return Collections.emptySet();
    }

    public void setIsVipUserLevel(SupplyOrder supplyOrder, OrderDetail orderDetail) {
        List<OrderExtendAttributeDTO> list = Optional.ofNullable(orderDetail).map(OrderDetail::getOrderExtendAttributeInfo).orElse(Collections.emptyList());
        String isVipUserLevel = OrderExtendAttributeUtil.getAttributeValue(list, "is_vip_user_level", "0");
        supplyOrder.setIsVipUserLevel(Integer.valueOf(isVipUserLevel));
    }

    public void setIsDowngradeCarType(SupplyOrder supplyOrder, OrderDetail orderDetail) {
        List<OrderExtendAttributeDTO> list = Optional.ofNullable(orderDetail).map(OrderDetail::getOrderExtendAttributeInfo).orElse(Collections.emptyList());
        String isVipUserLevel = OrderExtendAttributeUtil.getAttributeValue(list, "is_downgrade_car_type_order", "0");
        supplyOrder.setIsDowngradeCarType(Integer.valueOf(isVipUserLevel));
    }
}
