package com.ctrip.dcs.dsp.delay.conflict.impl

import com.ctrip.dcs.dsp.delay.conflict.CheckCode
import com.ctrip.dcs.dsp.delay.conflict.ConflictContext
import com.ctrip.dcs.dsp.delay.gateway.GeoGateway
import com.ctrip.dcs.dsp.delay.model.DelayDspOrder
import com.ctrip.dcs.dsp.delay.model.DelayDspTask
import com.ctrip.dcs.dsp.delay.model.DispatcherConfig
import com.ctrip.dcs.dsp.delay.model.Driver
import com.ctrip.dcs.dsp.delay.model.DriverAggregation
import com.ctrip.dcs.dsp.delay.model.Route
import com.ctrip.dcs.dsp.delay.model.SupplyOrder
import com.ctrip.dcs.dsp.delay.util.DateUtil
import com.google.common.collect.Lists
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 */
class HeadOrderCheckSpec extends Specification {

    def geoGateway = Mock(GeoGateway)

    def config = Mock(DispatcherConfig)

    def check = new HeadOrderCheck(geoGateway: geoGateway)

    @Unroll
    def "check"() {

        given: "Mock数据"
        def task = new DelayDspTask(taskId: 1, beginTime: DateUtil.parseDate("2022-08-29 12:00:00"))
        def next = new DelayDspOrder(sysExpectBookTime: DateUtil.parseDate("2022-08-29 12:30:00"), driverOrderFee: 100, kiloLength: 10)
        def driver = new Driver(addressLongitude: 100, addressLatitude: 31)
        def context = new ConflictContext(prev: prev, next: next, delayDspTask: task, config: config, driverAggregation: new DriverAggregation(driver: driver, orders: takenOrders, outTimeOrders: outOrders))

        geoGateway.queryRoute(_, _) >> new Route(duration: dur, distance: 10D)
        config.getHeadLimit() >> limit
        config.getOrderMileageValueRate(_, _) >> 0.1

        when: "执行校验方法"
        def res = check.check(context)

        then: "验证校验结果"
        res == code

        where:
        outOrders                                   | prev      | takenOrders                           | dur | limit || code
        null                                        | null      | null                                  | 15D | 10D   || CheckCode.OK
        null                                        | null      | null                                  | 45D | 10D   || CheckCode.HEAD_LIMIT_CONFLICT
        null                                        | null      | null                                  | 15D | -1D   || CheckCode.OK
        null                                        | newPrev() | null                                  | 15D | 10D   || CheckCode.OK
        null                                        | null      | newOrders("2022-08-29 12:10:00") | 15D | 10D   || CheckCode.OK
        null                                        | null      | newOrders("2022-08-29 13:00:00") | 15D | 10D   || CheckCode.OK
        newSupplyOrders("2022-08-29 11:00:00") | null      | newOrders("2022-08-29 13:00:00") | 15D | 10D   || CheckCode.OK
    }

    def newPrev() {
        return new DelayDspOrder(fromLongitude: 100, fromLatitude: 31, fromCoordsys: "", fromHash: "")
    }

    def newOrders(String time) {
        return Lists.newArrayList(new DelayDspOrder(sysExpectBookTime: DateUtil.parseDate(time)))
    }

    def newSupplyOrders(String time) {
        return Lists.newArrayList(new SupplyOrder(sysExpectBookTime: DateUtil.parseDate(time)))
    }
}
