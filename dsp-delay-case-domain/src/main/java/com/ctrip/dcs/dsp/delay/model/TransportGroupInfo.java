package com.ctrip.dcs.dsp.delay.model;


import lombok.Getter;
import lombok.Setter;

/**
 * 运力组实体
 * <AUTHOR>
 */
@Getter
@Setter
public class TransportGroupInfo {

    /**
     * 运力组id
     */
    private Integer transportGroupId;

    /**
     * 运力组名称
     */
    private String transportGroupName;

    /**
     * 运力组类型
     */
    private Integer transportGroupMode;

    private Integer shortTransportGroup;

    /**
     * 供应商id
     */
    private Long supplierId;

    /**
     * 可接订单距离用车时间 单位:分钟
     */
    private Integer takeOrderLimitTime;

    /**
     * 运力组进单配置
     */
    private String inOrderConfigs;

    /**
     * 调度语言 多种调度语言逗号分隔
     */
    private String dispatcherLanguage;

    /**
     * 调度电话
     */
    private String dispatcherPhone;
}
