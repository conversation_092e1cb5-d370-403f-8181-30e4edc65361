package com.ctrip.dcs.dsp.delay.conflict.impl;

import com.ctrip.dcs.dsp.delay.conflict.Check;
import com.ctrip.dcs.dsp.delay.conflict.CheckCode;
import com.ctrip.dcs.dsp.delay.conflict.ConflictContext;
import com.ctrip.dcs.dsp.delay.consts.CommonConstant;
import com.ctrip.dcs.dsp.delay.gateway.GeoGateway;
import com.ctrip.dcs.dsp.delay.model.DelayDspOrder;
import com.ctrip.dcs.dsp.delay.model.DelayDspTask;
import com.ctrip.dcs.dsp.delay.model.DispatcherConfig;
import com.ctrip.dcs.dsp.delay.model.Driver;
import com.ctrip.dcs.dsp.delay.model.Position;
import com.ctrip.dcs.dsp.delay.model.Route;
import com.ctrip.dcs.dsp.delay.model.SupplyOrder;
import com.ctrip.dcs.dsp.delay.util.DateUtil;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * 首单检查
 * <AUTHOR>
 */
@Component("headOrderCheck")
public class HeadOrderCheck implements Check {

    @Autowired
    private GeoGateway geoGateway;

    @Override
    public CheckCode check(ConflictContext context) {

        if (Objects.nonNull(context.getPrev())) {   // 单单匹配，不进行首单检查
            return CheckCode.OK;
        }

        DispatcherConfig config = context.getConfig();
        Double headLimit = config.getHeadLimit();
        if (headLimit < 0D) {   // 配置限制时长为负数，则通过检查
            return CheckCode.OK;
        }

        DelayDspOrder order = context.getNext();
        DelayDspTask task = context.getDelayDspTask();
        if (order.getSysExpectBookTime().after(DateUtil.addHours(task.getBeginTime(), CommonConstant.DRIVER_WORK_TIME_RANGE_HOUR))) {     // 订单不在首单时段内，非首单
            return CheckCode.OK;
        }

        List<SupplyOrder> supplyOrders = context.getDriverAggregation().getOutTimeOrders();
        if (CollectionUtils.isNotEmpty(supplyOrders)) {
            for (SupplyOrder outOrder : supplyOrders) {
                if (outOrder.getSysExpectBookTime().before(order.getSysExpectBookTime())) { // 司机时段外有抢到更早的订单，则不进行首单检查
                    return CheckCode.OK;
                }
            }
        }

        Driver driver = context.getDriverAggregation().getDriver();
        Route route = geoGateway.queryRoute(task.getTaskId(), new Position(driver, order));
        // 开始时间与订单用车时间的时间差
        double headDuration = DateUtil.minutesDiff(task.getBeginTime(), order.getSysExpectBookTime());
        if (route.getDuration() > headDuration + headLimit) {
            return CheckCode.HEAD_LIMIT_CONFLICT;
        }
        return CheckCode.OK;
    }
}
