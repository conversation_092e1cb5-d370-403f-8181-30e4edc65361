package com.ctrip.dcs.dsp.delay.conflict.impl

import com.ctrip.dcs.dsp.delay.conflict.CheckCode
import com.ctrip.dcs.dsp.delay.conflict.ConflictContext
import com.ctrip.dcs.dsp.delay.gateway.GeoGateway
import com.ctrip.dcs.dsp.delay.model.DelayDspOrder
import com.ctrip.dcs.dsp.delay.model.DelayDspTask
import com.ctrip.dcs.dsp.delay.model.DispatcherConfig
import com.ctrip.dcs.dsp.delay.model.Driver
import com.ctrip.dcs.dsp.delay.model.DriverAggregation
import com.ctrip.dcs.dsp.delay.model.Route
import com.ctrip.dcs.dsp.delay.util.DateUtil
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 */
class PrevMileageCheckSpec extends Specification {

    def geoGateway = Mock(GeoGateway)

    def config = Mock(DispatcherConfig)

    def check = new PrevMileageCheck(geoGateway: geoGateway)

    @Unroll
    def "check"() {

        given: "Mock数据"
        def task = new DelayDspTask(taskId: 1)
        def driver = new Driver(addressLongitude: 100, addressLatitude: 31)
        def next = new DelayDspOrder(fromLongitude: 100, fromLatitude: 31, fromCoordsys: "", fromHash: "", driverOrderFee: fee, kiloLength: kilo)
        def context = new ConflictContext(prev: prev, next: next, delayDspTask: task, config: config, driverAggregation: new DriverAggregation(driver: driver))

        geoGateway.queryRoute(_, _) >> new Route(distance: dis)
        config.getOrderMileageValueRate(_, _) >> rate

        when: "执行校验方法"
        def res = check.check(context)

        then: "验证校验结果"
        res == code

        where:
        prev      | fee  | kilo | dis | rate || code
        null      | 100D | 50D  | 50D | 1D   || CheckCode.OK
        null      | 100D | 60D  | 60D | 1D   || CheckCode.PREV_MILEAGE_CONFLICT
        newPrev() | 100D | 50D  | 50D | 1D   || CheckCode.OK
        newPrev() | 100D | 60D  | 60D | 1D   || CheckCode.PREV_MILEAGE_CONFLICT
    }

    def newPrev() {
        return new DelayDspOrder(fromLongitude: 100, fromLatitude: 31, fromCoordsys: "", fromHash: "")
    }
}
