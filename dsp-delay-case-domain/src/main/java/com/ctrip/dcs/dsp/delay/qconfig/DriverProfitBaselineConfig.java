package com.ctrip.dcs.dsp.delay.qconfig;

import com.ctrip.dcs.dsp.delay.qconfig.key.CarConfigKeyVO;
import com.ctrip.dcs.dsp.delay.qconfig.key.DriverHighProfitLineDayKeyVO;
import com.ctrip.dcs.dsp.delay.qconfig.value.DriverHighProfitLineDayValueVO;
import com.ctrip.dcs.dsp.delay.util.MetricsUtil;
import org.springframework.stereotype.Component;
import qunar.tc.qconfig.client.spring.QConfig;

import java.util.HashMap;
import java.util.Map;

@Component
public class DriverProfitBaselineConfig extends BaseCarConfig<DriverHighProfitLineDayKeyVO, DriverHighProfitLineDayValueVO> {

    @QConfig("visualConfig_driverProfitDayBaseline_0.json")
    private HashMap<String, Config<DriverHighProfitLineDayKeyVO, DriverHighProfitLineDayValueVO>> configMap;

    @Override
    Map<String, Config<DriverHighProfitLineDayKeyVO, DriverHighProfitLineDayValueVO>> getContext() {
        return configMap;
    }

    public Integer getHighProfitLineDay(Integer cityId, Integer carTypeId) {
        DriverHighProfitLineDayValueVO valueVO = get(new DriverHighProfitLineDayKeyVO(cityId, carTypeId));
        if (valueVO == null) {
            valueVO = get(new DriverHighProfitLineDayKeyVO(cityId, CarConfigKeyVO.ALL_CAR_TYPE));
        }
        if (valueVO == null) {
            valueVO = get(new DriverHighProfitLineDayKeyVO(CarConfigKeyVO.ALL_CITY_ID, CarConfigKeyVO.ALL_CAR_TYPE));
        }
        if (valueVO == null) {
            MetricsUtil.recordValue("DriverProfitBaselineConfig.getHighProfitLineDay.null", 1);
            return 0;
        }
        return valueVO.getHighProfit();
    }

    public Integer getStandardProfitLineDay(Integer cityId, Integer carTypeId) {
        DriverHighProfitLineDayValueVO valueVO = get(new DriverHighProfitLineDayKeyVO(cityId, carTypeId));
        if (valueVO == null) {
            valueVO = get(new DriverHighProfitLineDayKeyVO(cityId, CarConfigKeyVO.ALL_CAR_TYPE));
        }
        if (valueVO == null) {
            valueVO = get(new DriverHighProfitLineDayKeyVO(CarConfigKeyVO.ALL_CITY_ID, CarConfigKeyVO.ALL_CAR_TYPE));
        }
        if (valueVO == null) {
            MetricsUtil.recordValue("DriverProfitBaselineConfig.getStandardProfitLineDay.null", 1);
            return 0;
        }
        return valueVO.getStandardProfit();
    }
}
