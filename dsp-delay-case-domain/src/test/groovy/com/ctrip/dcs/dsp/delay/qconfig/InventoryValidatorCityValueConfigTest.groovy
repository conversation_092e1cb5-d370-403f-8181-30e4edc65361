package com.ctrip.dcs.dsp.delay.qconfig


import com.ctrip.dcs.dsp.delay.qconfig.key.InventoryValidatorCityKeyVO
import com.ctrip.dcs.dsp.delay.qconfig.value.CityValueVO
import com.google.common.collect.Lists
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 */
class InventoryValidatorCityValueConfigTest extends Specification {

    def map = new HashMap();

    def config = new InventoryValidatorCityValueConfig(context: map)

    @Unroll
    def "test"() {
        given: "Mock数据"
        InventoryValidatorCityKeyVO keyVO1 = new InventoryValidatorCityKeyVO()


        CityValueVO valueVO1 = new CityValueVO(1)
        CityValueVO valueVO2 = new CityValueVO(2)
        CityValueVO valueVO3 = new CityValueVO(3)
        CityValueVO valueVO4 = new CityValueVO(0)


        map.put("inventoryValidatorCityValue", new BaseCarConfig.Config(keyVO1, Lists.newArrayList(valueVO1, valueVO2, valueVO3, valueVO4)))

        when: "执行校验方法"
        def result = config.getValue(cityId)

        then: "验证校验结果"
        res == (Boolean.TRUE == result)

        where:
        cityId || res
        null   || false
        1      || true
        7      || true

    }

    @Unroll
    def "test2"() {
        given: "Mock数据"


        InventoryValidatorCityKeyVO keyVO1 = new InventoryValidatorCityKeyVO()
        map.put("inventoryValidatorCityValue", new BaseCarConfig.Config(keyVO1, Lists.newArrayList()))

        when: "执行校验方法"
        def result = config.getValue(cityId)

        then: "验证校验结果"
        res == (Boolean.TRUE == result)

        where:
        cityId || res
        1      || false
    }


    @Unroll
    def "test3"() {
        given: "Mock数据"

        CityValueVO valueVO3 = new CityValueVO(3)

        InventoryValidatorCityKeyVO keyVO1 = new InventoryValidatorCityKeyVO()
        map.put("inventoryValidatorCityValue", new BaseCarConfig.Config(keyVO1, Lists.newArrayList(valueVO3)))

        when: "执行校验方法"
        def result = config.getValue(cityId)

        then: "验证校验结果"
        res == (Boolean.TRUE == result)

        where:
        cityId || res
        1      || false
    }
}
