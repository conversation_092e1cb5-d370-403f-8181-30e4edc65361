package com.ctrip.dcs.dsp.delay.model;

import com.ctrip.dcs.dsp.delay.util.JsonUtil;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class DriverAggregation {

    private Driver driver;

    private DriverScore score;

    // 司机身上订单
    private List<DelayDspOrder> orders;
    // 司机时段外订单
    private List<SupplyOrder> outTimeOrders;

    @Override
    public String toString() {
        List<String> orderIds = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(orders)) {
            orderIds = orders.stream().map(DelayDspOrder::getOrderId).collect(Collectors.toList());
        }
        return "DriverAggregation{" +
                "driver=" + Optional.ofNullable(driver).map(Driver::getDriverId).orElse("") +
                ", score=" + Optional.ofNullable(score).map(DriverScore::getTotalScore).orElse(0.0) +
                ", orders=" + JsonUtil.toJson(orderIds) +
                '}';
    }
}
