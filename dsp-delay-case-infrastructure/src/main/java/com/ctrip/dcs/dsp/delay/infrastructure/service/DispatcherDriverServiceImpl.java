package com.ctrip.dcs.dsp.delay.infrastructure.service;

import com.ctrip.dcs.dsp.delay.infrastructure.trocks.TRocksProviderProxy;
import com.ctrip.dcs.dsp.delay.model.DelayDspTask;
import com.ctrip.dcs.dsp.delay.model.DriverAggregation;
import com.ctrip.dcs.dsp.delay.qconfig.DelayDspCommonQConfig;
import com.ctrip.dcs.dsp.delay.service.DispatcherDriverService;
import com.ctrip.dcs.dsp.delay.util.DateUtil;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.google.common.base.Splitter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Component
public class DispatcherDriverServiceImpl implements DispatcherDriverService {

    private static final Logger logger = LoggerFactory.getLogger(DispatcherDriverServiceImpl.class);

    private static final String DRIVER_MATCH_KEY = "DRIVER_MATCH_DELAY_DSP_%s";
    private static final String DRIVER_MATCH_VALUE = "%s_%s";

    @Autowired
    private TRocksProviderProxy rocksProviderProxy;

    @Autowired
    private DelayDspCommonQConfig delayDspCommonQConfig;

    @Override
    public void join(DelayDspTask task, List<DriverAggregation> drivers) {
        try {
            if (Objects.isNull(task) || CollectionUtils.isEmpty(drivers)) {
                return;
            }
            if (!isMatchCity(task.getCityId())) {
                // 不再配置中
                return;
            }
            Integer seconds = delayDspCommonQConfig.getDriverMatchMaxSeconds();
            String value = String.format(DRIVER_MATCH_VALUE, DateUtil.formatDate(task.getBeginTime(), DateUtil.DATE_FMT), DateUtil.formatDate(task.getEndTime(), DateUtil.DATE_FMT));
            for (DriverAggregation driver : drivers) {
                String key = String.format(DRIVER_MATCH_KEY, driver.getDriver().getDriverId());
                logger.info("DispatcherDriverServiceInfo", "driver join key: {} value: {} seconds: {}", key, value, seconds);
                rocksProviderProxy.setex(key, value, seconds);
            }
        } catch (Exception e) {
            logger.error("DispatcherDriverServiceError", e);
        }
    }

    @Override
    public void exit(List<DriverAggregation> drivers) {
        try {
            if (CollectionUtils.isEmpty(drivers)) {
                return;
            }
            for (DriverAggregation driver : drivers) {
                String key = String.format(DRIVER_MATCH_KEY, driver.getDriver().getDriverId());
                logger.info("DispatcherDriverServiceInfo", "driver exit key: {}", key);
                rocksProviderProxy.del(key);
            }
        } catch (Exception e) {
            logger.error("DispatcherDriverServiceError", e);
        }
    }

    @Override
    public Boolean isMatch(String driverId, String date) {
        try {
            if (StringUtils.isBlank(driverId) || StringUtils.isBlank(date)) {
                return false;
            }
            String key = String.format(DRIVER_MATCH_KEY, driverId);
            String value = rocksProviderProxy.get(key);
            logger.info("DispatcherDriverServiceInfo", "driver is match key: {} value:{}", key, value);
            if (value == null || "".equals(value)) {
                return false;
            }
            String[] split = value.split("_");
            return isBetween(date, split[0], split[1]);
        } catch (Exception e) {
            logger.error("DispatcherDriverServiceError", e);
        }
        return false;
    }

    public Boolean isBetween(String date, String beginDate, String endDate) {
        if (StringUtils.isBlank(date) || StringUtils.isBlank(beginDate) || StringUtils.isBlank(endDate)) {
            return false;
        }
        Date target = DateUtil.parseDate(date);
        Date begin = DateUtil.parseDate(beginDate);
        Date end = DateUtil.parseDate(endDate);
        return target != null && begin != null && end != null && target.getTime() >= begin.getTime() && target.getTime() <= end.getTime();
    }

    public Boolean isMatchCity(Integer cityId) {
        if (cityId == null) {
            return false;
        }
        String cityIds = delayDspCommonQConfig.getDriverMatchCheckCityIds();
        if (StringUtils.isBlank(cityIds)) {
            return false;
        }
        List<String> list = Splitter.on(",").omitEmptyStrings().trimResults().splitToList(cityIds);
        for (String city : list) {
            if ("all".equals(city) || city.equals(String.valueOf(cityId))) {
                return true;
            }
        }
        return false;
    }
}
