package com.ctrip.dcs.dsp.delay.qconfig;

import com.ctrip.dcs.dsp.delay.qconfig.key.CarConfigKeyVO;
import com.ctrip.dcs.dsp.delay.qconfig.key.OrderDisLimitKeyVO;
import com.ctrip.dcs.dsp.delay.qconfig.value.OrderDisLimitValueVO;
import com.ctrip.dcs.dsp.delay.util.JsonUtil;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import qunar.tc.qconfig.client.spring.QConfig;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Component
public class OrderDisLimitConfig extends BaseCarConfig<OrderDisLimitKeyVO, OrderDisLimitValueVO> {

    @QConfig("visualConfig_orderDistanceLimitNum_0.json")
    private HashMap<String, Config<OrderDisLimitKeyVO, OrderDisLimitValueVO>> context;

    @Override
    Map<String, Config<OrderDisLimitKeyVO, OrderDisLimitValueVO>> getContext() {
        return context;
    }
}
