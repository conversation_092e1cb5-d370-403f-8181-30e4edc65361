package com.ctrip.dcs.dsp.delay.qconfig;

import com.ctrip.dcs.dsp.delay.limit.OutPoolRateLimiter;
import org.springframework.stereotype.Component;
import qunar.tc.qconfig.client.MapConfig;

/**
 * <AUTHOR>
 */
@Component
public class OutPoolRateLimiterConfig {

    private static final MapConfig config = MapConfig.get("out_pool_rate_limiter.properties");

    static {
        OutPoolRateLimiter.createLimiter(config.asMap());
        config.addListener(OutPoolRateLimiter::createLimiter);
    }
}
