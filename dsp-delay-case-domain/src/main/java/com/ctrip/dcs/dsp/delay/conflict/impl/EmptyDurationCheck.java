package com.ctrip.dcs.dsp.delay.conflict.impl;

import com.ctrip.dcs.dsp.delay.conflict.Check;
import com.ctrip.dcs.dsp.delay.conflict.CheckCode;
import com.ctrip.dcs.dsp.delay.conflict.ConflictContext;
import com.ctrip.dcs.dsp.delay.gateway.GeoGateway;
import com.ctrip.dcs.dsp.delay.model.DelayDspOrder;
import com.ctrip.dcs.dsp.delay.model.DelayDspTask;
import com.ctrip.dcs.dsp.delay.model.DispatcherConfig;
import com.ctrip.dcs.dsp.delay.model.Position;
import com.ctrip.dcs.dsp.delay.model.Route;
import com.ctrip.dcs.dsp.delay.util.DateUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * 空驶时长是否冲突
 * <AUTHOR>
 */
@Component("emptyDurationCheck")
public class EmptyDurationCheck implements Check {

    @Autowired
    private GeoGateway geoGateway;

    @Override
    public CheckCode check(ConflictContext context) {
        DelayDspTask task = context.getDelayDspTask();
        DelayDspOrder prev = context.getPrev();
        DelayDspOrder next = context.getNext();
        DispatcherConfig config = context.getConfig();
        Route route = geoGateway.queryRoute(task.getTaskId(), new Position(prev, next));
        // 前向单完成时间+两单空驶时长+buffer早于后向单开始时间
        Date arrivalTime = DateUtil.addMinutes(
                prev.getPredictServiceStopTime(),
                (int) Math.ceil(route.getDuration() + config.getLbsBuffer(next.getSysExpectBookTime()))
        );
        boolean ok = arrivalTime.before(next.getSysExpectBookTime());
        return ok ? CheckCode.OK : CheckCode.EMPTY_DURATION_CONFLICT;
    }
}
