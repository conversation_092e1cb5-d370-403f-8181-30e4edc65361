package com.ctrip.dcs.dsp.delay.infrastructure.geteway;

import com.ctrip.dcs.dsp.delay.carconfig.CarConfig;
import com.ctrip.dcs.dsp.delay.consts.CommonConstant;
import com.ctrip.dcs.dsp.delay.gateway.CarConfigGateway;
import com.ctrip.dcs.dsp.delay.infrastructure.trocks.TRocksProviderProxy;
import com.ctrip.dcs.dsp.delay.util.JsonUtil;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import org.apache.commons.lang3.StringUtils;
import org.codehaus.jackson.type.TypeReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
public class CarConfigGatewayImpl implements CarConfigGateway {

    private static final Logger logger = LoggerFactory.getLogger(CarConfigGatewayImpl.class);

    @Autowired
    private TRocksProviderProxy tRocksProviderProxy;

    @Override
    public CarConfig query(String code, String version) {
        String key = CommonConstant.RPC_CACHE_PREFIX + CommonConstant.PLACEHOLDER + CommonConstant.APP_ID + code + CommonConstant.APP_ID + version;
        String v = tRocksProviderProxy.get(key);
        if (StringUtils.isNotBlank(v)) {
            logger.info("CarConfigGatewayImpl", "query from cache.value:{}", v);
            return JsonUtil.fromJson(v, new TypeReference<CarConfig>() {});
        }
        return new CarConfig();
    }
}
