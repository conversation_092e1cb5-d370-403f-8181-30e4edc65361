package com.ctrip.dcs.dsp.delay.infrastructure.gateway;

import com.ctrip.dcs.driver.domain.account.DriverUDL;
import com.ctrip.dcs.driver.domain.account.QueryUDLByDriverIdResponseType;
import com.ctrip.dcs.dsp.delay.enums.UdlEnum;
import com.ctrip.dcs.dsp.delay.infrastructure.dto.DriverUdlVO;
import com.ctrip.dcs.dsp.delay.infrastructure.geteway.DriverGatewayImpl;
import com.ctrip.dcs.dsp.delay.infrastructure.geteway.TransportGatewayImpl;
import com.ctrip.dcs.dsp.delay.infrastructure.http.DriverScoreServiceProxy;
import com.ctrip.dcs.dsp.delay.infrastructure.soa.DriverDomainServiceProxy;
import com.ctrip.dcs.dsp.delay.infrastructure.soa.TmsTransportServiceProxy;
import com.ctrip.dcs.dsp.delay.infrastructure.thread.EnableConfigThreadPool;
import com.ctrip.dcs.dsp.delay.infrastructure.util.TmsTransportServiceClientUtil;
import com.ctrip.dcs.dsp.delay.model.Driver;
import com.ctrip.dcs.dsp.delay.model.DriverLeave;
import com.ctrip.dcs.dsp.delay.qconfig.DelayDspCommonQConfig;
import com.ctrip.dcs.tms.transport.api.model.DriverInfo;
import com.ctrip.dcs.tms.transport.api.model.DriverInfoSOAResponseType;
import com.ctrip.dcs.tms.transport.api.model.DrvLeaveDetailSOAType;
import com.ctrip.dcs.tms.transport.api.model.QueryDrvLeaveDetailForDspSOAResponseType;
import com.ctrip.igt.ResponseResult;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.modules.junit4.PowerMockRunner;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * <AUTHOR>
 * @since 2025/6/3 19:17
 */
@RunWith(PowerMockRunner.class)
public class DriverGatewayImplTest {
    
    @InjectMocks
    private DriverGatewayImpl driverGateway;
    
    @Mock
    private TmsTransportServiceProxy tmsTransportServiceProxy;
    @Mock
    private TransportGatewayImpl transportGateway;
    @Mock
    private DriverScoreServiceProxy driverScoreServiceProxy;
    @Mock
    private ExecutorService driverThreadPool;
    @Mock
    DriverDomainServiceProxy driverDomainServiceProxy;
    @Mock
    private DelayDspCommonQConfig delayDspCommonQConfig;
    @Mock
    private TmsTransportServiceClientUtil transportServiceClientUtil;
    @Mock
    EnableConfigThreadPool enableConfigThreadPool;
    
    
    @Test
    public void testQueryByDriverId() {
        PowerMockito.when(delayDspCommonQConfig.isSelfCloudSwitch()).thenReturn(true);
        QueryUDLByDriverIdResponseType queryUDLByDriverIdResp = new QueryUDLByDriverIdResponseType();
        List<DriverUDL> driverUdLs = Lists.newArrayList();
        DriverUDL driverUDL = new DriverUDL();
        driverUDL.setDriverid("123");
        driverUDL.setUdl(UdlEnum.SGP.getValue());
        driverUDL.setUid("uid");
        driverUDL.setIsOversea(Boolean.TRUE);
        queryUDLByDriverIdResp.setDriverUdL(driverUdLs);
        ResponseResult responseResult = new ResponseResult();
        responseResult.setSuccess(Boolean.TRUE);
        queryUDLByDriverIdResp.setResponseResult(responseResult);
        PowerMockito.when(driverDomainServiceProxy.queryUDLByDriverId(Mockito.any())).thenReturn(queryUDLByDriverIdResp);
        PowerMockito.when(delayDspCommonQConfig.getQueryDriverTimeOut()).thenReturn(6000);
        PowerMockito.when(enableConfigThreadPool.get(Mockito.any())).thenReturn(Executors.newFixedThreadPool(3));
        
        DriverInfoSOAResponseType driverInfoSOAResp = new DriverInfoSOAResponseType();
        driverInfoSOAResp.setResponseResult(responseResult);
        List<DriverInfo> driverList = Lists.newArrayList();
        DriverInfo driverInfo = new DriverInfo();
        driverInfo.setDriverId(123L);
        driverList.add(driverInfo);
        driverInfoSOAResp.setDriverList(driverList);
        driverInfoSOAResp.setDriverList(driverList);
        PowerMockito.when(tmsTransportServiceProxy.queryDriver(Mockito.any())).thenReturn(driverInfoSOAResp);
        
        Driver driver= driverGateway.query("123");
        Assert.assertTrue(Objects.nonNull(driver));
    }
    
    @Test
    public void testQueryByDriverIds() {
        QueryUDLByDriverIdResponseType queryUDLByDriverIdResp = new QueryUDLByDriverIdResponseType();
        List<DriverUDL> driverUdLs = Lists.newArrayList();
        DriverUDL driverUDL = new DriverUDL();
        driverUDL.setDriverid("123");
        driverUDL.setUdl(UdlEnum.SGP.getValue());
        driverUDL.setUid("uid");
        driverUDL.setIsOversea(Boolean.TRUE);
        queryUDLByDriverIdResp.setDriverUdL(driverUdLs);
        ResponseResult responseResult = new ResponseResult();
        responseResult.setSuccess(Boolean.TRUE);
        queryUDLByDriverIdResp.setResponseResult(responseResult);
        PowerMockito.when(driverDomainServiceProxy.queryUDLByDriverId(Mockito.any())).thenReturn(queryUDLByDriverIdResp);
        PowerMockito.when(delayDspCommonQConfig.getQueryDriverTimeOut()).thenReturn(6000);
        PowerMockito.when(enableConfigThreadPool.get(Mockito.any())).thenReturn(Executors.newFixedThreadPool(3));
        
        DriverInfoSOAResponseType driverInfoSOAResp = new DriverInfoSOAResponseType();
        List<DriverInfo> driverList = Lists.newArrayList();
        DriverInfo driverInfo = new DriverInfo();
        driverInfo.setDriverId(123L);
        driverList.add(driverInfo);
        driverInfoSOAResp.setDriverList(driverList);
        PowerMockito.when(tmsTransportServiceProxy.queryDriver(Mockito.any())).thenReturn(driverInfoSOAResp);
        
        PowerMockito.when(delayDspCommonQConfig.isSelfCloudSwitch()).thenReturn(true);
        
        
        Set<String> driverIds = new HashSet<>();
        driverIds.add("123");
        List<Driver> drivers = driverGateway.query(driverIds);
        Assert.assertTrue(CollectionUtils.isNotEmpty(drivers));
    }
    
    @Test
    public void testGetDriverVOs() {
        QueryUDLByDriverIdResponseType queryUDLByDriverIdResp = new QueryUDLByDriverIdResponseType();
        List<DriverUDL> driverUdLs = Lists.newArrayList();
        DriverUDL driverUDL = new DriverUDL();
        driverUDL.setDriverid("123");
        driverUDL.setUdl(UdlEnum.SGP.getValue());
        driverUDL.setUid("uid");
        driverUDL.setIsOversea(Boolean.TRUE);
        queryUDLByDriverIdResp.setDriverUdL(driverUdLs);
        ResponseResult responseResult = new ResponseResult();
        responseResult.setSuccess(Boolean.TRUE);
        queryUDLByDriverIdResp.setResponseResult(responseResult);
        PowerMockito.when(driverDomainServiceProxy.queryUDLByDriverId(Mockito.any())).thenReturn(queryUDLByDriverIdResp);
        PowerMockito.when(delayDspCommonQConfig.getQueryDriverTimeOut()).thenReturn(6000);
        PowerMockito.when(enableConfigThreadPool.get(Mockito.any())).thenReturn(Executors.newFixedThreadPool(3));
        
        DriverInfoSOAResponseType driverInfoSOAResp = new DriverInfoSOAResponseType();
        List<DriverInfo> driverList = Lists.newArrayList();
        DriverInfo driverInfo = new DriverInfo();
        driverInfo.setDriverId(123L);
        driverList.add(driverInfo);
        driverInfoSOAResp.setDriverList(driverList);
        PowerMockito.when(tmsTransportServiceProxy.queryDriver(Mockito.any())).thenReturn(driverInfoSOAResp);
        
        
        Set<Long> driverIds =  new HashSet<>();
        driverIds.add(123L);
        List<DriverInfo> driverInfoList = driverGateway.getDriverVOs(driverIds);
        Assert.assertTrue(CollectionUtils.isNotEmpty(driverInfoList));
    }
    
    @Test
    public void testQueryLeave() {
        QueryUDLByDriverIdResponseType queryUDLByDriverIdResp = new QueryUDLByDriverIdResponseType();
        List<DriverUDL> driverUdLs = Lists.newArrayList();
        DriverUDL driverUDL = new DriverUDL();
        driverUDL.setDriverid("123");
        driverUDL.setUdl(UdlEnum.SGP.getValue());
        driverUDL.setUid("uid");
        driverUDL.setIsOversea(Boolean.TRUE);
        queryUDLByDriverIdResp.setDriverUdL(driverUdLs);
        ResponseResult responseResult = new ResponseResult();
        responseResult.setSuccess(Boolean.TRUE);
        queryUDLByDriverIdResp.setResponseResult(responseResult);
        PowerMockito.when(driverDomainServiceProxy.queryUDLByDriverId(Mockito.any())).thenReturn(queryUDLByDriverIdResp);
        PowerMockito.when(delayDspCommonQConfig.getQueryDriverTimeOut()).thenReturn(6000);
        PowerMockito.when(enableConfigThreadPool.get(Mockito.any())).thenReturn(Executors.newFixedThreadPool(3));
        PowerMockito.when(delayDspCommonQConfig.isSelfCloudSwitch()).thenReturn(true);
        
        QueryDrvLeaveDetailForDspSOAResponseType queryDrvLeaveDetailForDspSOAResp = new QueryDrvLeaveDetailForDspSOAResponseType();
        List<DrvLeaveDetailSOAType> data = Lists.newArrayList();
        DrvLeaveDetailSOAType drvLeaveDetail = new DrvLeaveDetailSOAType();
        drvLeaveDetail.setDrvId(123L);
        drvLeaveDetail.setId(11111L);
        data.add(drvLeaveDetail);
        queryDrvLeaveDetailForDspSOAResp.setData(data);
        PowerMockito.when( tmsTransportServiceProxy.queryDrvLeaveDetailForDsp(Mockito.any())).thenReturn(queryDrvLeaveDetailForDspSOAResp);
        
        List<String> driverIds = Lists.newArrayList("123");
        List<DriverLeave> leaves = driverGateway.queryLeave(driverIds);
        Assert.assertTrue(CollectionUtils.isNotEmpty(leaves));
    }
    
    
    
    @Test
    public void testGroupDrvUdl() {
        Map<Long, DriverUdlVO> map = Maps.newHashMap();
        Map<String, List<Long>> result = driverGateway.groupDrvUdl(map);
        Assert.assertTrue(result.size()==0);
        
        DriverUdlVO driverUdlVO1 = new DriverUdlVO();
        driverUdlVO1.setDriverid("123");
        driverUdlVO1.setUdl(UdlEnum.SHA.getValue());
        driverUdlVO1.setUid("uid");
        driverUdlVO1.setOversea(Boolean.FALSE);
        map.put(123L, driverUdlVO1);
        
        DriverUdlVO driverUdlVO2 = new DriverUdlVO();
        driverUdlVO2.setDriverid("456");
        driverUdlVO2.setUdl(UdlEnum.SGP.getValue());
        driverUdlVO2.setUid("uid");
        driverUdlVO2.setOversea(Boolean.TRUE);
        map.put(456L, driverUdlVO2);
        
        DriverUdlVO driverUdlVO3 = new DriverUdlVO();
        driverUdlVO3.setDriverid("789");
        driverUdlVO3.setUdl(null);
        driverUdlVO3.setUid("uid");
        driverUdlVO3.setOversea(Boolean.TRUE);
        map.put(789L, driverUdlVO3);
        
        result = driverGateway.groupDrvUdl(map);
        Assert.assertTrue(result.get(UdlEnum.SHA.getValue()).size() == 2);
        Assert.assertTrue(result.get(UdlEnum.SGP.getValue()).size() == 1);
    }
    
    @Test
    public void testGetDrvUdlMap() {
        QueryUDLByDriverIdResponseType queryUDLByDriverIdResp = new QueryUDLByDriverIdResponseType();
        List<DriverUDL> driverUdLs = Lists.newArrayList();
        DriverUDL driverUDL = new DriverUDL();
        driverUDL.setDriverid("123");
        driverUDL.setUdl(UdlEnum.SGP.getValue());
        driverUDL.setUid("uid");
        driverUDL.setIsOversea(Boolean.TRUE);
        queryUDLByDriverIdResp.setDriverUdL(driverUdLs);
        ResponseResult responseResult = new ResponseResult();
        responseResult.setSuccess(Boolean.TRUE);
        queryUDLByDriverIdResp.setResponseResult(responseResult);
        PowerMockito.when(driverDomainServiceProxy.queryUDLByDriverId(Mockito.any())).thenReturn(queryUDLByDriverIdResp);
        PowerMockito.when(delayDspCommonQConfig.getQueryDriverTimeOut()).thenReturn(6000);
        PowerMockito.when(enableConfigThreadPool.get(Mockito.any())).thenReturn(Executors.newFixedThreadPool(3));
        
        Set<Long> drvIdList = new HashSet<>();
        drvIdList.add(123L);
        Map<Long, DriverUdlVO> resultMap = driverGateway.getDrvUdlMap(drvIdList);
        Assert.assertTrue(Objects.nonNull(resultMap));
    }

    
    @Test
    public void testConvert() {
        DriverUDL driverUDL = new DriverUDL();
        driverUDL.setDriverid("123");
        driverUDL.setUdl(UdlEnum.SGP.getValue());
        driverUDL.setUid("uid");
        driverUDL.setIsOversea(Boolean.TRUE);
        DriverUdlVO driverUdlVO  = driverGateway.convert(driverUDL);
        Assert.assertTrue(Objects.equals(driverUDL.getDriverid(), driverUdlVO.getDriverid()));
        Assert.assertTrue(Objects.equals(driverUDL.getUdl(), driverUdlVO.getUdl()));
        Assert.assertTrue(Objects.equals(driverUDL.getUid(), driverUdlVO.getUid()));
        Assert.assertTrue(Objects.equals(driverUDL.isIsOversea(), driverUdlVO.getOversea()));
    }
    
    @Test
    public void testGetDrvUdl() {
        QueryUDLByDriverIdResponseType responseType = new QueryUDLByDriverIdResponseType();
        ResponseResult responseResult = new ResponseResult();
        responseType.setResponseResult(responseResult);
        PowerMockito.when(driverDomainServiceProxy.queryUDLByDriverId(Mockito.any())).thenReturn(responseType);
        List<String> drvIds = Lists.newArrayList("123","456");
        List<DriverUDL> drvUdl = driverGateway.getDrvUdl(drvIds);
        Assert.assertTrue(drvUdl.stream().anyMatch(t -> Objects.equals(t.getUdl(), UdlEnum.SHA.getValue())));
        
        
        responseResult.setSuccess(Boolean.TRUE);
        PowerMockito.when(driverDomainServiceProxy.queryUDLByDriverId(Mockito.any())).thenReturn(responseType);
        drvUdl = driverGateway.getDrvUdl(drvIds);
        Assert.assertTrue(drvUdl.stream().anyMatch(t -> Objects.equals(t.getUdl(), UdlEnum.SHA.getValue())));
        
        
        List<DriverUDL> driverUdLs = Lists.newArrayList();
        DriverUDL driverUDL = new DriverUDL();
        driverUDL.setDriverid("456");
        driverUDL.setUdl(UdlEnum.SHA.getValue());
        driverUDL.setUid(UUID.randomUUID().toString());
        driverUDL.setIsOversea(Boolean.FALSE);
        driverUdLs.add(driverUDL);
        responseType.setDriverUdL(driverUdLs);
        PowerMockito.when(driverDomainServiceProxy.queryUDLByDriverId(Mockito.any())).thenReturn(responseType);
        drvUdl = driverGateway.getDrvUdl(drvIds);
        Assert.assertTrue(drvUdl.size()==2);
        
        
        DriverUDL driverUDL2 = new DriverUDL();
        driverUDL2.setDriverid("123");
        driverUDL2.setUdl(UdlEnum.SGP.getValue());
        driverUDL2.setUid(UUID.randomUUID().toString());
        driverUDL2.setIsOversea(Boolean.TRUE);
        driverUdLs.add(driverUDL2);
        PowerMockito.when(driverDomainServiceProxy.queryUDLByDriverId(Mockito.any())).thenReturn(responseType);
        drvUdl = driverGateway.getDrvUdl(drvIds);
        Assert.assertFalse(drvUdl.stream().allMatch(t -> Objects.equals(t.getUdl(), UdlEnum.SHA.getValue())));
    }
    
    @Test
    public void testInitDriverUdlVO() {
        DriverUDL driverUDL  = driverGateway.initDriverUdlVO("213");
        Assert.assertTrue(Objects.equals(driverUDL.getUdl(), UdlEnum.SHA.getValue()));
    }
    
    @Test
    public void testGetTimeOut() {
        PowerMockito.when(delayDspCommonQConfig.getQueryDriverTimeOut()).thenReturn(200);
        Integer timeOut = driverGateway.getTimeOut();
        Assert.assertTrue(Objects.equals(timeOut, 200));
        
        PowerMockito.when(delayDspCommonQConfig.getQueryDriverTimeOut()).thenThrow(new RuntimeException(""));
        timeOut = driverGateway.getTimeOut();
        Assert.assertTrue(Objects.equals(timeOut, 500));
    }
    
}
