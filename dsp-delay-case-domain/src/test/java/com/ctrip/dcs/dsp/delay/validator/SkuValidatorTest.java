package com.ctrip.dcs.dsp.delay.validator;

import com.ctrip.dcs.dsp.delay.gateway.TransportGateway;
import com.ctrip.dcs.dsp.delay.model.SupplyOrder;
import com.ctrip.dcs.dsp.delay.model.TransportSku;
import com.ctrip.dcs.dsp.delay.validator.impl.SkuValidator;
import com.google.common.collect.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.modules.junit4.PowerMockRunner;

/**
 * <AUTHOR>
 */
@RunWith(PowerMockRunner.class)
public class SkuValidatorTest {

    @InjectMocks
    private SkuValidator skuValidator;

    @Mock
    private TransportGateway transportGateway;

    @Test
    public void testEmptySku() {
        SupplyOrder order = new SupplyOrder();
        order.setCityId(1);
        order.setCarTypeId(117);

        PowerMockito.when(transportGateway.queryTransportSku(Mockito.anyInt(), Mockito.anyInt())).thenReturn(Lists.newArrayList());
        ValidatorDTO code = skuValidator.validate(order);
        Assert.assertEquals(ValidatorCode.SKU, code.getCode());
    }

    @Test
    public void testNotContainsSku() {
        SupplyOrder order = new SupplyOrder();
        order.setCityId(1);
        order.setCarTypeId(117);
        order.setSkuId(1L);

        TransportSku sku = new TransportSku();
        sku.setSkuIds(Lists.newArrayList(2L,3L));

        PowerMockito.when(transportGateway.queryTransportSku(Mockito.anyInt(), Mockito.anyInt())).thenReturn(Lists.newArrayList(sku));
        ValidatorDTO code = skuValidator.validate(order);
        Assert.assertEquals(ValidatorCode.SKU, code.getCode());
    }

    @Test
    public void test() {
        SupplyOrder order = new SupplyOrder();
        order.setCityId(1);
        order.setCarTypeId(117);
        order.setSkuId(1L);

        TransportSku sku = new TransportSku();
        sku.setSkuIds(Lists.newArrayList(1L,2L,3L));

        PowerMockito.when(transportGateway.queryTransportSku(Mockito.anyInt(), Mockito.anyInt())).thenReturn(Lists.newArrayList(sku));
        ValidatorDTO code = skuValidator.validate(order);
        Assert.assertEquals(ValidatorCode.OK, code.getCode());
    }
}
