package com.ctrip.dcs.dsp.delay.service.impl;

import com.ctrip.dcs.dsp.delay.gateway.InventoryGateway;
import com.ctrip.dcs.dsp.delay.model.SupplyOrder;
import com.ctrip.dcs.dsp.delay.service.DriverInventoryService;
import com.ctrip.dcs.dsp.delay.util.MetricsUtil;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 */
@Component
public class DriverInventoryServiceImpl implements DriverInventoryService {

    private static final Logger logger = LoggerFactory.getLogger(DriverInventoryServiceImpl.class);


    @Autowired
    private InventoryGateway inventoryGateway;

    @Override
    public String checkInventoryConflict(List<String> driverIdList, SupplyOrder order) {
        long startTime = System.currentTimeMillis();
        try{
            String driverId = inventoryGateway.checkInventoryConflict(driverIdList, order);
            long endTime = System.currentTimeMillis();
            MetricsUtil.recordTime("insert_pool_checkInventoryConflict", endTime - startTime);
            return driverId;
        }catch (Exception ex){
            logger.error("DriverInventoryServiceImpl_checkInventoryConflict", ex);
        }
        long endTime = System.currentTimeMillis();
        MetricsUtil.recordTime("insert_pool_checkInventoryConflict", endTime - startTime);
        return null;
    }
}
