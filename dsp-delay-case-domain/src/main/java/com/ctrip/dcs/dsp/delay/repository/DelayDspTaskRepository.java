package com.ctrip.dcs.dsp.delay.repository;

import com.ctrip.dcs.dsp.delay.model.DelayDspMatchTaskRecord;
import com.ctrip.dcs.dsp.delay.model.DelayDspTask;
import com.ctrip.dcs.dsp.delay.model.DelayDspTaskRecord;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
public interface DelayDspTaskRepository {

    DelayDspTask queryByTaskId(Long taskId);

    List<DelayDspTask> queryByTaskIds(Set<Long> taskId);

    Long save(DelayDspTask task);

    List<DelayDspTask> queryByTime(Date date);

    void update(Long taskId, Integer status);

    void updateMatchInfo(Long taskId, Integer status, Integer taskVersion, Date matchTime,Integer matchStatus);

    void save(List<DelayDspTaskRecord> records);

    void saveDelayDspTaskRecordAndUpdateMatchTaskRecord(List<DelayDspMatchTaskRecord> records, Integer outReason);
    void update(DelayDspTaskRecord record);

    DelayDspTask query(DelayDspTask task);

    List<DelayDspTask> query(Integer cityId, Integer carTypeId, Date date);

    List<DelayDspTask> queryByMatchTime(Date date, Integer matchStatus, String taskType);

    void updateVersion(Long taskId, Integer status, Integer taskVersion);

    void updateOutPoolTime(Long taskId, Date outPoolTime);

    List<DelayDspTask> batchQueryForChangeOutPoolTime(String cityId, String begin, String end, Integer status, Integer matchStatus);

    void updateRecordMatchStatus(Integer isOut, Integer matchSuccess, Long recordId);

    void updateTakenCode(Integer takenCode, DelayDspTaskRecord record);

    List<DelayDspTask> queryCountByDeadTime(Date date);

    void updateTaskInfo(DelayDspTask delayDspTask);

    List<DelayDspTaskRecord> queryRecordByTaskId(Long taskId, Integer preOut);
}
