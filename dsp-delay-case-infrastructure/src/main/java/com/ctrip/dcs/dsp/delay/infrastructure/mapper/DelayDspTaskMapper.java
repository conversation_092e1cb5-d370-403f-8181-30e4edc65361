package com.ctrip.dcs.dsp.delay.infrastructure.mapper;

import com.ctrip.dcs.dsp.delay.model.DelayDspTask;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface DelayDspTaskMapper {

    Long insert(DelayDspTask task);

    DelayDspTask queryByTaskId(@Param("taskId") Long taskId);

    List<DelayDspTask> queryByTime(@Param("date") Date date);

    void update(@Param("taskId") Long taskId, @Param("taskStatus") Integer taskStatus);

    DelayDspTask query(
            @Param("cityId") Integer cityId,
            @Param("carTypeId") Integer carTypeId,
            @Param("beginTime") Date beginTime,
            @Param("endTime") Date endTime,
            @Param("executeTime") Date executeTime,
            @Param("deadline") Date deadline
    );

    List<DelayDspTask> batchQuery(
            @Param("cityId") Integer cityId,
            @Param("carTypeId") Integer carTypeId,
            @Param("date") Date date);

    List<DelayDspTask> queryByMatchTime(@Param("date") Date date, @Param("matchStatus") Integer matchStatus, @Param("taskType") String taskType);

    void updateMatchInfo(@Param("taskId") Long taskId, @Param("taskStatus") Integer taskStatus, @Param("taskVersion")Integer taskVersion,  @Param("matchTime")Date matchTime, @Param("matchStatus")Integer matchStatus);

    void updateVersion(@Param("taskId") Long taskId, @Param("taskStatus") Integer taskStatus, @Param("taskVersion")Integer taskVersion);

    void updateOutPoolTime(@Param("taskId")Long taskId, @Param("outPoolTime")Date outPoolTime);

    List<DelayDspTask> batchQueryForChangeOutPoolTime(
            @Param("cityId")String cityId,
            @Param("begin")String begin,
            @Param("end")String end,
            @Param("status")Integer status,
            @Param("matchStatus")Integer matchStatus);

    List<DelayDspTask> queryCountByDeadTime(@Param("date")Date date);

    List<Long> queryByTaskIdsAndDeadTime(@Param("taskIds") List<Long> taskIds, @Param("date")Date date);

    void updateTaskInfo(@Param("taskId")Long taskId, @Param("taskStatus")Integer taskStatus, @Param("matchStatus")Integer matchStatus, @Param("executeTime")Date executeTime, @Param("matchTime")Date matchTime);
}
