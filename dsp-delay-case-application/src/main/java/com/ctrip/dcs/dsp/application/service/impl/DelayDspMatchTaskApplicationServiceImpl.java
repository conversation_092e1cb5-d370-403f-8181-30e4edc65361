package com.ctrip.dcs.dsp.application.service.impl;

import com.ctrip.dcs.dsp.application.request.DispatchDelayDspTaskRequest;
import com.ctrip.dcs.dsp.application.service.DelayDspMatchTaskApplicationService;
import com.ctrip.dcs.dsp.delay.consts.CommonConstant;
import com.ctrip.dcs.dsp.delay.context.DelayDspContext;
import com.ctrip.dcs.dsp.delay.context.DispatcherManager;
import com.ctrip.dcs.dsp.delay.enums.DelayDspTaskStatus;
import com.ctrip.dcs.dsp.delay.enums.DelayDspTaskType;
import com.ctrip.dcs.dsp.delay.enums.OrderSource;
import com.ctrip.dcs.dsp.delay.enums.OutReasonEnum;
import com.ctrip.dcs.dsp.delay.enums.YesOrNo;
import com.ctrip.dcs.dsp.delay.factory.DelayDspContextFactory;
import com.ctrip.dcs.dsp.delay.factory.ModelFactory;
import com.ctrip.dcs.dsp.delay.gateway.SelfDriverOrderGateway;
import com.ctrip.dcs.dsp.delay.model.DelayDspMatchTaskRecord;
import com.ctrip.dcs.dsp.delay.model.DelayDspOrder;
import com.ctrip.dcs.dsp.delay.model.DelayDspTask;
import com.ctrip.dcs.dsp.delay.model.DispatchResult;
import com.ctrip.dcs.dsp.delay.model.DispatchResultDetail;
import com.ctrip.dcs.dsp.delay.model.OccupancyInventoryDTO;
import com.ctrip.dcs.dsp.delay.mq.MessageProducer;
import com.ctrip.dcs.dsp.delay.qconfig.DSPScheduleMatchValueConfig;
import com.ctrip.dcs.dsp.delay.qconfig.value.DSPScheduleMatchValueVO;
import com.ctrip.dcs.dsp.delay.repository.DelayDspMatchTaskRepository;
import com.ctrip.dcs.dsp.delay.repository.DelayDspOrderRepository;
import com.ctrip.dcs.dsp.delay.repository.DelayDspTaskRepository;
import com.ctrip.dcs.dsp.delay.service.DispatchTaskService;
import com.ctrip.dcs.dsp.delay.util.JsonUtil;
import com.ctrip.dcs.dsp.delay.util.MetricsUtil;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
public class DelayDspMatchTaskApplicationServiceImpl implements DelayDspMatchTaskApplicationService {

    private static final Logger logger = LoggerFactory.getLogger(DelayDspMatchTaskApplicationServiceImpl.class);
    private static final Integer LIMIT = 200;
    @Autowired
    private DelayDspTaskRepository delayDspTaskRepository;

    @Autowired
    private DelayDspMatchTaskRepository delayDspMatchTaskRepository;

    @Autowired
    private DelayDspOrderRepository delayDspOrderRepository;

    @Autowired
    private DelayDspContextFactory delayDspContextFactory;

    @Autowired
    private MessageProducer messageProducer;

    @Autowired
    private SelfDriverOrderGateway driverOrderGateway;
    @Autowired
    private DSPScheduleMatchValueConfig dspScheduleMatchValueConfig;

    @Override
    public void dispatch(DispatchDelayDspTaskRequest request) {
            Date nowDate = new Date();
            DelayDspTask task = delayDspTaskRepository.queryByTaskId(request.getTaskId());
            logger.info("DelayDspMatchTaskApplicationServiceImpl_task", JsonUtil.toJson(task));
            if (Objects.isNull(task)) {
                return;
            }
            if (!Objects.equals(task.getTaskStatus(), DelayDspTaskStatus.MATCH_EXECUTING.getCode())) {
                logger.info("DelayDspMatchTaskApplicationServiceImpl_taskStatus", "task is complete! task id: {}", request.getTaskId());
                return;
            }
            List<DelayDspOrder> orders = delayDspOrderRepository.queryByTaskId(task.getTaskId(), YesOrNo.YES.getCode());
            logger.info("DelayDspMatchTaskApplicationServiceImpl_orders", JsonUtil.toJson(orders));
            if (CollectionUtils.isEmpty(orders)) {
                logger.info("DelayDispatchApplicationServiceImpl_dispatch", "order is empty! task id: {}", request.getTaskId());
                delayDspTaskRepository.updateVersion(task.getTaskId(), DelayDspTaskStatus.UN_EXECUTED.getCode(), task.getTaskVersion() + 1);
                return;
            }
            long start = System.currentTimeMillis();

            DispatchResult result = new DispatchResult(orders);
        try {
            DelayDspContext delayDspContext = delayDspContextFactory.createDelayDspContextForMatch(task, orders);
            logger.info("DelayDspMatchTaskApplicationServiceImpl_delayDspContext", JsonUtil.toJson(delayDspContext));
            DispatchTaskService dispatchTaskService = DispatcherManager.get(DelayDspTaskType.of(task.getTaskType()));
            logger.info("DelayDspMatchTaskApplicationServiceImpl_dispatchTaskService", dispatchTaskService.getClass().getSimpleName());
            // 派单
            result = dispatchTaskService.dispatch(delayDspContext);
            logger.info("DelayDspMatchTaskApplicationServiceImpl_dispatch_result", JsonUtil.toJson(result));
            // 更新任务状态
            delayDspTaskRepository.update(task.getTaskId(), DelayDspTaskStatus.MATCH_EXECUTION_COMPLETE.getCode());
            // 保存派单结果
            saveDispatchResult(task, result.getDetails(), nowDate);
            Map<String, Object> map = Maps.newHashMap();
            map.put("taskId", task.getTaskId());
            map.put("taskVersion", task.getTaskVersion());
            messageProducer.sendMessage(CommonConstant.DELAY_DSP_TASK_MATCH_COMPLETE, map);
        } catch (Exception e) {
            MetricsUtil.recordValue("dispatch.match.task.error", 1);
            logger.error("DelayDspMatchTaskApplicationServiceImpl_error", e);
            delayDspTaskRepository.update(task.getTaskId(), DelayDspTaskStatus.UN_EXECUTED.getCode());
        } finally {
            long end = System.currentTimeMillis();
            MetricsUtil.recordTime("dispatch.match.total.time", end - start);
            MetricsUtil.recordValue("order.match.task.total", result.getMatchDetails().size() + result.getUnMatchDetails().size());
        }
    }


    @Override
    public void dealTaskMatchComplete(Long taskId, Integer taskVersion) {
        DelayDspTask delayDspTask = delayDspTaskRepository.queryByTaskId(taskId);
        logger.info("DelayDspMatchTaskApplicationServiceImpl_dealTaskMatchComplete_delayDspTask", JsonUtil.toJson(delayDspTask));
        if(Objects.isNull(delayDspTask) || delayDspTask.getTaskStatus().equals(DelayDspTaskStatus.EXECUTION_COMPLETE.getCode())){
            return;
        }
        List<DelayDspMatchTaskRecord> query = delayDspMatchTaskRepository.query(taskId, taskVersion);
        logger.info("DelayDspMatchTaskApplicationServiceImpl_dealTaskMatchComplete_query", JsonUtil.toJson(query));
        if(CollectionUtils.isEmpty(query)){
            delayDspTaskRepository.updateVersion(taskId, DelayDspTaskStatus.UN_EXECUTED.getCode(), delayDspTask.getTaskVersion() + 1);
            return;
        }
        //占用库存
        occupancyInventory(query, taskId);
        //达标司机提前出池,放入到匹配记录里，由定时任务执行出池接单逻辑
        reachStandardDriverOutPool(query, delayDspTask);
        //未匹配到司机的订单提前出池改派
        notMatchDriverOrderRedispatch(query, delayDspTask);
        //修改定时配置任务状态
        delayDspTaskRepository.updateVersion(taskId, DelayDspTaskStatus.UN_EXECUTED.getCode(), delayDspTask.getTaskVersion() + 1);
    }

    public void notMatchDriverOrderRedispatch(List<DelayDspMatchTaskRecord> query, DelayDspTask delayDspTask) {
        try {
            DSPScheduleMatchValueVO dspScheduleMatchValue = dspScheduleMatchValueConfig.getValue(delayDspTask.getCityId().toString(), delayDspTask.getCarTypeId());
            logger.info("DelayDspMatchTaskApplicationServiceImpl_notMatchDriverOrderRedispatch_dspScheduleMatchValue", JsonUtil.toJson(dspScheduleMatchValue));
            if(Objects.isNull(dspScheduleMatchValue)){
                return;
            }
            if(dspScheduleMatchValue.getIsOut().equals(String.valueOf(YesOrNo.NO.getCode()))){
                return;
            }
            //获取所有未匹配到司机的延后派订单
            List<DelayDspMatchTaskRecord> notMatchDriverOrders = query.stream().filter(x -> x.getMatchSuccess().equals(YesOrNo.NO.getCode())).collect(Collectors.toList());
            if(CollectionUtils.isEmpty(notMatchDriverOrders)){
                return;
            }
            List<String> collect = notMatchDriverOrders.stream().map(DelayDspMatchTaskRecord::getOrderId).collect(Collectors.toList());
            logger.info("DelayDspMatchTaskApplicationServiceImpl_notMatchDriverOrderRedispatch_orderIds", JsonUtil.toJson(collect));
            sendMessageReleaseInventory(collect);
            List<DelayDspOrder> delayDspOrders = delayDspOrderRepository.queryByOrderIdsWithTaskId(delayDspTask.getTaskId(), collect, dspScheduleMatchValue.getOutLimit());
            List<String> outOrderIds = delayDspOrders.stream().map(DelayDspOrder::getOrderId).collect(Collectors.toList());
            if(CollectionUtils.isEmpty(outOrderIds)){
                return;
            }
            List<DelayDspMatchTaskRecord> outDelayDspMatchTaskRecord = notMatchDriverOrders.stream().filter(x -> outOrderIds.contains(x.getOrderId())).collect(Collectors.toList());
            logger.info("DelayDspMatchTaskApplicationServiceImpl_notMatchDriverOrderRedispatch_outDelayDspMatchTaskRecord", JsonUtil.toJson(outDelayDspMatchTaskRecord));
            //插入到匹配记录表中
            delayDspTaskRepository.saveDelayDspTaskRecordAndUpdateMatchTaskRecord(outDelayDspMatchTaskRecord, OutReasonEnum.NOT_MATCH_DRIVER.getCode());
        }catch (Exception ex){
            logger.error("DelayDspMatchTaskApplicationServiceImpl_notMatchDriverOrderRedispatch_error", ex);
            MetricsUtil.recordValue("DelayDspMatchTaskApplicationServiceImpl.notMatchDriverOrderRedispatch.error", 1);
        }
    }

    private void sendMessageReleaseInventory(List<String> collect) {
        //发送消息释放虚拟库存
        Map<String, Object> map = Maps.newHashMap();
        map.put("dspOrderIds", String.join(",", collect));
        messageProducer.sendMessage(CommonConstant.DELAY_DSP_RELEASE_INVENTORY_FOR_INSERT_SUBJECT, map);
    }


    public void reachStandardDriverOutPool(List<DelayDspMatchTaskRecord> records, DelayDspTask delayDspTask) {
        try{
            //获取所有匹配到司机的延后派订单
            List<DelayDspMatchTaskRecord> matchDriverOrders = records.stream().filter(x -> x.getMatchSuccess().equals(YesOrNo.YES.getCode()) && x.getIsDelay().equals(YesOrNo.YES.getCode())).collect(Collectors.toList());
            logger.info("DelayDspMatchTaskApplicationServiceImpl_reachStandardDriverOutPool_matchDriverOrders", JsonUtil.toJson(matchDriverOrders));
            if(CollectionUtils.isEmpty(matchDriverOrders)){
                return;
            }
            //司机下通过延后派匹配到的单
            Map<String, List<String>> driverOrderIdsMapping = matchDriverOrders.stream().collect(Collectors.groupingBy(DelayDspMatchTaskRecord::getDriverId, Collectors.mapping(DelayDspMatchTaskRecord::getOrderId, Collectors.toList())));
            logger.info("DelayDspMatchTaskApplicationServiceImpl_reachStandardDriverOutPool_driverOrderIdsMapping", JsonUtil.toJson(driverOrderIdsMapping));
            //待预出池的司机
            List<String> outPoolDrivers = delayDspContextFactory.queryPreOutPoolDrivers(delayDspTask, driverOrderIdsMapping);
            logger.info("DelayDspMatchTaskApplicationServiceImpl_reachStandardDriverOutPool_outPoolDrivers", JsonUtil.toJson(outPoolDrivers));
            if(CollectionUtils.isEmpty(outPoolDrivers)){
                return;
            }
            List<DelayDspMatchTaskRecord> outPoolRecords = matchDriverOrders.stream().filter(x -> outPoolDrivers.contains(x.getDriverId())).collect(Collectors.toList());
            logger.info("DelayDspMatchTaskApplicationServiceImpl_reachStandardDriverOutPool_outPoolRecords", JsonUtil.toJson(outPoolRecords));
            //创建司机单
            batchCreateDriverOrder(outPoolRecords);
            outPoolRecords = outPoolRecords.stream().filter(x -> StringUtils.isNotBlank(x.getDriverOrderId())).collect(Collectors.toList());
            logger.info("DelayDspMatchTaskApplicationServiceImpl_reachStandardDriverOutPool_final_outPoolRecords", JsonUtil.toJson(outPoolRecords));
            //插入到匹配记录表中
            delayDspTaskRepository.saveDelayDspTaskRecordAndUpdateMatchTaskRecord(outPoolRecords, OutReasonEnum.DRIVE_REACH_STANDARD.getCode());
        }catch (Exception ex){
            logger.error("DelayDspMatchTaskApplicationServiceImpl_reachStandardDriverOutPool_error", ex);
            MetricsUtil.recordValue("DelayDspMatchTaskApplicationServiceImpl.reachStandardDriverOutPool.error", 1);
        }
    }
    private void batchCreateDriverOrder(List<DelayDspMatchTaskRecord> outPoolRecords) {
        for (DelayDspMatchTaskRecord record : outPoolRecords) {
            try {
                if (StringUtils.equalsIgnoreCase(record.getOrderSource(), OrderSource.QUNAR.name())) {
                    record.setDriverOrderId(record.getOrderId());
                } else {
                    // 仅未接单并且匹配到司机的延后派订单，需要创建司机单
                    String driverOrderId = driverOrderGateway.createById(record.getOrderId(), record.getDriverId());
                    record.setDriverOrderId(driverOrderId);
                }
            } catch (Exception e) {
                MetricsUtil.recordValue("createDriverOrder.error", 1);
                logger.error("DelayDspMatchTaskApplicationServiceImpl_batchCreateDriverOrder_error", e);
            }
        }
    }

    public void occupancyInventory(List<DelayDspMatchTaskRecord> query, Long taskId) {
        try {
            //获取所有匹配到司机的延后派订单
            List<DelayDspMatchTaskRecord> matchDriverOrders = query.stream().filter(x -> x.getMatchSuccess().equals(YesOrNo.YES.getCode()) && x.getIsDelay().equals(YesOrNo.YES.getCode())).collect(Collectors.toList());
            logger.info("DelayDspMatchTaskApplicationServiceImpl_occupancyInventory_matchDriverOrders", JsonUtil.toJson(matchDriverOrders));
            if(CollectionUtils.isEmpty(matchDriverOrders)){
                return;
            }
            //组装消息体，批量发送消息占用虚拟库存
            List<String> orderIds = matchDriverOrders.stream().map(DelayDspMatchTaskRecord::getOrderId).collect(Collectors.toList());
            logger.info("DelayDspMatchTaskApplicationServiceImpl_occupancyInventory_orderIds", JsonUtil.toJson(orderIds));
            List<DelayDspOrder> orders = delayDspOrderRepository.queryByTaskId(taskId, YesOrNo.YES.getCode());
            logger.info("DelayDspMatchTaskApplicationServiceImpl_occupancyInventory_orders", JsonUtil.toJson(orders));
            Map<String, DelayDspOrder> orderMap = orders.stream().collect(Collectors.toMap(DelayDspOrder::getOrderId, o -> o, (o1, o2) -> o2));
            Map<String, String> orderDriverMapping = matchDriverOrders.stream().collect(Collectors.toMap(DelayDspMatchTaskRecord::getOrderId,DelayDspMatchTaskRecord::getDriverId));
            List<OccupancyInventoryDTO> listMsg = new ArrayList<>();
            orderIds.forEach(x -> {
                OccupancyInventoryDTO occupancyInventoryDTO = new OccupancyInventoryDTO();
                occupancyInventoryDTO.setDspOrderId(x);
                DelayDspOrder delayDspOrder = orderMap.get(x);
                if(delayDspOrder == null){
                    logger.error("DelayDspMatchTaskApplicationServiceImpl_occupancyInventory_delayDspOrder_is_null", x);
                    return;
                }
                occupancyInventoryDTO.setStartTime(delayDspOrder.getSysExpectBookTime());
                occupancyInventoryDTO.setEndTime(delayDspOrder.getPredictServiceStopTime());
                String driverId = orderDriverMapping.get(x);
                if (StringUtils.isBlank(driverId)) {
                    return;
                }
                occupancyInventoryDTO.setDriverId(driverId);
                listMsg.add(occupancyInventoryDTO);
            });
            logger.info("DelayDspMatchTaskApplicationServiceImpl_occupancyInventory_listMsg", JsonUtil.toJson(listMsg));
            List<List<OccupancyInventoryDTO>> listMsgPartition = Lists.partition(listMsg, LIMIT);
            listMsgPartition.forEach(x -> {
                Map<String, Object> stringObjectMap = new HashMap<>();
                stringObjectMap.put("inventoryList", JsonUtil.toJson(x));
                logger.info("DelayDspMatchTaskApplicationServiceImpl_occupancyInventory_sendMessage", JsonUtil.toJson(stringObjectMap));
                messageProducer.sendMessage(CommonConstant.UPDATE_INVENTORY_FOR_MATCH_TASK_COMPLETE, stringObjectMap);
            });
        }catch (Exception ex){
            logger.error("DelayDspMatchTaskApplicationServiceImpl_occupancyInventory_error", ex);
            MetricsUtil.recordValue("DelayDspMatchTaskApplicationServiceImpl.occupancyInventory.error", 1);
        }
    }



    private void saveDispatchResult(DelayDspTask task, List<DispatchResultDetail> details, Date nowDate) {
        try {
            List<DelayDspMatchTaskRecord> records = ModelFactory.buildDelayDspMatchTaskRecords(task, details, nowDate);
            delayDspMatchTaskRepository.save(records);
        } catch (Exception e) {
            logger.error("DelayDspMatchTaskApplicationServiceImpl_saveDispatchResult_error", e);
            throw e;
        }
    }
}
