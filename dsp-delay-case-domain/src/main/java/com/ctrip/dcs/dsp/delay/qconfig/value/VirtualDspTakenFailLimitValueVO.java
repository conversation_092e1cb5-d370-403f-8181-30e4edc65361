package com.ctrip.dcs.dsp.delay.qconfig.value;

import com.ctrip.dcs.dsp.delay.util.DateUtil;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Getter
public class VirtualDspTakenFailLimitValueVO implements CarConfigValueVO {

    private String startTime;

    private String endTime;

    private Integer limitedValue;

    @JsonCreator
    public VirtualDspTakenFailLimitValueVO(@JsonProperty("startTime") String startTime, @JsonProperty("endTime") String endTime, @JsonProperty("limitedValue") Integer limitedValue) {
        this.startTime = startTime;
        this.endTime = endTime;
        this.limitedValue = limitedValue;
    }

    public boolean match(Date date) {
        String bookTimeStr = DateUtil.formatDate(date,DateUtil.HOUR_MIN_FMT);
        return  this.getEndTime().compareTo(this.getStartTime()) > 0 ?
                (bookTimeStr.compareTo(this.getStartTime()) >= 0 && bookTimeStr.compareTo(this.getEndTime()) <= 0) :
                (bookTimeStr.compareTo(this.getEndTime()) <= 0 || bookTimeStr.compareTo(this.getStartTime()) >= 0);
    }
}
