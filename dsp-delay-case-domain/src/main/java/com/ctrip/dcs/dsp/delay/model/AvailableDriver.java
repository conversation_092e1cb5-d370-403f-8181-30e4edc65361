package com.ctrip.dcs.dsp.delay.model;

import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Table;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class AvailableDriver {

    private List<AvailableDriverDetail> details;

    public AvailableDriver(List<AvailableDriverDetail> details) {
        this.details = details;
    }

    public Map<String, DelayDspOrder> orders() {
        return details.stream().map(AvailableDriverDetail::getOrder).collect(Collectors.toMap(DelayDspOrder::getOrderId, o -> o, (o1, o2) -> o2));
    }

    public Map<Long, Driver> drivers() {
        return details.stream().map(AvailableDriverDetail::getDriver).collect(Collectors.toMap(d -> Long.valueOf(d.getDriverId()), d -> d, (d1, d2) -> d2));
    }

    public Table<Long, String, Double> table() {
        Table<Long, String, Double> table = HashBasedTable.create();
        for (AvailableDriverDetail detail : details) {
            String driverId = detail.getDriver().getDriverId();
            table.put(Long.valueOf(driverId), detail.getOrder().getOrderId(), Double.valueOf(detail.getValue()));
        }
        return table;
    }

    public boolean isAvailable(String orderId, Long driverId) {
        return details.stream()
                .filter(d -> d.getOrder() != null)
                .filter(d -> d.getDriver() != null)
                .anyMatch(d -> Objects.equals(orderId, d.getOrder().getOrderId()) && Objects.equals(driverId.toString(), d.getDriver().getDriverId()) && StringUtils.isNotBlank(d.getValue()));
    }

}
