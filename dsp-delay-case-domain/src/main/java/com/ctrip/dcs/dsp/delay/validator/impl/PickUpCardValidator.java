package com.ctrip.dcs.dsp.delay.validator.impl;

import com.ctrip.dcs.dsp.delay.enums.XSkuCategoryCode;
import com.ctrip.dcs.dsp.delay.gateway.SelfDispatcherOrderGateway;
import com.ctrip.dcs.dsp.delay.model.SupplyOrder;
import com.ctrip.dcs.dsp.delay.validator.Validator;
import com.ctrip.dcs.dsp.delay.validator.ValidatorCode;
import com.ctrip.dcs.dsp.delay.validator.ValidatorDTO;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.google.common.collect.Sets;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Set;

/**
 * 举牌接机订单，不进入延后派
 * <AUTHOR>
 */
@Component("pickUpCardValidator")
public class PickUpCardValidator implements Validator {

    private static final Logger logger = LoggerFactory.getLogger(PickUpCardValidator.class);

    @Override
    public ValidatorDTO validate(SupplyOrder order) {
        try {
            // 查询主单的附加服务code
            Set<String> xSkuCategoryCodes = Sets.newHashSet();
            if (CollectionUtils.isNotEmpty(order.getOrderPackageServiceCodes())) {
                xSkuCategoryCodes.addAll(order.getOrderPackageServiceCodes());
            }
            return xSkuCategoryCodes.contains(XSkuCategoryCode.PICK_UP_CARD.getCode()) ? new ValidatorDTO(ValidatorCode.PICK_UP_CARD_LIMIT) : new ValidatorDTO(ValidatorCode.OK);
        } catch (Exception e) {
            logger.error("PickUpCardValidator", "PickUpCardValidator error! order: {}, error: {}", order.getOrderId(), e);
        }
        return new ValidatorDTO(ValidatorCode.PICK_UP_CARD_LIMIT);
    }
}
