package com.ctrip.dcs.dsp.delay.interfaces.executor;

import com.ctrip.dcs.dsp.application.service.DelayDspOrderApplicationService;
import com.ctrip.dcs.dsp.delay.service.interfaces.message.IncreaseVirtualDspTakenFailCountRequestType;
import com.ctrip.dcs.dsp.delay.service.interfaces.message.IncreaseVirtualDspTakenFailCountResponseType;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.annontation.ServiceLogTag;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@ServiceLogTag(tagKeys = {"orderId"})
public class IncreaseVirtualDspTakenFailCountExecutor extends AbstractRpcExecutor<IncreaseVirtualDspTakenFailCountRequestType, IncreaseVirtualDspTakenFailCountResponseType> implements Validator<IncreaseVirtualDspTakenFailCountRequestType> {

    @Autowired
    private DelayDspOrderApplicationService delayDspApplicationService;

    @Override
    public IncreaseVirtualDspTakenFailCountResponseType execute(IncreaseVirtualDspTakenFailCountRequestType request) {
        IncreaseVirtualDspTakenFailCountResponseType responseType = new IncreaseVirtualDspTakenFailCountResponseType();
        Long count = delayDspApplicationService.increaseVirtualDspTakenFailCount(request);
        responseType.setCount(count);
        return  ServiceResponseUtils.success(responseType);
    }

    @Override
    public void validate(AbstractValidator<IncreaseVirtualDspTakenFailCountRequestType> validator) {
        validator.ruleFor("orderId").notNull().notEmpty();
    }
}
