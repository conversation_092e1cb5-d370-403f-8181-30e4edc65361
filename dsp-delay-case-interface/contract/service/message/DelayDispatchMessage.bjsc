include '..\..\BaijiCommonTypes.bjsc'
include '..\..\IGTCommonTypes.bjsc'
include '..\..\BaijiMobileCommonTypes.bjsc'
include '..\dto\DelayDspOrderDTO.bjsc'

namespace java 'com.ctrip.dcs.dsp.delay.service.interfaces.message'

class InsertDelayDspOrderRequestType {
    /*ctrip请求Head*/
    BaijiMobileCommonTypes.MobileRequestHead head;
    /*igt-rest请求头*/
    IGTCommonTypes.RequestHeader requestHeader;
    /*订单id*/
    string orderId;
}

class InsertDelayDspOrderResponseType {
    /* 响应状态 */
    BaijiCommonTypes.ResponseStatusType responseStatus;
    /*响应体*/
    IGTCommonTypes.ResponseResult responseResult;
    /*订单id*/
    string orderId;
    /*任务id*/
    long taskId;
    /*是否延后派*/
    int isDelay;
    /*延后派发时间*/
    string executeTime;
    /*最晚延后派发时间*/
    string executeTimeDeadline;
    /*是否需要虚拟派*/
    int isVirtualDsp;
}

class IncreaseVirtualDspTakenFailCountRequestType {
    /*ctrip请求Head*/
    BaijiMobileCommonTypes.MobileRequestHead head;
    /*igt-rest请求头*/
    IGTCommonTypes.RequestHeader requestHeader;
    /*订单id*/
    string orderId;
}

class IncreaseVirtualDspTakenFailCountResponseType {
    /* 响应状态 */
    BaijiCommonTypes.ResponseStatusType responseStatus;
    /*响应体*/
    IGTCommonTypes.ResponseResult responseResult;
    /*虚拟派未接单订单数量*/
    long count;
}

class QueryDelayDspOrderRequestType {
    /*ctrip请求Head*/
    BaijiMobileCommonTypes.MobileRequestHead head;
    /*igt-rest请求头*/
    IGTCommonTypes.RequestHeader requestHeader;
    /*订单id*/
    string orderId;
    /*主订单id*/
    string mainOrderId;
}

class QueryDelayDspOrderResponseType {
    /* 响应状态 */
    BaijiCommonTypes.ResponseStatusType responseStatus;
    /*响应体*/
    IGTCommonTypes.ResponseResult responseResult;
    /*订单id*/
    string orderId;
    /*任务id*/
    long taskId;
    /*是否延后派*/
    int isDelay;
    /*延后派发时间*/
    string executeTime;
    /*最晚延后派发时间*/
    string executeTimeDeadline;
}

class QueryDelayDspOrderBatchRequestType {
    /*ctrip请求Head*/
    BaijiMobileCommonTypes.MobileRequestHead head;
    /*igt-rest请求头*/
    IGTCommonTypes.RequestHeader requestHeader;
    /*订单id*/
    list<string> orderIds;
}

class QueryDelayDspOrderBatchResponseType {
    /* 响应状态 */
    BaijiCommonTypes.ResponseStatusType responseStatus;
    /*响应体*/
    IGTCommonTypes.ResponseResult responseResult;

    list<DelayDspOrderDTO.DelayDspOrderDTO> data
}

class InsertDelayDspPoolRequestType {
    /*ctrip请求Head*/
    BaijiMobileCommonTypes.MobileRequestHead head;
    /*igt-rest请求头*/
    IGTCommonTypes.RequestHeader requestHeader;
    /*订单id*/
    string orderId;
    /*duid*/
    string duid;
}

class InsertDelayDspPoolResponseType {
    /* 响应状态 */
    BaijiCommonTypes.ResponseStatusType responseStatus;
    /*响应体*/
    IGTCommonTypes.ResponseResult responseResult;
    /*订单id*/
    string orderId;
    /*任务id*/
    long taskId;
    /*是否延后派*/
    int isDelay;
    /*延后派发时间*/
    string executeTime;
    /*最晚延后派发时间*/
    string executeTimeDeadline;
    /*是否需要虚拟派*/
    int isVirtualDsp;
}

class ExecuteDelayDspTaskRequestType {
    /*ctrip请求Head*/
    BaijiMobileCommonTypes.MobileRequestHead head;
    /*igt-rest请求头*/
    IGTCommonTypes.RequestHeader requestHeader;
    /*任务id*/
    long taskId;
}

class ExecuteDelayDspTaskResponseType {
    /* 响应状态 */
    BaijiCommonTypes.ResponseStatusType responseStatus;
    /*响应体*/
    IGTCommonTypes.ResponseResult responseResult;
}

/**
* 司机是否正在参与延后派匹配 请求
**/
class QueryIsDriverMatchingRequestType {
    /*ctrip请求Head*/
    BaijiMobileCommonTypes.MobileRequestHead head;
    /*igt-rest请求头*/
    IGTCommonTypes.RequestHeader requestHeader;
    /*司机id*/
    string driverId;
    /*时间*/
    string sysExpectBookTime;
}

/**
* 司机是否正在参与延后派匹配 响应
**/
class QueryIsDriverMatchingResponseType {
    /* 响应状态 */
    BaijiCommonTypes.ResponseStatusType responseStatus;
    /*响应体*/
    IGTCommonTypes.ResponseResult responseResult;
    /*司机id*/
    string driverId;
    /*是否正在匹配中*/
    int isMatching;
}


/**
* 查询延后派派发配置信息
**/
class QueryDelayDspConfigInfoRequestType {
    /*ctrip请求Head*/
    BaijiMobileCommonTypes.MobileRequestHead head;
    /*igt-rest请求头*/
    IGTCommonTypes.RequestHeader requestHeader;
    /*查询延后派配置信息*/
    list<DelayDspOrderDTO.DelayDspConfigInfo> delayDspConfigList;
}

/**
* 查询延后派派发配置信息
**/
class QueryDelayDspConfigResInfoResponseType {
    /* 响应状态 */
    BaijiCommonTypes.ResponseStatusType responseStatus;
    /*响应体*/
    IGTCommonTypes.ResponseResult responseResult;
    /*延后派配置信息返回信息*/
    list<DelayDspOrderDTO.DelayDspConfigResInfo> data;
}


/**
* 检查特殊日期
**/
class CheckSpecialDateRequestType {
    /*ctrip请求Head*/
    BaijiMobileCommonTypes.MobileRequestHead head;
    /*igt-rest请求头*/
    IGTCommonTypes.RequestHeader requestHeader;
    /*城市id*/
    int cityId;
    /*车型id*/
    int carTypeId;
    /*预估用车时间 yyyy-MM-dd HH:mm:ss*/
    string sysExpectBookTime;
}

class CheckSpecialDateResponseType {
    /* 响应状态 */
    BaijiCommonTypes.ResponseStatusType responseStatus;
    /*响应体*/
    IGTCommonTypes.ResponseResult responseResult;
    /*是否命中 true.命中特殊日子*/
    bool hit;
}


/**
* 检查特殊日期
**/
class UpdateTaskInfoRequestType {
    /*ctrip请求Head*/
    BaijiMobileCommonTypes.MobileRequestHead head;
    /*igt-rest请求头*/
    IGTCommonTypes.RequestHeader requestHeader;
    /*任务id*/
    long taskId;
    /*任务状态*/
    int taskStatus;
    /*任务匹配状态*/
    int matchStatus;
    /*任务执行时间 yyyy-MM-dd HH:mm:ss*/
    string executeTime;
    /*匹配任务执行时间 yyyy-MM-dd HH:mm:ss*/
    string matchTime;
}

class UpdateTaskInfoResponseType {
    /* 响应状态 */
    BaijiCommonTypes.ResponseStatusType responseStatus;
    /*响应体*/
    IGTCommonTypes.ResponseResult responseResult;
}


class PreOutPoolRequestType {
    /*ctrip请求Head*/
    BaijiMobileCommonTypes.MobileRequestHead head;
    /*igt-rest请求头*/
    IGTCommonTypes.RequestHeader requestHeader;
    /*用户单号，必传*/
    string userOrderId;
    /*催派来源1：用户 2、客服，必传*/
    int pressedDispatchSource;
    /*客服工号,pressedDispatchSource为2必传*/
    string customerServiceUid;
}

class PreOutPoolResponseType {
    /* 响应状态 */
    BaijiCommonTypes.ResponseStatusType responseStatus;
    /*响应体*/
    IGTCommonTypes.ResponseResult responseResult;
    /*结果*/
    int resCode
}
