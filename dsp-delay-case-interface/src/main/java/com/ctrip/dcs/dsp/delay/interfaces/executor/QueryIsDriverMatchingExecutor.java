package com.ctrip.dcs.dsp.delay.interfaces.executor;

import com.ctrip.dcs.dsp.delay.enums.YesOrNo;
import com.ctrip.dcs.dsp.delay.service.DispatcherDriverService;
import com.ctrip.dcs.dsp.delay.service.interfaces.message.QueryIsDriverMatchingRequestType;
import com.ctrip.dcs.dsp.delay.service.interfaces.message.QueryIsDriverMatchingResponseType;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.annontation.ServiceLogTag;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@ServiceLogTag(tagKeys = {"driverId"})
public class QueryIsDriverMatchingExecutor extends AbstractRpcExecutor<QueryIsDriverMatchingRequestType, QueryIsDriverMatchingResponseType> implements Validator<QueryIsDriverMatchingRequestType> {

    @Autowired
    private DispatcherDriverService dispatcherDriverService;

    @Override
    public QueryIsDriverMatchingResponseType execute(QueryIsDriverMatchingRequestType requestType) {
        Boolean isMatching = dispatcherDriverService.isMatch(requestType.getDriverId(), requestType.getSysExpectBookTime());
        return toIsDriverMatchingResponseType(requestType.getDriverId(), isMatching);
    }

    private QueryIsDriverMatchingResponseType toIsDriverMatchingResponseType(String driverId, Boolean isMatching) {
        QueryIsDriverMatchingResponseType response = new QueryIsDriverMatchingResponseType();
        response.setDriverId(driverId);
        response.setIsMatching(isMatching != null && isMatching ? YesOrNo.YES.getCode() : YesOrNo.NO.getCode());
        return ServiceResponseUtils.success(response);
    }

    @Override
    public void validate(AbstractValidator<QueryIsDriverMatchingRequestType> validator) {
        validator.ruleFor("driverId").notNull();
        validator.ruleFor("sysExpectBookTime").notNull();
    }
}
