package com.ctrip.dcs.dsp.delay.validator;

import com.ctrip.dcs.dsp.delay.enums.CoopMode;
import com.ctrip.dcs.dsp.delay.gateway.DriverGateway;
import com.ctrip.dcs.dsp.delay.model.Driver;
import com.ctrip.dcs.dsp.delay.model.SupplyOrder;
import com.ctrip.dcs.dsp.delay.validator.impl.DriverValidator;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.modules.junit4.PowerMockRunner;

/**
 * <AUTHOR>
 */
@RunWith(PowerMockRunner.class)
public class DriverValidatorTest {

    @InjectMocks
    private DriverValidator driverValidator;

    @Mock
    private DriverGateway driverGateway;

    @Test
    public void testNullDriverId() {
        SupplyOrder order = new SupplyOrder();
        Driver driver = new Driver();
        PowerMockito.when(driverGateway.query(Mockito.anyString())).thenReturn(driver);
        ValidatorDTO validate = driverValidator.validate(order);
        Assert.assertEquals(ValidatorCode.NO_DRIVER, validate.getCode());
    }

    @Test
    public void testNotFullTime() {
        SupplyOrder order = new SupplyOrder();
        order.setDriverId("123");
        Driver driver = new Driver();
        driver.setCoopMode(CoopMode.PART_TIME_ASSIGN.getCode());
        PowerMockito.when(driverGateway.query(Mockito.anyString())).thenReturn(driver);
        ValidatorDTO code = driverValidator.validate(order);
        Assert.assertEquals(ValidatorCode.NOT_FULL_TIME_DRIVER, code.getCode());
    }

    @Test
    public void test() {
        SupplyOrder order = new SupplyOrder();
        order.setDriverId("123");
        Driver driver = new Driver();
        driver.setCoopMode(CoopMode.FULL_TIME_ASSIGN.getCode());
        PowerMockito.when(driverGateway.query(Mockito.anyString())).thenReturn(driver);
        ValidatorDTO code = driverValidator.validate(order);
        Assert.assertEquals(ValidatorCode.OK, code.getCode());
    }

}
