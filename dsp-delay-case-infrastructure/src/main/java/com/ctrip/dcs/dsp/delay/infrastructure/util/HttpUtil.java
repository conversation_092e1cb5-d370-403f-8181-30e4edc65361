package com.ctrip.dcs.dsp.delay.infrastructure.util;

import com.ctrip.dcs.dsp.delay.util.JsonUtil;
import com.ctrip.dcs.dsp.delay.util.MetricsUtil;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.exception.BizException;
import com.google.common.base.Splitter;
import lombok.Getter;
import lombok.Setter;
import okhttp3.*;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.codehaus.jackson.annotate.JsonIgnore;
import org.codehaus.jackson.type.TypeReference;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
public class HttpUtil {

    private static final Logger LOGGER = LoggerFactory.getLogger(HttpUtil.class);

    public static final OkHttpClient CLIENT = new OkHttpClient.Builder()
            .connectionPool(new ConnectionPool(32, 5, TimeUnit.MINUTES))
            .connectTimeout(10, TimeUnit.SECONDS)
            .readTimeout(10, TimeUnit.SECONDS)
            .writeTimeout(10, TimeUnit.SECONDS)
            .build();

    public static final MediaType JSON_MEDIA_TYPE = MediaType.parse("application/json;charset=utf-8");

    public static final MediaType FORM_MEDIA_TYPE = MediaType.parse("application/x-www-form-urlencoded");

    public static <T> T post(HttpPostParam param) {
        long start = System.currentTimeMillis();
        T t = null;
        try {
            Request request = new Request.Builder()
                    .url(param.getUrl())
                    .post(param.buildRequestBody())
                    .build();
            Call call = CLIENT.newCall(request);
            Response response = call.execute();
            ResponseBody body = response.body();
            String s = body == null ? StringUtils.EMPTY : body.string();
            t = (T) JsonUtil.fromJson(s, param.getTypeReference());
        } catch (Exception e) {
//            MetricsUtil.recordValue("http." + param.formatUrl() + ".error", 1);
            LOGGER.error("http error!", e);
            throw new BizException("http." + param.formatUrl() + ".error", e);
        } finally {
            long end = System.currentTimeMillis();
//            MetricsUtil.recordTime("http." + param.formatUrl() + ".time", end - start);
        }
        LOGGER.info("http post info","request is {}, response is {}", JsonUtil.toJson(param), JsonUtil.toJson(t));
        return t;
    }

    @Setter
    @Getter
    public static final class HttpPostParam<T> {

        private String url;

        private MediaType mediaType;

        private Object param;

        @JsonIgnore
        private TypeReference<T> typeReference;

        public RequestBody buildRequestBody() {
            if (Objects.equals(this.getMediaType(), HttpUtil.JSON_MEDIA_TYPE)) {
                return buildJsonRequestBody();
            }
            return buildFormRequestBody();
        }

        private RequestBody buildJsonRequestBody() {
            return FormBody.create(this.mediaType, JsonUtil.toJson(this.param));
        }

        private RequestBody buildFormRequestBody() {
            try {
                FormBody.Builder builder = new FormBody.Builder();

                if (param instanceof Map) {
                    Map<Object, Object> map = (Map<Object, Object>) param;
                    for (Map.Entry<Object, Object> entry : map.entrySet()) {
                        if (Objects.isNull(entry.getKey()) || Objects.isNull(entry.getValue())) {
                            continue;
                        }
                        builder.add(entry.getKey().toString(), entry.getValue().toString());
                    }

                } else {
                    Map<String, String> map = BeanUtils.describe(param);
                    for (Map.Entry<String, String> entry : map.entrySet()) {
                        if (StringUtils.isBlank(entry.getKey()) || StringUtils.isBlank(entry.getValue())) {
                            continue;
                        }
                        builder.add(entry.getKey(), entry.getValue());
                    }
                }

                return builder.build();
            } catch (Exception e) {
                LOGGER.error("http error!", e);
                throw new BizException("http util build form request body error!", e);
            }
        }

        private HttpPostParam(Builder builder) {
            setUrl(builder.url);
            setTypeReference(builder.typeReference);
            setMediaType(builder.mediaType);
            setParam(builder.param);
        }


        public static final class Builder {
            private String url;
            private TypeReference typeReference;
            private MediaType mediaType;
            private Object param;

            public Builder() {
            }

            public Builder withUrl(String val) {
                url = val;
                return this;
            }

            public Builder withTypeReference(TypeReference val) {
                typeReference = val;
                return this;
            }

            public Builder withMediaType(MediaType val) {
                mediaType = val;
                return this;
            }

            public Builder withParam(Object val) {
                param = val;
                return this;
            }

            public HttpPostParam build() {
                return new HttpPostParam(this);
            }
        }

        public String formatUrl() {
            try {
                if (StringUtils.isBlank(url)) {
                    return StringUtils.EMPTY;
                }
                List<String> list = Splitter.on("com/").trimResults().omitEmptyStrings().splitToList(url);
                if (CollectionUtils.isEmpty(list)) {
                    return StringUtils.EMPTY;
                }
                return list.get(1).replaceAll("/", "_");
            } catch (Exception e) {
                LOGGER.warn(e);
                return StringUtils.EMPTY;
            }
        }
    }
}
