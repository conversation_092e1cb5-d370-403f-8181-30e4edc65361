package com.ctrip.dcs.dsp.delay.carconfig.value

import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 */
class DelayDspSubSkuValueSpec extends Specification {

    def value = new DelayDspSubSkuValue(8888, "8889 , 8887")

    @Unroll
    def "get out sub sku id list"() {

        when: "执行校验方法"
        def res = value.getOutSubSkuIdList()

        then: "验证校验结果"
        res.size() == 2
    }
}
