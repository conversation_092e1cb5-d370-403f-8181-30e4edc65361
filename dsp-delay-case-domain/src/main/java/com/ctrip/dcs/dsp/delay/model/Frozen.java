package com.ctrip.dcs.dsp.delay.model;

import com.ctrip.dcs.dsp.delay.enums.YesOrNo;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Objects;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class Frozen {

    private DelayDspOrder order;

    private Driver driver;

    private Integer frozen;

    public Frozen(DelayDspOrder order, Driver driver) {
        this.order = order;
        this.driver = driver;
        this.frozen = YesOrNo.NO.getCode();
    }

    public boolean isFrozen() {
        return Objects.equals(this.frozen, YesOrNo.YES.getCode());
    }
}
