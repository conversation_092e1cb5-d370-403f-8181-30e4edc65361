package com.ctrip.dcs.dsp.delay.service;

import com.ctrip.dcs.dsp.delay.model.PreOutPoolDTO;

/**
 * <AUTHOR>
 */
public interface PreOutPoolService {


    /**
     * 催派
     * @param preOutPoolDTO
     * @return
     */
    int preOutPool(PreOutPoolDTO preOutPoolDTO);

    /**
     * 提前出池改派
     *
     * @param dspOrderId
     * @param userOrderId
     * @param pressedDispatchSource
     * @param customerServiceUid
     * @return
     */
    int redispatchForPreOutPool(String dspOrderId, String userOrderId, Integer pressedDispatchSource, String customerServiceUid, String orderSource);


    void sendMessageToWorkBenchLog(String userOrderId, String contentKey);

}
