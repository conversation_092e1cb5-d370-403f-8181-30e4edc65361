package com.ctrip.dcs.dsp.delay.carconfig

import com.ctrip.dcs.dsp.delay.carconfig.key.VirtualDspTakenFailLimitKey
import com.ctrip.dcs.dsp.delay.carconfig.value.VirtualDspTakenFailLimitValue
import com.ctrip.dcs.dsp.delay.util.JsonUtil
import org.codehaus.jackson.type.TypeReference
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 */
class CarConfigSpec extends Specification {

    @Unroll
    def "values"() {

        given: "Mock数据"
        def carconfig = JsonUtil.fromJson(json1(), new TypeReference<CarConfig>() {})

        when: "执行校验方法"
        def res = carconfig.values(
                type1(),
                new VirtualDspTakenFailLimitKey(city, carType)
        )

        then: "验证校验结果"
        res.size() == size

        where:
        city            | carType || size
        "changchun"     | 117     || 1
        "shanghai_city" | 117     || 0
    }

    def json1() {
        return "{\"uniqCode\":\"car.dsp.delay_dsp_taken_fail_limit_conf\",\"configType\":\"TABLE\",\"labType\":\"FILE\",\"version\":\"2.2.15\",\"schema\":{\"selectScript\":\"function() {return true;}\",\"data\":{\"all#0\":{\"selectScript\":\"function() {return true;}\",\"rowKey\":\"all#0\",\"rowValue\":\"{\\\"values\\\":[{\\\"startTime\\\":\\\"00:00\\\",\\\"endTime\\\":\\\"23:59\\\",\\\"limitedValue\\\":3}],\\\"params\\\":{\\\"cityCode\\\":\\\"all\\\",\\\"carTypeId\\\":0}}\"},\"all#117\":{\"selectScript\":\"function() {return true;}\",\"rowKey\":\"all#117\",\"rowValue\":\"{\\\"values\\\":[{\\\"startTime\\\":\\\"00:00\\\",\\\"endTime\\\":\\\"23:59\\\",\\\"limitedValue\\\":5}],\\\"params\\\":{\\\"cityCode\\\":\\\"all\\\",\\\"carTypeId\\\":117}}\"},\"all#118\":{\"selectScript\":\"function() {return true;}\",\"rowKey\":\"all#118\",\"rowValue\":\"{\\\"values\\\":[{\\\"startTime\\\":\\\"00:00\\\",\\\"endTime\\\":\\\"23:59\\\",\\\"limitedValue\\\":5}],\\\"params\\\":{\\\"cityCode\\\":\\\"all\\\",\\\"carTypeId\\\":118}}\"},\"all#121\":{\"selectScript\":\"function() {return true;}\",\"rowKey\":\"all#121\",\"rowValue\":\"{\\\"values\\\":[{\\\"startTime\\\":\\\"00:00\\\",\\\"endTime\\\":\\\"23:59\\\",\\\"limitedValue\\\":1}],\\\"params\\\":{\\\"cityCode\\\":\\\"all\\\",\\\"carTypeId\\\":121}}\"},\"changchun#117\":{\"selectScript\":\"function() {return true;}\",\"rowKey\":\"changchun#117\",\"rowValue\":\"{\\\"values\\\":[{\\\"startTime\\\":\\\"00:00\\\",\\\"endTime\\\":\\\"23:59\\\",\\\"limitedValue\\\":1}],\\\"params\\\":{\\\"cityCode\\\":\\\"changchun\\\",\\\"carTypeId\\\":117}}\"},\"changchun#118\":{\"selectScript\":\"function() {return true;}\",\"rowKey\":\"changchun#118\",\"rowValue\":\"{\\\"values\\\":[{\\\"startTime\\\":\\\"00:00\\\",\\\"endTime\\\":\\\"23:59\\\",\\\"limitedValue\\\":1}],\\\"params\\\":{\\\"cityCode\\\":\\\"changchun\\\",\\\"carTypeId\\\":118}}\"},\"changchun#121\":{\"selectScript\":\"function() {return true;}\",\"rowKey\":\"changchun#121\",\"rowValue\":\"{\\\"values\\\":[{\\\"startTime\\\":\\\"00:00\\\",\\\"endTime\\\":\\\"23:59\\\",\\\"limitedValue\\\":0}],\\\"params\\\":{\\\"cityCode\\\":\\\"changchun\\\",\\\"carTypeId\\\":121}}\"},\"haikou#121\":{\"selectScript\":\"function() {return true;}\",\"rowKey\":\"haikou#121\",\"rowValue\":\"{\\\"values\\\":[{\\\"startTime\\\":\\\"00:00\\\",\\\"endTime\\\":\\\"23:59\\\",\\\"limitedValue\\\":0}],\\\"params\\\":{\\\"cityCode\\\":\\\"haikou\\\",\\\"carTypeId\\\":121}}\"},\"jinan#117\":{\"selectScript\":\"function() {return true;}\",\"rowKey\":\"jinan#117\",\"rowValue\":\"{\\\"values\\\":[{\\\"startTime\\\":\\\"00:00\\\",\\\"endTime\\\":\\\"23:59\\\",\\\"limitedValue\\\":1}],\\\"params\\\":{\\\"cityCode\\\":\\\"jinan\\\",\\\"carTypeId\\\":117}}\"},\"jinan#118\":{\"selectScript\":\"function() {return true;}\",\"rowKey\":\"jinan#118\",\"rowValue\":\"{\\\"values\\\":[{\\\"startTime\\\":\\\"00:00\\\",\\\"endTime\\\":\\\"23:59\\\",\\\"limitedValue\\\":1}],\\\"params\\\":{\\\"cityCode\\\":\\\"jinan\\\",\\\"carTypeId\\\":118}}\"},\"kunming#121\":{\"selectScript\":\"function() {return true;}\",\"rowKey\":\"kunming#121\",\"rowValue\":\"{\\\"values\\\":[{\\\"startTime\\\":\\\"00:00\\\",\\\"endTime\\\":\\\"23:59\\\",\\\"limitedValue\\\":1}],\\\"params\\\":{\\\"cityCode\\\":\\\"kunming\\\",\\\"carTypeId\\\":121}}\"},\"nanjing#121\":{\"selectScript\":\"function() {return true;}\",\"rowKey\":\"nanjing#121\",\"rowValue\":\"{\\\"values\\\":[{\\\"startTime\\\":\\\"00:00\\\",\\\"endTime\\\":\\\"23:59\\\",\\\"limitedValue\\\":0}],\\\"params\\\":{\\\"cityCode\\\":\\\"nanjing\\\",\\\"carTypeId\\\":121}}\"},\"shenyang#121\":{\"selectScript\":\"function() {return true;}\",\"rowKey\":\"shenyang#121\",\"rowValue\":\"{\\\"values\\\":[{\\\"startTime\\\":\\\"00:00\\\",\\\"endTime\\\":\\\"23:59\\\",\\\"limitedValue\\\":0}],\\\"params\\\":{\\\"cityCode\\\":\\\"shenyang\\\",\\\"carTypeId\\\":121}}\"},\"wuhan#121\":{\"selectScript\":\"function() {return true;}\",\"rowKey\":\"wuhan#121\",\"rowValue\":\"{\\\"values\\\":[{\\\"startTime\\\":\\\"00:00\\\",\\\"endTime\\\":\\\"23:59\\\",\\\"limitedValue\\\":0}],\\\"params\\\":{\\\"cityCode\\\":\\\"wuhan\\\",\\\"carTypeId\\\":121}}\"},\"zhuhai#120\":{\"selectScript\":\"function() {return true;}\",\"rowKey\":\"zhuhai#120\",\"rowValue\":\"{\\\"values\\\":[{\\\"startTime\\\":\\\"00:00\\\",\\\"endTime\\\":\\\"23:59\\\",\\\"limitedValue\\\":0}],\\\"params\\\":{\\\"cityCode\\\":\\\"zhuhai\\\",\\\"carTypeId\\\":120}}\"}}}}"
    }

    def type1() {
        return new TypeReference<List<VirtualDspTakenFailLimitValue>>() {}
    }

}
