package com.ctrip.dcs.dsp.delay.model;

import com.ctrip.dcs.dsp.delay.qconfig.DelayDspCommonQConfig;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.Objects;
import java.util.Optional;

/**
 * 延后派订单
 * <AUTHOR>
 */
@Getter
@Setter
public class DelayDspOrder {

    private static final double DISTANCE_CONVERSION_RATE = 0.5;

    private String orderId;
    private String mainOrderId;
    private Long taskId;
    private Integer carTypeId;
    private String cityCode;
    private Integer cityId;
    private Long skuId;
    private Long channelId;
    private Date sysExpectBookTime;
    private Date predictServiceStopTime;
    private String categoryCode;
    private Integer isOta;
    private Double kiloLength;
    private Double fromLongitude;
    private Double fromLatitude;
    private String fromCoordsys;
    private String fromHash;
    private Double toLongitude;
    private Double toLatitude;
    private String toCoordsys;
    private String toHash;
    private String driverId;
    private Double driverOrderFee;
    private Integer isCancel;
    private Integer isDelay;
    private String duid = "";
    private String orderSource;

    public boolean hasTaken() {
        return StringUtils.isNotBlank(driverId) && !"0".equals(driverId) && isDelay == 0;
    }

    /**
     * 计算订单收益
     * @param route
     * @return
     */
    public double calculateProfit(Route route) {
        Double distance = Optional.ofNullable(route).map(Route::getDistance).orElse((double) Integer.MAX_VALUE);
        return this.getDriverOrderFee() - (this.getKiloLength() + distance) * DISTANCE_CONVERSION_RATE;
    }

    public double calculateProfit(Route route, DelayDspCommonQConfig config) {
        if (config.isOrderProfitCity(this.getCityId())) {
            return this.getDriverOrderFee();
        }
        Double distance = Optional.ofNullable(route).map(Route::getDistance).orElse((double) Integer.MAX_VALUE);
        return this.getDriverOrderFee() - (this.getKiloLength() + distance) * DISTANCE_CONVERSION_RATE;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        DelayDspOrder order = (DelayDspOrder) o;
        return Objects.equals(orderId, order.orderId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(orderId);
    }

}
