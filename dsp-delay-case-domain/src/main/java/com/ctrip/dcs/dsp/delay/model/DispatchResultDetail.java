package com.ctrip.dcs.dsp.delay.model;

import com.ctrip.dcs.dsp.delay.enums.YesOrNo;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class DispatchResultDetail {

    private Driver driver;

    private DriverScore driverScore;

    private DelayDspOrder order;

    private Integer orderSeq;

    private Route empty;

    private Integer matchSuccess;

    private String driverOrderId;

    public DispatchResultDetail() {
    }

    public DispatchResultDetail(DelayDspOrder order) {
        this.order = order;
        this.matchSuccess = YesOrNo.NO.getCode();
    }

    public DispatchResultDetail(DriverAggregation driverAggregation, DelayDspOrder order, Integer orderSeq, Route empty) {
        this.driver = driverAggregation.getDriver();
        this.driverScore = driverAggregation.getScore();
        this.order = order;
        this.orderSeq = orderSeq;
        this.empty = empty;
        this.matchSuccess = YesOrNo.YES.getCode();
    }

    public DispatchResultDetail(DriverAggregation driverAggregation, DelayDspOrder order, Integer orderSeq, Route empty, String driverOrderId) {
        this.driver = driverAggregation.getDriver();
        this.driverScore = driverAggregation.getScore();
        this.order = order;
        this.orderSeq = orderSeq;
        this.empty = empty;
        this.matchSuccess = YesOrNo.YES.getCode();
        this.driverOrderId = driverOrderId;
    }

    public DispatchResultDetail(Driver driver, DelayDspOrder order) {
        this.driver = driver;
        this.order = order;
        this.matchSuccess = YesOrNo.YES.getCode();
    }

}
