package com.ctrip.dcs.dsp.delay.qconfig;

import com.ctrip.dcs.dsp.delay.qconfig.key.VirtualDspTakenFailLimitKeyVO;
import com.ctrip.dcs.dsp.delay.qconfig.value.VirtualDspTakenFailLimitValueVO;
import org.springframework.stereotype.Component;
import qunar.tc.qconfig.client.spring.QConfig;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Component
public class VirtualDspTakenFailLimitConfig extends BaseCarConfig<VirtualDspTakenFailLimitKeyVO, VirtualDspTakenFailLimitValueVO>{

    @QConfig("visualConfig_virtualDspTakenFailLimit_0.json")
    private HashMap<String, Config<VirtualDspTakenFailLimitKeyVO, VirtualDspTakenFailLimitValueVO>> context;

    @Override
    Map<String, Config<VirtualDspTakenFailLimitKeyVO, VirtualDspTakenFailLimitValueVO>> getContext() {
        return context;
    }
}
