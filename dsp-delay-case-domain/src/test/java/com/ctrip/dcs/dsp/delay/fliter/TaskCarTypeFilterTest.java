package com.ctrip.dcs.dsp.delay.fliter;

import com.ctrip.dcs.dsp.delay.filter.impl.TaskCarTypeFilter;
import com.ctrip.dcs.dsp.delay.model.DelayDspTask;
import com.ctrip.dcs.dsp.delay.model.Driver;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.powermock.modules.junit4.PowerMockRunner;

/**
 * <AUTHOR>
 */
@RunWith(PowerMockRunner.class)
public class TaskCarTypeFilterTest {

    @InjectMocks
    private TaskCarTypeFilter filter;

    @Test
    public void test() {
        Driver driver = new Driver();
        driver.setCarTypeId(117);
        DelayDspTask task = new DelayDspTask();
        task.setCarTypeId(117);
        boolean ok = this.filter.filter(driver, task);
        Assert.assertTrue(ok);
        driver.setCarTypeId(118);
        ok = this.filter.filter(driver, task);
        Assert.assertFalse(ok);
    }
}
