package com.ctrip.dcs.dsp.delay.interfaces.executor;

import com.ctrip.dcs.dsp.application.service.DelayDspOrderApplicationService;
import com.ctrip.dcs.dsp.delay.service.interfaces.message.QueryDelayDspOrderBatchRequestType;
import com.ctrip.dcs.dsp.delay.service.interfaces.message.QueryDelayDspOrderBatchResponseType;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.annontation.ServiceLogTag;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
public class QueryDelayDspOrderBatchExecutor extends AbstractRpcExecutor<QueryDelayDspOrderBatchRequestType, QueryDelayDspOrderBatchResponseType> implements Validator<QueryDelayDspOrderBatchRequestType> {

    @Autowired
    private DelayDspOrderApplicationService delayDspApplicationService;

    @Override
    public QueryDelayDspOrderBatchResponseType execute(QueryDelayDspOrderBatchRequestType request) {
        QueryDelayDspOrderBatchResponseType response = delayDspApplicationService.queryDelayDspOrderBatch(request);
        return ServiceResponseUtils.success(response);
    }

    @Override
    public void validate(AbstractValidator<QueryDelayDspOrderBatchRequestType> validator) {
        validator.ruleFor("orderIds").notNull();
    }
}
