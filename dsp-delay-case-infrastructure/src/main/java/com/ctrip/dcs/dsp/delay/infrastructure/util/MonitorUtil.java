package com.ctrip.dcs.dsp.delay.infrastructure.util;

import java.util.HashMap;
import java.util.Map;

import com.ctrip.dcs.dsp.delay.infrastructure.dto.GaoDeFutureMonitorDTO;
import com.ctrip.igt.framework.common.base.GsonUtil;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.dianping.cat.Cat;

public class MonitorUtil {

    private static final Logger logger = LoggerFactory.getLogger(MonitorUtil.class);

    private static final String SCENARIO = "dcs-map-future-log";

    public static void monitor(GaoDeFutureMonitorDTO gaoDeFutureMonitorDTO) {
        try {
            Map<String, String> map = ObjectUtil.objectToMap(gaoDeFutureMonitorDTO);
            logger.info("monitorUtil gaoDeFutureMonitorDTO:{}", GsonUtil.toJson(map));

            Map<String, String> storedTags = new HashMap<>(16);
            storedTags.put("originData", GsonUtil.toJson(gaoDeFutureMonitorDTO));
            Cat.logTags(SCENARIO, map, storedTags);
        } catch (IllegalAccessException e) {
            logger.error("monitorUtil IllegalAccessException:{}", e.getMessage());
        }
    }
}
