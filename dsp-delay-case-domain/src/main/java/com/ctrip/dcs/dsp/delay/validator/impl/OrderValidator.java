package com.ctrip.dcs.dsp.delay.validator.impl;

import com.ctrip.dcs.dsp.delay.enums.BizAreaTypeEnum;
import com.ctrip.dcs.dsp.delay.enums.CategoryCode;
import com.ctrip.dcs.dsp.delay.enums.OrderSysTypeEnum;
import com.ctrip.dcs.dsp.delay.enums.SupplyOrderStatus;
import com.ctrip.dcs.dsp.delay.enums.YesOrNo;
import com.ctrip.dcs.dsp.delay.model.DelayDspOrder;
import com.ctrip.dcs.dsp.delay.model.SupplyOrder;
import com.ctrip.dcs.dsp.delay.qconfig.DelayDspCommonQConfig;
import com.ctrip.dcs.dsp.delay.repository.DelayDspOrderRepository;
import com.ctrip.dcs.dsp.delay.validator.Validator;
import com.ctrip.dcs.dsp.delay.validator.ValidatorCode;
import com.ctrip.dcs.dsp.delay.validator.ValidatorDTO;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.google.common.collect.Sets;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.Set;

/**
 * 自营
 * 接送机站
 * <AUTHOR>
 */
@Component("orderValidator")
public class OrderValidator implements Validator {

    private static final Logger logger = LoggerFactory.getLogger(OrderValidator.class);

    @Autowired
    private DelayDspOrderRepository delayDspOrderRepository;

    @Autowired
    private DelayDspCommonQConfig delayDspCommonQConfig;

    @Override
    public ValidatorDTO validate(SupplyOrder order) {
        if (Objects.equals(order.getOrderStatus(), SupplyOrderStatus.CANCEL.getCode())) {
            // 订单状态不合法
            logger.info("OrderValidator", "order status illegal!, order id: {}", order.getOrderId());
            return new ValidatorDTO(ValidatorCode.ILLEGAL_STATUS);
        }
        if (Objects.equals(order.getIsOtaBookOrder(), YesOrNo.YES.getCode())) {
            // ota订单
            logger.info("OrderValidator", "order is ota!, order id: {}", order.getOrderId());
            return new ValidatorDTO(ValidatorCode.OTA);

        }
        if (!order.isTaken() && !CategoryCode.isJnt(order.getCategoryCode().toLowerCase())) {
            // 入池，非接送机站
            logger.info("OrderValidator", "order is not jnt!, order id: {}", order.getOrderId());
            return new ValidatorDTO(ValidatorCode.NOT_JNT);
        }
        if (order.isTaken() && !CategoryCode.isJntOrPtp(order.getCategoryCode().toLowerCase())) {
            // 接单，非接送机站或点对点
            logger.info("OrderValidator", "order is not jnt!, order id: {}", order.getOrderId());
            return new ValidatorDTO(ValidatorCode.NOT_JNT);
        }

        if (Objects.equals(order.getShortDisOrder(),YesOrNo.YES.getCode()) && !delayDspCommonQConfig.getShortDisOrderCategoryCodeSet(order.getCityId(),order.getCategoryCode())) {
            // 非接送机站
            logger.info("ShortOrderValidator", "order is not jnt!, order id: {}", order.getOrderId());
            return new ValidatorDTO(ValidatorCode.NOT_JNT);

        }
        if (BizAreaTypeEnum.isIGT(order.getBizAreaType())) {
            // 境外订单
            logger.info("OrderValidator", "order is igt!, order id: {}", order.getOrderId());
            return new ValidatorDTO(ValidatorCode.IGT);

        }
        if (!order.isTaken() && Objects.equals(OrderSysTypeEnum.T.getCode(), order.getOrderSysType())) {
            // T站订单
            logger.info("OrderValidator", "order is T!, order id: {}", order.getOrderId());
            return new ValidatorDTO(ValidatorCode.T);

        }
        if (!order.isTaken() && order.isVip()) {
            // VIP订单
            logger.info("OrderValidator", "order is vip!, order id: {}", order.getOrderId());
            return new ValidatorDTO(ValidatorCode.VIP);
        }
        if (!order.isTaken() && order.isDowngradeCarType()) {
            // 舒适下接订单
            logger.info("OrderValidator", "order is downgrade_car_type_order!, order id: {}", order.getOrderId());
            return new ValidatorDTO(ValidatorCode.DOWNGRADE_CAR_TYPE);
        }
        if (Objects.equals(order.getOrderStatus(), SupplyOrderStatus.SEND_ORDER.getCode())) {
            DelayDspOrder delayDspOrder = delayDspOrderRepository.queryByMainOrderId(order.getSourceOrderId());
            if (Objects.nonNull(delayDspOrder)
                    && !Objects.equals(order.getOrderId(), delayDspOrder.getOrderId())) {
                // 延后派订单表中，这个主单已经有其余子单了，则该订单不进入延后派
                logger.info("OrderValidator", "other order exist!, main order id: {}", order.getSourceOrderId());
                return new ValidatorDTO(ValidatorCode.MAIN_ORDER_EXIST);

            }
        }
        return new ValidatorDTO(ValidatorCode.OK);
    }
}
