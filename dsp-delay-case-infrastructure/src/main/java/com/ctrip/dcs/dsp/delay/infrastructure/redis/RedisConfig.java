package com.ctrip.dcs.dsp.delay.infrastructure.redis;

import credis.java.client.CacheProvider;
import credis.java.client.RedisCacheProvider;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@Configuration
public class RedisConfig {

    @Bean("redisCacheProvider")
    public CacheProvider cacheProvider() {
        return new RedisCacheProvider("DCSDelayDispatchtrocks");
    }
}
