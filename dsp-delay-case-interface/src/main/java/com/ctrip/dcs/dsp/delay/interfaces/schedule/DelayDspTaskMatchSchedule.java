package com.ctrip.dcs.dsp.delay.interfaces.schedule;

import com.ctrip.arch.distlock.DLock;
import com.ctrip.arch.distlock.redis.RedisDistributedLockService;
import com.ctrip.dcs.dsp.delay.consts.CommonConstant;
import com.ctrip.dcs.dsp.delay.enums.DelayDspTaskStatus;
import com.ctrip.dcs.dsp.delay.enums.DelayDspTaskType;
import com.ctrip.dcs.dsp.delay.enums.MatchTaskStatus;
import com.ctrip.dcs.dsp.delay.model.DelayDspTask;
import com.ctrip.dcs.dsp.delay.mq.MessageProducer;
import com.ctrip.dcs.dsp.delay.qconfig.DelayDspCommonQConfig;
import com.ctrip.dcs.dsp.delay.repository.DelayDspTaskRepository;
import com.ctrip.dcs.dsp.delay.service.DelayTaskService;
import com.ctrip.dcs.dsp.delay.util.DateUtil;
import com.ctrip.dcs.dsp.delay.util.JsonUtil;
import com.ctrip.dcs.dsp.delay.util.MetricsUtil;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerContext;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.exception.BizException;
import com.dianping.cat.Cat;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.NeedRetryException;
import qunar.tc.qschedule.config.QSchedule;
import qunar.tc.schedule.Parameter;

import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;

/**
 * 定时匹配任务执行
 */
@Component
public class DelayDspTaskMatchSchedule {

    private static final Logger logger = LoggerFactory.getLogger(DelayDspTaskMatchSchedule.class);

    private static final long DEFAULT_INTERVAL = 2000L;

    @Autowired
    private DelayDspTaskRepository delayDspTaskRepository;

    @Autowired
    private MessageProducer messageProducer;

    @Autowired
    private DelayTaskService delayTaskService;

    @Autowired
    private DelayDspCommonQConfig delayDspCommonQConfig;

    @Autowired
    private RedisDistributedLockService distributedLockService;


    @QSchedule("dcs.dsp.delay.task.match.run.task")
    public void execute(Parameter parameter) {
        LoggerContext.newContext().withGlobalTraceId(UUID.randomUUID().toString()).init();
        DLock lock = distributedLockService.getLock("DelayDspOutPoolTimeChangeConsumer");
        try{
            if (!lock.tryLock()) {
                logger.warn("DelayDspTaskMatchSchedule", "task is executing");
                return;
            }
            Long interval = parameter.getProperty("interval", Long.class);
            if (interval == null) {
                interval = DEFAULT_INTERVAL;
            }
            logger.info("DelayDspTaskMatchSchedule_execute_interval", JsonUtil.toJson(interval));
            List<DelayDspTask> tasks = delayDspTaskRepository.queryByMatchTime(new Date(), MatchTaskStatus.START.getCode(), "DP");
            logger.info("DelayDspTaskMatchSchedule_execute_tasks", JsonUtil.toJson(tasks));
            tasks.sort(Comparator.comparing(DelayDspTask::getMatchTime));
            long delay = 0;
            for (DelayDspTask task : tasks) {
                Integer matchTaskTimeMin = delayTaskService.getMatchTaskTime(task);
                logger.info("DelayDspTaskMatchSchedule_execute_matchTaskTimeMin", JsonUtil.toJson(matchTaskTimeMin));
                if(matchTaskTimeMin == null){
                    MetricsUtil.recordValue("DelayDspTaskMatchSchedule.getMatchTaskTime", 1L);
                    delayDspTaskRepository.updateMatchInfo(task.getTaskId(),null, null, null, MatchTaskStatus.END.getCode());
                    continue;
                }
                if (matchTaskTimeMin == 0) {
                    delayDspTaskRepository.updateMatchInfo(task.getTaskId(),null, null, null, MatchTaskStatus.END.getCode());
                    continue;
                }
                Date matchTaskTime = DateUtil.addMinutes(task.getMatchTime(), matchTaskTimeMin);
                //任务滞后，跳过当前时间之前的任务
                if(matchTaskTime.before(new Date())){
                    delayDspTaskRepository.updateMatchInfo(task.getTaskId(), null, null, matchTaskTime, MatchTaskStatus.START.getCode());
                    continue;
                }
                if(task.getTaskStatus() == DelayDspTaskStatus.UN_EXECUTED.getCode()){
                    delayDspTaskRepository.updateMatchInfo(task.getTaskId(), DelayDspTaskStatus.MATCH_EXECUTING.getCode(), task.getTaskVersion(), matchTaskTime, MatchTaskStatus.START.getCode());
                    Map<String, Object> map = Maps.newHashMap();
                    map.put("taskId", task.getTaskId());
                    map.put("cityId", task.getCityId());
                    messageProducer.sendDelayMessage(CommonConstant.DELAY_DSP_MATCH_TASK_RUN_SUBJECT, map, delay);
                    messageProducer.sendDelayMessage(CommonConstant.DELAY_DSP_MATCH_TASK_RUN_CHECK_SUBJECT, map, matchTaskTimeMin * 60 * 1000 - delayDspCommonQConfig.getCheckMatchTaskRunMillisecond());
                    delay += interval;
                }else{
                    delayDspTaskRepository.updateMatchInfo(task.getTaskId(), null, null, matchTaskTime, MatchTaskStatus.START.getCode());
                }
            }
        }catch (Exception ex){
            logger.error("DelayDspTaskMatchSchedule_execute_error", ex);
            throw new BizException("DelayDspTaskMatchSchedule_error！",ex);
        }finally {
            lock.unlock();
        }
    }
}
