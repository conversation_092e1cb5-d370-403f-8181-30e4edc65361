package com.ctrip.dcs.dsp.application.service.impl;

import com.ctrip.dcs.dsp.application.service.InsertDelayDspPoolApplicationService;
import com.ctrip.dcs.dsp.application.util.ResponseUtil;
import com.ctrip.dcs.dsp.delay.consts.CommonConstant;
import com.ctrip.dcs.dsp.delay.enums.OrderSource;
import com.ctrip.dcs.dsp.delay.enums.YesOrNo;
import com.ctrip.dcs.dsp.delay.factory.ModelFactory;
import com.ctrip.dcs.dsp.delay.gateway.SelfDispatcherOrderGateway;
import com.ctrip.dcs.dsp.delay.model.DelayDspOrder;
import com.ctrip.dcs.dsp.delay.model.DelayDspTask;
import com.ctrip.dcs.dsp.delay.model.SupplyOrder;
import com.ctrip.dcs.dsp.delay.mq.MessageProducer;
import com.ctrip.dcs.dsp.delay.qconfig.DelayDspCommonQConfig;
import com.ctrip.dcs.dsp.delay.repository.DelayDspOrderRepository;
import com.ctrip.dcs.dsp.delay.service.DelayDspOrderService;
import com.ctrip.dcs.dsp.delay.service.DelayTaskService;
import com.ctrip.dcs.dsp.delay.service.interfaces.message.InsertDelayDspPoolRequestType;
import com.ctrip.dcs.dsp.delay.service.interfaces.message.InsertDelayDspPoolResponseType;
import com.ctrip.dcs.dsp.delay.util.DateUtil;
import com.ctrip.dcs.dsp.delay.util.JsonUtil;
import com.ctrip.dcs.dsp.delay.util.MetricsUtil;
import com.ctrip.dcs.dsp.delay.validator.ValidatorChain;
import com.ctrip.dcs.dsp.delay.validator.ValidatorCode;
import com.ctrip.dcs.dsp.delay.validator.ValidatorDTO;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.jackson.JacksonSerializer;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 */
@Component
public class InsertDelayDspPoolApplicationServiceImpl implements InsertDelayDspPoolApplicationService {

    private static final Logger logger = LoggerFactory.getLogger(InsertDelayDspPoolApplicationServiceImpl.class);

    @Autowired
    private DelayDspOrderRepository delayDspOrderRepository;

    @Autowired
    private SelfDispatcherOrderGateway dispatcherOrderGateway;

    @Autowired
    private MessageProducer messageProducer;

    @Autowired
    private DelayDspCommonQConfig delayDspCommonQConfig;

    @Autowired
    private ValidatorChain validatorChain;

    @Autowired
    private DelayTaskService delayTaskService;
    
    @Resource
    DelayDspOrderService delayDspOrderService;
    
    

    @Override
    public InsertDelayDspPoolResponseType insertDelayDspPool(InsertDelayDspPoolRequestType request) {
        String driverId = null;
        ValidatorDTO code = null;
        SupplyOrder supplyOrder = null;
        long startTime = System.currentTimeMillis();

        try {
            supplyOrder = dispatcherOrderGateway.query(request.getOrderId());
            if (Objects.isNull(supplyOrder) || supplyOrder.isCancel()) {
                return ResponseUtil.buildInsertDelayDspPoolResponseType(request.getOrderId());
            }
            // 订单延后派入池校验
            code = validatorChain.validate(supplyOrder);

            if (!ValidatorCode.OK.equals(code.getCode())) {
                logger.info("InsertDelayDspPoolApplicationServiceImpl_insertDelayDspOrder", "order: {}, Validator Code: {}", supplyOrder.getOrderId(), code.getCode().name());
                MetricsUtil.recordValue("insert.delay.dsp.order.validator." + code.getCode().name(), 1);
                return ResponseUtil.buildInsertDelayDspPoolResponseType(supplyOrder.getOrderId());
            }
            DelayDspTask task = delayTaskService.queryOrCreate(supplyOrder);
            logger.info("InsertDelayDspPoolApplicationServiceImpl_task", "task: {}", JsonUtil.toJson(task));
            if (Objects.isNull(task) || !task.isUnExecuted()) {
                return ResponseUtil.buildInsertDelayDspPoolResponseType(supplyOrder.getOrderId());
            }
            if (task.getExecuteTime().after(supplyOrder.getSysExpectBookTime())) {
                MetricsUtil.recordValue("insert.delay.dsp.order.afterBookTime", 1);
                return ResponseUtil.buildInsertDelayDspPoolResponseType(supplyOrder.getOrderId());
            }
            // 构建延后派订单
            DelayDspOrder order = ModelFactory.buildDelayDspOrder(supplyOrder, task, request.getDuid(), OrderSource.CTRIP);
            if(code.getDriverAggregations() != null){
                driverId = code.getDriverAggregations().getDriver().getDriverId();
            }
            delayDspOrderRepository.save(order);
            // 发送延后派订单入池消息
            sendMessage(order, task);
            MetricsUtil.recordValue("insert.delay.dsp.order.success", 1);
            return ResponseUtil.buildInsertDelayDspPoolResponseType(
                    order.getOrderId(),
                    order.getIsDelay(),
                    task.getExecuteTime(),
                    task.getExecuteTimeDeadline(),
                    delayDspCommonQConfig.getVirtualDspSwitch()
            );
        } catch (Exception e) {
            MetricsUtil.recordValue("insert.delay.dsp.order.error", 1);
            logger.error("insertDelayDspOrder error!", e);
            if(StringUtils.isNotBlank(driverId)){
                //发送消息释放虚拟库存
                Map<String, Object> map = Maps.newHashMap();
                map.put("dspOrderId", request.getOrderId());
                map.put("driverId", driverId);
                messageProducer.sendMessage(CommonConstant.DELAY_DSP_RELEASE_INVENTORY_FOR_INSERT_SUBJECT, map);
            }
        } finally {
            if (Objects.nonNull(code)) {
                //记录延后派入池原因
                delayDspOrderService.recordInsertDspPoolReasonRecord(code, supplyOrder, startTime, System.currentTimeMillis());
            }
        }
        return ResponseUtil.buildInsertDelayDspPoolResponseType(request.getOrderId());
    }

    public long sendMessage(DelayDspOrder order, DelayDspTask task) {
        try {
            HashMap<String, Object> msg = Maps.newHashMap();
            msg.put("orderId", order.getOrderId());
            msg.put("userOrderId", order.getMainOrderId());
            msg.put("orderSource", order.getOrderSource());
            msg.put("taskId", task.getTaskId());
            msg.put("taskType", task.getTaskType());
            if (Objects.equals(order.getIsDelay(), YesOrNo.YES.getCode())) {
                msg.put("duid", order.getDuid());
            }
            // 延迟检查，提前60、30、15分钟发送最晚出池时间检查消息
            Set<Integer> delayMinutes = Sets.newHashSet(60, 30, 15);
            for (Integer delayMinute : delayMinutes) {
                sendDeadlineMessage(task, msg, delayMinute);
            }
            this.cacheEdgesForMatchTask(task, msg);
            Date now = new Date();
            Date date = DateUtil.addHours(task.getExecuteTime(), -delayDspCommonQConfig.getBeforeExecuteTimeHour());
            if (date.before(now)) {
                // 当前时间距离任务执行时间在配置阈值范围内，立即处理
                messageProducer.sendMessage(CommonConstant.DELAY_DSP_ORDER_INSERT_SUBJECT, msg);
                return 0L;
            } else {
                // 延迟处理
                long delay = date.getTime() - now.getTime();
                messageProducer.sendDelayMessage(CommonConstant.DELAY_DSP_ORDER_INSERT_SUBJECT, msg, delay);
                return delay;
            }
        } catch (Exception e) {
            logger.error("InsertDelayDspPoolApplicationServiceImpl_sendMessage", e);
            return -1L;
        }
    }

    /**
     * 最晚出池时间检查
     * @param task
     * @param msg
     */
    public void sendDeadlineMessage(DelayDspTask task, HashMap<String, Object> msg, int minutes) {
        msg.put("checkMinutes", minutes);
        // 最晚出池时间前1小时
        Date checkTime = DateUtil.addMinutes(task.getExecuteTimeDeadline(), -minutes);
        // 时间差
        long seconds = (long) DateUtil.secondsDiff(new Date(), checkTime);
        long delay = Math.max(seconds, 0L) + RandomUtils.nextLong(0, 60 * 10);
        messageProducer.sendDelayMessage(CommonConstant.DELAY_DSP_ORDER_DEADLINE_CHECK_SUBJECT, msg, delay * 1000);
        logger.info("InsertDelayDspPoolApplicationServiceImpl_sendDeadlineMessage", "msg:{}, delay:{}", JacksonSerializer.INSTANCE().serialize(msg), delay);
    }


    private void cacheEdgesForMatchTask(DelayDspTask task, HashMap<String, Object> msg) {
        try{
            Date now = new Date();
            Date date = DateUtil.addHours(task.getMatchTime(), -delayDspCommonQConfig.getBeforeExecuteTimeHour());
            if (date.before(now)) {
                // 当前时间距离任务执行时间在配置阈值范围内，立即处理
                messageProducer.sendMessage(CommonConstant.DELAY_DSP_ORDER_INSERT_SUBJECT, msg);
            } else {
                // 延迟处理
                long delay = date.getTime() - now.getTime() + new Random().nextInt(60 * 60) * 1000;
                messageProducer.sendDelayMessage(CommonConstant.DELAY_DSP_ORDER_INSERT_SUBJECT, msg, delay);
            }
        }catch (Exception ex){
            logger.error("DelayDspOrderApplicationServiceImpl_cacheEdgesForMatchTask_error", ex);
        }
    }
}
