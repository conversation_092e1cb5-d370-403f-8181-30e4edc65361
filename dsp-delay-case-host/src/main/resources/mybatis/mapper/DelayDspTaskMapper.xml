<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ctrip.dcs.dsp.delay.infrastructure.mapper.DelayDspTaskMapper">

    <resultMap id="BaseResultMap" type="com.ctrip.dcs.dsp.delay.model.DelayDspTask">
        <result column="task_id" property="taskId" />
        <result column="city_code" property="cityCode" />
        <result column="city_id" property="cityId" />
        <result column="car_type_id" property="carTypeId" />
        <result column="task_status" property="taskStatus" />
        <result column="task_seq" property="taskSeq" />
        <result column="begin_time" property="beginTime" />
        <result column="end_time" property="endTime" />
        <result column="execute_time" property="executeTime" />
        <result column="execute_time_deadline" property="executeTimeDeadline" />
        <result column="task_type" property="taskType" />
        <result column="task_version" property="taskVersion" />
        <result column="match_time" property="matchTime" />
        <result column="match_status" property="matchStatus" />
        <result column="config_begin_time" property="configBeginTime" />
        <result column="config_end_Time" property="configEndTime" />

    </resultMap>

    <sql id="Base_Column_List">
        task_id,
        city_code,
        city_id,
        car_type_id,
        task_status,
        task_seq,
        begin_time,
        end_time,
        execute_time,
        execute_time_deadline,
        task_type,
        task_version,
        match_time,
        match_status,
        config_begin_time,
        config_end_time
    </sql>

    <insert id="insert" parameterType="com.ctrip.dcs.dsp.delay.model.DelayDspTask" useGeneratedKeys="true" keyProperty="taskId">
        INSERT INTO delay_dsp_task (
            city_code,
            city_id,
            car_type_id,
            task_status,
            task_seq,
            begin_time,
            end_time,
            execute_time,
            execute_time_deadline,
            task_type,
            task_version,
            match_time,
            match_status,
            config_begin_time,
            config_end_time,
            datachange_lasttime,
            datachange_createtime
        )
        VALUES (
            #{cityCode},
            #{cityId},
            #{carTypeId},
            #{taskStatus},
            #{taskSeq},
            #{beginTime},
            #{endTime},
            #{executeTime},
            #{executeTimeDeadline},
            #{taskType},
            #{taskVersion},
            #{matchTime},
            #{matchStatus},
            #{configBeginTime},
            #{configEndTime},
            NOW(),
            NOW()
        )
    </insert>

    <update id="update">
        UPDATE delay_dsp_task
        <set>
            task_status = #{taskStatus}
        </set>
        WHERE task_id = #{taskId}
    </update>

    <update id="updateMatchInfo">
        UPDATE delay_dsp_task
        <set>
            <if test="taskVersion != null">
                task_version = #{taskVersion},
            </if>
            <if test="matchTime != null">
                match_time = #{matchTime},
            </if>
            <if test="taskStatus != null">
                task_status = #{taskStatus},
            </if>
            match_status = #{matchStatus}
        </set>
        WHERE task_id = #{taskId}
    </update>


    <update id="updateTaskInfo">
        UPDATE delay_dsp_task
        <set>
            <if test="taskStatus != null">
                task_status = #{taskStatus},
            </if>
            <if test="matchStatus != null">
                match_status = #{matchStatus},
            </if>
            <if test="executeTime != null">
                execute_time = #{executeTime},
            </if>
            <if test="matchTime != null">
                match_time = #{matchTime},
            </if>
        </set>
        WHERE task_id = #{taskId}
    </update>




    <update id="updateVersion">
        UPDATE delay_dsp_task
        <set>
            task_version = #{taskVersion},
            task_status = #{taskStatus}
        </set>
        WHERE task_id = #{taskId}
    </update>


    <update id="updateOutPoolTime">
        UPDATE delay_dsp_task
        <set>
            execute_time = #{outPoolTime}
        </set>
        WHERE task_id = #{taskId}
    </update>


    <select id="query" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM delay_dsp_task
        WHERE
        city_id = #{cityId}
        AND car_type_id = #{carTypeId}
        AND begin_time = #{beginTime}
        AND end_time = #{endTime}
        AND execute_time = #{executeTime}
        AND execute_time_deadline = #{deadline}
        limit 1
    </select>

    <select id="queryByTime" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM delay_dsp_task
        WHERE
        task_status = 0
        and match_status = 0
        AND <![CDATA[execute_time <= #{date}]]>
    </select>

    <select id="queryByMatchTime" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM delay_dsp_task
        WHERE
         match_status = #{matchStatus}
        AND
        <![CDATA[match_time < #{date}]]>
        <if test="taskType != null">
            AND  task_type = #{taskType}
        </if>
    </select>


    <select id="queryByTaskId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM delay_dsp_task
        WHERE
        task_id = #{taskId}
    </select>

    <select id="batchQuery" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM delay_dsp_task
        WHERE
        city_id = #{cityId}
        AND car_type_id = #{carTypeId}
        AND <![CDATA[begin_time <= #{date}]]>
        AND <![CDATA[end_time > #{date}]]>
    </select>


    <select id="batchQueryForChangeOutPoolTime" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM delay_dsp_task
        WHERE
        city_id = #{cityId}
        AND  match_status = #{matchStatus}
        AND config_begin_time = #{begin}
        AND config_end_time = #{end}
        <if test="status != null">
            AND  task_status = #{status}
        </if>
    </select>

    <select id="queryCountByDeadTime" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM delay_dsp_task
        where  task_status in (0,3,4)  and <![CDATA[execute_time_deadline <= #{date}]]>
    </select>


    <select id="queryByTaskIdsAndDeadTime" resultType="Long">
        SELECT distinct(task_id) FROM delay_dsp_task
        where
          task_id in
        <foreach collection="taskIds" item="taskId" open="(" separator="," close=")">
            #{taskId}
        </foreach>
        and
        <![CDATA[execute_time_deadline <= #{date}]]>

    </select>

</mapper>