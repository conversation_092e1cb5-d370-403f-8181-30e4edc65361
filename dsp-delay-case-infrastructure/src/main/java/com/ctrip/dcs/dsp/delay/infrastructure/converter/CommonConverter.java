package com.ctrip.dcs.dsp.delay.infrastructure.converter;

import com.ctrip.dcs.dsp.delay.model.ChannelNumber;
import com.ctrip.dcs.dsp.delay.model.Driver;
import com.ctrip.dcs.dsp.delay.model.DriverLeave;
import com.ctrip.dcs.dsp.delay.model.TransportSku;
import com.ctrip.dcs.shopping.common.interfaces.dto.ChannelNumberDTO;
import com.ctrip.dcs.tms.transport.api.model.DriverInfo;
import com.ctrip.dcs.tms.transport.api.model.DrvLeaveDetailSOAType;
import com.ctrip.dcs.tms.transport.api.model.QueryApplyTransGroupsSkuForDspInfo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface CommonConverter {

    CommonConverter INSTANCE = Mappers.getMapper(CommonConverter.class);

    ChannelNumber toChannelNumber(ChannelNumberDTO dto);

    TransportSku toTransportSku(QueryApplyTransGroupsSkuForDspInfo dto);

    List<TransportSku> toTransportSku(List<QueryApplyTransGroupsSkuForDspInfo> list);

    DriverLeave toDriverLeave(DrvLeaveDetailSOAType dto);

    List<DriverLeave> toDriverLeave(List<DrvLeaveDetailSOAType> list);

    @Mapping(source = "qunarCityCode", target = "cityCode")
    Driver toDriver(DriverInfo dto);

    List<Driver> toDriver(List<DriverInfo> list);

}
