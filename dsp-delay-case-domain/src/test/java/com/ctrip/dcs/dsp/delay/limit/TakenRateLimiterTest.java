package com.ctrip.dcs.dsp.delay.limit;

import com.ctrip.dcs.dsp.delay.qconfig.DelayDspCommonQConfig;
import com.ctrip.dcs.dsp.delay.util.MetricsUtil;
import com.ctrip.igt.framework.common.concurrent.threadpool.ThreadPoolBuilder;
import com.ctrip.igt.framework.common.metrics.MetricsFactory;
import io.dropwizard.metrics5.MetricRegistry;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;

/**
 * <AUTHOR>
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest(value = {MetricsUtil.class, MetricsFactory.class})
public class TakenRateLimiterTest {

    @InjectMocks
    private TakenRateLimiter limiter;

    @Mock
    private DelayDspCommonQConfig delayDspCommonQConfig;

    @Mock
    private MetricRegistry metricRegistry;

    @Test
    public void test() throws ExecutionException, InterruptedException {
        PowerMockito.mockStatic(MetricsFactory.class );
        PowerMockito.when(MetricsFactory.getMetricRegistry()).thenReturn(metricRegistry);
        PowerMockito.mockStatic(MetricsUtil.class );
        PowerMockito.when(delayDspCommonQConfig.getTakenPermitsPerSecond()).thenReturn(1);
        limiter.init();
        ExecutorService threadPool = Executors.newSingleThreadExecutor();
        double time = 0.0;
        for (int i = 0; i < 10; i++) {
            Future<Double> future = threadPool.submit(() -> limiter.acquire());
            time += future.get();
        }
        Assert.assertTrue(time > 0);
    }
}
