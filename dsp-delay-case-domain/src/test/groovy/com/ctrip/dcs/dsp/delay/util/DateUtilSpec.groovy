package com.ctrip.dcs.dsp.delay.util

import com.ctrip.dcs.dsp.delay.model.Route
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 */
class DateUtilSpec extends Specification {

    @Unroll
    def "not over lap"() {
        given: "Mock数据"


        when: "执行校验方法"
        def res = DateUtil.notOverlap(sourceBegin, sourceEnd, targetBegin, targetEnd)

        then: "验证校验结果"
        res == result

        where:
        sourceBegin                        | sourceEnd                          | targetBegin                        | targetEnd                          || result
        format("2022-05-01 12:00:00") | format("2022-05-01 13:00:00") | format("2022-05-01 12:00:00") | format("2022-05-01 13:00:00") || false
        format("2022-05-01 12:00:00") | format("2022-05-01 13:00:00") | format("2022-05-01 14:00:00") | format("2022-05-01 15:00:00") || true
        format("2022-05-01 12:00:00") | format("2022-05-01 13:00:00") | format("2022-05-01 12:30:00") | format("2022-05-01 12:40:00") || false
        format("2022-05-01 12:00:00") | format("2022-05-01 13:00:00") | format("2022-05-01 12:30:00") | format("2022-05-01 13:40:00") || false
        format("2022-05-01 12:00:00") | format("2022-05-01 13:00:00") | format("2022-05-01 11:30:00") | format("2022-05-01 12:40:00") || false
        format("2022-05-01 12:00:00") | format("2022-05-01 13:00:00") | format("2022-05-01 11:30:00") | format("2022-05-01 11:40:00") || true
        format("2022-05-01 12:00:00") | format("2022-05-01 13:00:00") | format("2022-05-01 13:10:00") | format("2022-05-01 13:40:00") || false
        format("2022-05-01 12:00:00") | format("2022-05-01 13:00:00") | format("2022-05-01 11:10:00") | format("2022-05-01 11:50:00") || true
        format("2022-05-01 22:00:00") | format("2022-05-02 02:00:00") | format("2022-05-01 21:10:00") | format("2022-05-02 03:50:00") || false
    }

    def format(String date) {
        return DateUtil.parseDate(date)
    }
}
