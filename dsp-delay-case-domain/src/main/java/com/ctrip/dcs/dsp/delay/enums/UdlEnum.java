package com.ctrip.dcs.dsp.delay.enums;

/**
 * <AUTHOR>
 * @since 2025/4/8 16:24
 */
public enum UdlEnum {
    SHA("CN_CSPD"),
    SGP("US_SPD");
    
    private String value;
    
    UdlEnum(String value) {
        this.value = value;
    }
    
    
    public String getValue() {
        return value;
    }
    
    public static boolean isSgp(String value) {
        return UdlEnum.SGP.getValue().equalsIgnoreCase(value);
    }
}
