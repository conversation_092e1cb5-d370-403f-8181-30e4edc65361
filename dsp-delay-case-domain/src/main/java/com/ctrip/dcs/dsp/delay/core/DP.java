package com.ctrip.dcs.dsp.delay.core;

import com.ctrip.dcs.dsp.delay.util.MetricsUtil;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;

import java.util.*;

/**
 * <AUTHOR>
 */
public class DP {

    private static final Logger logger = LoggerFactory.getLogger(DP.class);

    private int n;

    private int line;

    private int[][] matrix;

    private double[] profit;

    private double[] kilo;

    private boolean[] taken;

    private boolean[] ignore;

    private boolean[][] conflict;

    private double[][] distance;

    private double[] dp;

    private ProfitFunction profitFunction;

    public DP(int n, int line, ProfitFunction profitFunction) {
        this.n = n;
        this.line = line;
        this.matrix = new int[n][n];
        this.profit = new double[n];
        this.kilo = new double[n];
        this.taken = new boolean[n];
        this.ignore = new boolean[n];
        this.conflict = new boolean[n + 1][n + 1];
        this.distance = new double[n + 1][n + 1];
        this.profitFunction = profitFunction;
    }

    public int[][] execute() {
        // 服务完对应订单所能产生的最大收益
        dp = new double[n + 1];
        for (int i = 1; i <= n; i++) {
            try {
                if (ignore[i - 1]) {
                    // 订单预估用车时间超出了任务时间范围
                    continue;
                }
                // 初始化收益
                dp[i] = init(i, profit, conflict, taken, kilo, distance);
                matrix[i - 1][i - 1] = 1;
                for (int j = i - 1; j > 0; j--) {
                    if (ignore[j - 1]) {
                        // 订单预估用车时间超出了任务时间范围
                        continue;
                    }
                    if (!conflict[j][i] || (taken[i - 1] && taken[j - 1])) {
                        // 待派订单i与订单j不冲突
                        // 订单i与订单j都是司机已接订单
                        double v = dp[j] + profitFunction.apply(i, j, this);
                        if (dp[i] < v) {
                            dp[i] = v;
                            System.arraycopy(matrix[j - 1], 0, matrix[i - 1], 0, i - 1);
                        }
                    }
                    if (taken[j - 1]) {
                        // 订单j是司机已接订单，则没必要在向前遍历
                        if (conflict[j][i] && !taken[i - 1]) {
                            // 待派订单i与司机已接订单j冲突
                            dp[i] = 0.0;
                            matrix[i - 1] = new int[n];
                        }
                        break;
                    }
                }
                if (dp[i] <= 0) {
                    // 订单i不能被司机接起，赋值成最小值，防止后向单又与i相连
                    dp[i] = Integer.MIN_VALUE;
                    matrix[i - 1] = new int[n];
                }
            } catch (Exception e) {
                dp[i] = Integer.MIN_VALUE;
                matrix[i - 1] = new int[n];
                logger.error("dp error", e);
                MetricsUtil.recordValue("dp.error", 1);
            }
        }
        Queue<Integer> queue = new PriorityQueue<>((i1, i2) -> {
            // 排序规则：两个值都大于基线时，小的靠前；其余情况，大的靠前
            if (dp[i1] >= line && dp[i2] >= line) {
                return Double.compare(dp[i1], dp[i2]);
            }
            return Double.compare(dp[i2], dp[i1]);
        });
        for (int i = n; i > 0; i--) {
            if (dp[i] == Integer.MIN_VALUE) {
                continue;
            }
            queue.offer(i);
        }
        int[][] result = new int[queue.size()][queue.size()];
        int i = 0;
        while (!queue.isEmpty()) {
            Integer r = queue.poll();
            if (r != null) {
                result[i++] = matrix[r - 1];
            }
        }
        return result;
    }



    private double init(int i, double[] profit, boolean[][] conflict, boolean[] taken, double[] kilo, double[][] distance) {
        if (!taken[i - 1] && conflict[0][i]) {
            // 待派订单不是司机已接订单
            return 0D;
        }
        return profitFunction.apply(i, 0, this);
    }

    public double[] profit() {
        return profit;
    }

    public double[] kilo() {
        return kilo;
    }

    public boolean[] taken() {
        return taken;
    }

    public boolean[] ignore() {
        return ignore;
    }

    public boolean[][] conflict() {
        return conflict;
    }

    public double[][] distance() {
        return distance;
    }

    public double[] dp() {
        return dp;
    }

    public int[][] matrix() {
        return matrix;
    }

    public interface ProfitFunction {

        double apply(int i, int j, DP dp);

    }

    /**
     * 订单数收入
     */
    public static class OrderProfitFunction implements ProfitFunction {

        @Override
        public double apply(int i, int j, DP dp) {
            return dp.profit()[i - 1];
        }
    }

    /**
     * 订单净收入
     */
    public static class OrderNetProfitFunction implements ProfitFunction {

        private static final double DISTANCE_CONVERSION_RATE = 0.5;

        @Override
        public double apply(int i, int j, DP dp) {
            return dp.profit()[i - 1] - (dp.kilo()[i - 1] + dp.distance()[j][i]) * DISTANCE_CONVERSION_RATE;
        }
    }
}
