package com.ctrip.dcs.dsp.delay.validator;

import com.ctrip.dcs.dsp.delay.enums.SupplyOrderStatus;
import com.ctrip.dcs.dsp.delay.enums.YesOrNo;
import com.ctrip.dcs.dsp.delay.model.SupplyOrder;
import com.ctrip.dcs.dsp.delay.qconfig.DelayDspCommonQConfig;
import com.ctrip.dcs.dsp.delay.util.SpringApplicationContextUtil;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.jackson.JacksonSerializer;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Component
public class ValidatorChain {

    private final Logger logger = LoggerFactory.getLogger(ValidatorChain.class);

    @Autowired
    private DelayDspCommonQConfig delayDspCommonQConfig;

    public ValidatorDTO validate(SupplyOrder supplyOrder) {
        ValidatorDTO validatorDTO = new ValidatorDTO();
        if (Objects.isNull(supplyOrder)) {
            return validatorDTO;
        }
        List<Validator> validators = buildValidator(supplyOrder);
        for (Validator validator : validators) {
            validatorDTO = validator.validate(supplyOrder);
            if (!Objects.equals(validatorDTO.getCode(), ValidatorCode.OK)) {
                logger.info("ValidatorChain.validate", "order id: {},validate code: {}", supplyOrder.getOrderId(), validatorDTO.getCode().name());
                return validatorDTO;
            }
        }
        return validatorDTO;
    }

    private List<Validator> buildValidator(SupplyOrder supplyOrder) {
        List<String> validatorNames;
        if (supplyOrder.isTaken() || supplyOrder.isSyncKmOrder()) {
            validatorNames = delayDspCommonQConfig.getDelayTakenValidatorsList();
        } else if (Objects.equals(supplyOrder.getShortDisOrder(), YesOrNo.YES.getCode())) {
            validatorNames = delayDspCommonQConfig.getShortDisDelayDspValidators();
        } else {
            validatorNames = delayDspCommonQConfig.getDelayDspValidatorsList();
        }
        logger.info("ValidatorChain.validatorNames", "validatorNames: {}", JacksonSerializer.INSTANCE().serialize(validatorNames));
        List<Validator> validators = Lists.newArrayList();
        for (String name : validatorNames) {
            try {
                Validator validator = SpringApplicationContextUtil.getBean(name, Validator.class);
                validators.add(validator);
            } catch (Exception e) {
                logger.error("ValidatorChain.buildValidator", "there is no Validator. name: {}, error: {}", name, e);
            }
        }
        return validators;
    }


}
