package com.ctrip.dcs.dsp.delay.enums;

/**
 * <AUTHOR>
 */
public enum DelayDspTaskType {
    DP,
    SD,
    KM;

    public static DelayDspTaskType of(String code) {
        for (DelayDspTaskType type : values()) {
            if (type.name().equalsIgnoreCase(code)) {
                return type;
            }
        }
        return DP;
    }


    public static DelayDspTaskType getType(String code) {
        for (DelayDspTaskType type : values()) {
            if (type.name().equalsIgnoreCase(code)) {
                return type;
            }
        }
        return null;
    }

    public static boolean isDP(String code) {
        return DP.name().equalsIgnoreCase(code);
    }

    public static boolean isKM(String code) {
        return KM.name().equalsIgnoreCase(code);
    }

    public static boolean isSD(String code) {
        return SD.name().equalsIgnoreCase(code);
    }
}
