package com.ctrip.dcs.dsp.delay.carconfig.value;

import com.ctrip.dcs.dsp.delay.carconfig.Value;
import com.ctrip.dcs.dsp.delay.util.DateUtil;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class DelayDspTaskValue implements Value<Date> {

    private Integer taskSeq;

    // HH:mm
    private String begin;

    // HH:mm
    private String end;

    private Integer hour;

    private Integer deadline;

    private String type;

    @Override
    public boolean match(Date date) {
        String bookTimeStr = DateUtil.formatDate(date,DateUtil.HOUR_MIN_FMT);
        return  this.getEnd().compareTo(this.getBegin()) > 0 ?
                (bookTimeStr.compareTo(this.getBegin()) >= 0 && bookTimeStr.compareTo(this.getEnd()) <= 0) :
                (bookTimeStr.compareTo(this.getEnd()) <= 0 || bookTimeStr.compareTo(this.getBegin()) >= 0);
    }

    public Date getBeginTime(Date day) {
        String s = DateUtil.formatDate(day, DateUtil.DAY_FMT);
        Date result = DateUtil.parseDate(s + " " + begin + ":00");
        while (result != null && result.after(day)) {
            result = DateUtil.addDays(result, -1);
        }
        return result;
    }

    public Date getEndTime(Date day) {
        String s = DateUtil.formatDate(day, DateUtil.DAY_FMT);
        Date result = DateUtil.parseDate(s + " " + end + ":59");
        while (result != null && result.before(day)) {
            result = DateUtil.addDays(result, 1);
        }
        return result;
    }
}
