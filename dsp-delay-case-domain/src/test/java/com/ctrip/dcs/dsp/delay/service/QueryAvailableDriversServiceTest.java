package com.ctrip.dcs.dsp.delay.service;

import com.ctrip.dcs.dsp.delay.gateway.DriverGateway;
import com.ctrip.dcs.dsp.delay.gateway.SelfDispatcherOrderGateway;
import com.ctrip.dcs.dsp.delay.model.AvailableDriver;
import com.ctrip.dcs.dsp.delay.model.DelayDspOrder;
import com.ctrip.dcs.dsp.delay.model.Driver;
import com.ctrip.dcs.dsp.delay.service.impl.QueryAvailableDriversServiceImpl;
import com.ctrip.dcs.dsp.delay.util.MetricsUtil;
import com.ctrip.igt.framework.common.metrics.MetricsFactory;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Map;

/**
 * <AUTHOR>
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest(value = {MetricsUtil.class, MetricsFactory.class})
public class QueryAvailableDriversServiceTest {

    @InjectMocks
    private QueryAvailableDriversServiceImpl queryAvailableDriversService;

    @Mock
    private SelfDispatcherOrderGateway dispatcherOrderGateway;

    @Mock
    private DriverGateway driverGateway;

    @Mock
    private DelayDspOrder delayDspOrder;

    @Mock
    private Driver driver;

    @Test
    public void test1() {
        AvailableDriver result = queryAvailableDriversService.query(Lists.newArrayList(delayDspOrder));
        Assert.assertNotNull(result);
    }

    @Test
    public void test2() {
        Map<String, String> map = Maps.newHashMap();
        map.put("1", "1");
        PowerMockito.when(dispatcherOrderGateway.queryAvailableDrivers(Mockito.any(), Mockito.any())).thenReturn(map);
        PowerMockito.when(driverGateway.query(Mockito.anySet())).thenReturn(Lists.newArrayList(driver));
        AvailableDriver result = queryAvailableDriversService.query(Lists.newArrayList(delayDspOrder));
        Assert.assertNotNull(result);
    }
}
