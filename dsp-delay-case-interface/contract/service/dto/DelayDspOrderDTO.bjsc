include '..\..\BaijiCommonTypes.bjsc'

namespace java 'com.ctrip.dcs.dsp.transport.inventory.service.api.dto'

class DelayDspOrderDTO {
    /*订单id*/
    string orderId;
    /*任务id*/
    long taskId;
    /*是否延后派*/
    int isDelay;
    /*延后派发时间*/
    string executeTime;
    /*最晚延后派发时间*/
    string executeTimeDeadline;
}

class DelayDspConfigInfo{
  /*城市ID*/
  long cityId;
  /*车型ID*/
  int carTypeId;
  /*预估用车时间yyyy-MM-dd HH:mm:ss*/
  string sysExpectBookTime;
  /*任务类型:DP,KM，不传默认DP*/
  list<string> taskTypes;
}


class DelayDspConfigResInfo{
  /*城市ID*/
  long cityId;
  /*车型ID*/
  int carTypeId;
  /*预估用车时间yyyy-MM-dd HH:mm:ss*/
  string sysExpectBookTime;
  /*任务截止时间 yyyy-MM-dd HH:mm:ss*/
  string taskDeadTime;
  /*任务结束时间yyyy-MM-dd HH:mm:ss*/
  string taskEndTime;
}




