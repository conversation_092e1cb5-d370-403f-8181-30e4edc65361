package com.ctrip.dcs.dsp.delay.factory;

import com.ctrip.dcs.dsp.delay.carconfig.value.DelayDspTaskValue;
import com.ctrip.dcs.dsp.delay.conflict.CheckCode;
import com.ctrip.dcs.dsp.delay.context.DelayDspContext;
import com.ctrip.dcs.dsp.delay.core.DP;
import com.ctrip.dcs.dsp.delay.core.KM;
import com.ctrip.dcs.dsp.delay.gateway.GeoGateway;
import com.ctrip.dcs.dsp.delay.model.DelayDspOrder;
import com.ctrip.dcs.dsp.delay.model.Driver;
import com.ctrip.dcs.dsp.delay.model.DriverAggregation;
import com.ctrip.dcs.dsp.delay.model.Route;
import com.ctrip.dcs.dsp.delay.qconfig.DelayDspCommonQConfig;
import com.ctrip.dcs.dsp.delay.service.ConflictService;
import com.ctrip.dcs.dsp.delay.util.JsonUtil;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Table;
import org.codehaus.jackson.type.TypeReference;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Comparator;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@RunWith(PowerMockRunner.class)
public class DPFactoryTest {

    @InjectMocks
    private DPFactory factory;

    @Mock
    private DelayDspOrder delayDspOrder;

    @Mock
    private DriverAggregation driverAggregation;

    @Mock
    private Driver driver;

    @Mock
    DelayDspCommonQConfig delayDspCommonQConfig;

    @Mock
    private ConflictService conflictService;

    @Mock
    private GeoGateway geoGateway;

    @Test
    public void test() {
        Mockito.when(delayDspCommonQConfig.isOrderProfitCity(Mockito.anyInt())).thenReturn(true);
        Mockito.when(geoGateway.queryRoute(Mockito.anyLong(), Mockito.any())).thenReturn(new Route("", Integer.MAX_VALUE, Integer.MAX_VALUE));
        Mockito.when(conflictService.checkConflict(Mockito.any(DelayDspContext.class), Mockito.any(DriverAggregation.class), Mockito.any(DelayDspOrder.class))).thenReturn(CheckCode.OK);
        Mockito.when(conflictService.checkConflict(Mockito.any(DelayDspContext.class), Mockito.any(DriverAggregation.class), Mockito.any(DelayDspOrder.class), Mockito.any(DelayDspOrder.class))).thenReturn(CheckCode.SERVICE_TIME_CONFLICT);
        String s = "{\"delayDspTask\":{\"taskId\":3942,\"cityCode\":\"beijing_city\",\"cityId\":1,\"carTypeId\":117,\"taskStatus\":1,\"taskSeq\":0,\"beginTime\":\"2024-09-28 06:00:00\",\"endTime\":\"2024-09-28 17:59:59\",\"executeTime\":\"2024-08-22 18:20:00\",\"executeTimeDeadline\":\"2024-09-27 23:00:00\",\"taskType\":\"DP\",\"taskVersion\":21,\"matchTime\":\"2024-08-22 01:24:24\",\"matchStatus\":1,\"configBeginTime\":\"06:00\",\"configEndTime\":\"17:59\"},\"orders\":[{\"orderId\":\"91254199591436302\",\"mainOrderId\":\"70125247804\",\"taskId\":3942,\"carTypeId\":117,\"cityCode\":\"beijing_city\",\"cityId\":1,\"skuId\":49748,\"channelId\":235390,\"sysExpectBookTime\":\"2024-09-28 09:15:00\",\"predictServiceStopTime\":\"2024-09-28 10:28:00\",\"categoryCode\":\"airport_pickup\",\"isOta\":0,\"kiloLength\":43.0,\"fromLongitude\":116.593409,\"fromLatitude\":40.079174,\"fromCoordsys\":\"GCJ02\",\"fromHash\":\"1tu9p211tbfs\",\"toLongitude\":116.27356,\"toLatitude\":39.907612,\"toCoordsys\":\"GCJ02\",\"toHash\":\"1tu8v2nzwtd8\",\"driverId\":\"\",\"driverOrderFee\":129.39,\"isCancel\":0,\"isDelay\":1,\"duid\":\"91254199591436302-v2.0-0-8889-1:1:1-************-2-5-0-0\",\"orderSource\":\"QUNAR\"}],\"driverAggregations\":[{\"driver\":{\"driverId\":\"3452468\",\"cityId\":1,\"cityCode\":\"beijing_city\",\"status\":2,\"carTypeId\":117,\"coopMode\":5,\"carLicense\":\"京A21011\",\"addressLongitude\":116.489407,\"addressLatitude\":39.617582,\"isEnergy\":1,\"workTimes\":[\"00:00~11:59\"],\"transportGroups\":[{\"transportGroupId\":2539,\"transportGroupName\":\"是是是\",\"transportGroupMode\":1006},{\"transportGroupId\":2723,\"transportGroupName\":\"测试2024\",\"transportGroupMode\":1003},{\"transportGroupId\":1476,\"transportGroupName\":\"回归报名制\",\"transportGroupMode\":1001},{\"transportGroupId\":253,\"transportGroupName\":\"测试境外进单配置01运力组\",\"transportGroupMode\":1003},{\"transportGroupId\":1699,\"transportGroupName\":\"测试备用调度电话\",\"transportGroupMode\":1003},{\"transportGroupId\":3795,\"transportGroupName\":\"北京-携程专车1002000\",\"transportGroupMode\":1006},{\"transportGroupId\":224,\"transportGroupName\":\"测试01运力组01\",\"transportGroupMode\":1001},{\"transportGroupId\":3998,\"transportGroupName\":\"香港到深圳\",\"transportGroupMode\":1003},{\"transportGroupId\":134,\"transportGroupName\":\"新增优先人工调度\",\"transportGroupMode\":1003},{\"transportGroupId\":239,\"transportGroupName\":\"何海龙测试兼职勿动\",\"transportGroupMode\":1002},{\"transportGroupId\":468,\"transportGroupName\":\"测试升级\",\"transportGroupMode\":1003},{\"transportGroupId\":249,\"transportGroupName\":\"运力组进单测试1\",\"transportGroupMode\":1003}],\"registerTransportGroupId\":2539},\"score\":{\"drivId\":\"3452468\",\"totalScore\":296.2,\"driverBaseScore\":300.0,\"carBaseScore\":0.0,\"activeScore\":1.4,\"orderServiceScore\":-8.2,\"extraBonusScore\":0.0,\"studyScore\":0.0},\"orders\":[{\"orderId\":\"881833283240602455\",\"mainOrderId\":\"36555519365\",\"taskId\":3942,\"carTypeId\":118,\"cityCode\":\"shanghai_city\",\"cityId\":2,\"skuId\":49767,\"channelId\":235390,\"sysExpectBookTime\":\"2024-09-28 10:15:00\",\"predictServiceStopTime\":\"2024-09-28 10:35:00\",\"categoryCode\":\"airport_pickup\",\"isOta\":0,\"kiloLength\":21.0,\"fromLongitude\":121.327447,\"fromLatitude\":31.192568,\"fromCoordsys\":\"GCJ02\",\"fromHash\":\"1tms65wgrx52\",\"toLongitude\":121.474556,\"toLatitude\":31.23277,\"toCoordsys\":\"GCJ02\",\"toHash\":\"1tms7j3y08d6\",\"driverId\":\"3452468\",\"driverOrderFee\":499.0,\"isCancel\":0,\"isDelay\":0,\"duid\":\"\",\"orderSource\":\"QUNAR\"}],\"outTimeOrders\":[]}],\"config\":{\"orderMileageValues\":[{\"startKilo\":0,\"endKilo\":99,\"startTime\":\"00:00\",\"endTime\":\"23:59\",\"value\":1.0}],\"driverProfitDayBaselineValue\":{\"standardProfit\":190,\"highProfit\":1000},\"lbsBufferValues\":[{\"startTime\":\"00:00\",\"endTime\":\"23:59\",\"valueType\":0,\"value\":0.0}],\"headTailLimitValue\":{\"headLimit\":-1.0,\"tailLimit\":-1.0},\"incomeStandardLine\":1000,\"headLimit\":-1.0,\"tailLimit\":-1.0}}";
        DelayDspContext delayDspContext = JsonUtil.fromJson(s, new TypeReference<DelayDspContext>() {
        });
        List<DelayDspOrder> list = Lists.newArrayList();
        list.addAll(delayDspContext.getOrders());
        list.addAll(delayDspContext.getDriverAggregations().get(0).getOrders());
        list.sort(Comparator.comparing(DelayDspOrder::getPredictServiceStopTime));
        DP dp = factory.create(delayDspContext.getDriverAggregations().get(0), list, delayDspContext);
        int[][] result = dp.execute();
        Assert.assertEquals(result.length, 2);
    }
}
