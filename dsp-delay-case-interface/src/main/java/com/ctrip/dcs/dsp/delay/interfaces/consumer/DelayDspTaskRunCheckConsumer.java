package com.ctrip.dcs.dsp.delay.interfaces.consumer;

import com.ctrip.dcs.dsp.delay.consts.CommonConstant;
import com.ctrip.dcs.dsp.delay.enums.DelayDspTaskStatus;
import com.ctrip.dcs.dsp.delay.enums.YesOrNo;
import com.ctrip.dcs.dsp.delay.model.DelayDspOrder;
import com.ctrip.dcs.dsp.delay.model.DelayDspTask;
import com.ctrip.dcs.dsp.delay.mq.MessageProducer;
import com.ctrip.dcs.dsp.delay.qconfig.DelayDspCommonQConfig;
import com.ctrip.dcs.dsp.delay.repository.DelayDspOrderRepository;
import com.ctrip.dcs.dsp.delay.repository.DelayDspTaskRepository;
import com.ctrip.dcs.dsp.delay.util.MetricsUtil;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.qmq.annontation.QmqLogTag;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.NeedRetryException;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * 检查延后派任务是否执行完成
 * <AUTHOR>
 */
@Component
public class DelayDspTaskRunCheckConsumer {

    private static final Logger logger = LoggerFactory.getLogger(DelayDspTaskRunCheckConsumer.class);

    @Autowired
    private DelayDspTaskRepository delayDspTaskRepository;

    @Autowired
    private DelayDspOrderRepository delayDspOrderRepository;

    @Autowired
    private MessageProducer messageProducer;

    @Autowired
    private DelayDspCommonQConfig delayDspCommonQConfig;

    @QmqLogTag(tagKeys = {"taskId"})
    @QmqConsumer(prefix = CommonConstant.DELAY_DSP_TASK_RUN_CHECK_SUBJECT, consumerGroup = CommonConstant.APP_ID)
    public void onMessage(Message message) {
        try {
            Long taskId = message.getLongProperty("taskId");
            if (taskId == 0L) {
                return;
            }
            DelayDspTask task = delayDspTaskRepository.queryByTaskId(taskId);
            if (Objects.isNull(task) || isComplete(task)) {
                return;
            }
            logger.warn("DelayDspTaskRunCheckConsumerInfo", "taskId:{} is not complete", taskId);
            MetricsUtil.recordValue("dispatch.delay.task.execute.timeout", 1);
            Map<String, Object> map = Maps.newHashMap();
            map.put("taskId", task.getTaskId());
            map.put("cityId", task.getCityId());
            map.put("carTypeId", task.getCarTypeId());
            map.put("taskType",task.getTaskType());
            messageProducer.sendMessage(CommonConstant.DELAY_DSP_TASK_RUN_SUBJECT, map);
            messageProducer.sendDelayMessage(CommonConstant.DELAY_DSP_TASK_RUN_CHECK_SUBJECT, map, delayDspCommonQConfig.getCheckTaskRunMillisecond());
        } catch (Exception e) {
            logger.warn("DelayDspTaskRunCheckConsumerError", e);
            MetricsUtil.recordValue("dispatch.delay.task.execute.timeout", 1);
            // 5秒后重试
            throw new NeedRetryException(System.currentTimeMillis() + 5 * 1000, "DelayDspTaskRunCheckConsumerRetry");
        }
    }

    public boolean isComplete(DelayDspTask task) {
        if (task == null) {
            throw new IllegalArgumentException("task is null");
        }
        if (Objects.equals(task.getTaskStatus(), DelayDspTaskStatus.EXECUTION_COMPLETE.getCode())) {
            return true;
        }
        List<DelayDspOrder> orders = delayDspOrderRepository.queryByTaskId(task.getTaskId(), YesOrNo.YES.getCode());
        return CollectionUtils.isEmpty(orders);
    }
}
