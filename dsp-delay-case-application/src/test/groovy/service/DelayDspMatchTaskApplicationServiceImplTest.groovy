package service

import com.ctrip.dcs.dsp.application.request.DispatchDelayDspTaskRequest
import com.ctrip.dcs.dsp.application.service.impl.DelayDspMatchTaskApplicationServiceImpl
import com.ctrip.dcs.dsp.delay.context.DispatcherManager
import com.ctrip.dcs.dsp.delay.enums.DelayDspTaskType
import com.ctrip.dcs.dsp.delay.enums.YesOrNo
import com.ctrip.dcs.dsp.delay.factory.DelayDspContextFactory
import com.ctrip.dcs.dsp.delay.gateway.SelfDriverOrderGateway
import com.ctrip.dcs.dsp.delay.model.*
import com.ctrip.dcs.dsp.delay.mq.MessageProducer
import com.ctrip.dcs.dsp.delay.qconfig.DSPScheduleMatchValueConfig
import com.ctrip.dcs.dsp.delay.qconfig.value.DSPScheduleMatchValueVO
import com.ctrip.dcs.dsp.delay.repository.DelayDspMatchTaskRepository
import com.ctrip.dcs.dsp.delay.repository.DelayDspOrderRepository
import com.ctrip.dcs.dsp.delay.repository.DelayDspTaskRepository
import com.ctrip.dcs.dsp.delay.service.DispatchTaskService
import com.ctrip.igt.framework.common.clogging.Logger
import org.assertj.core.util.Lists
import spock.lang.Specification
import spock.lang.Unroll

class DelayDspMatchTaskApplicationServiceImplTest extends Specification {
    def testObj = new DelayDspMatchTaskApplicationServiceImpl()
    def logger = Mock(Logger)

    def delayDspTaskRepository = Mock(DelayDspTaskRepository)
    def delayDspMatchTaskRepository = Mock(DelayDspMatchTaskRepository)
    def delayDspOrderRepository = Mock(DelayDspOrderRepository)
    def delayDspContextFactory = Mock(DelayDspContextFactory)
    def messageProducer = Mock(MessageProducer)
    def driverOrderGateway = Mock(SelfDriverOrderGateway)
    def dspScheduleMatchValueConfig = Mock(DSPScheduleMatchValueConfig)

    def dispatchTaskService = Mock(DispatchTaskService)


    def setup() {
        testObj.delayDspTaskRepository = delayDspTaskRepository
        testObj.delayDspMatchTaskRepository = delayDspMatchTaskRepository
        testObj.delayDspOrderRepository = delayDspOrderRepository
        testObj.delayDspContextFactory = delayDspContextFactory
        testObj.messageProducer = messageProducer
        testObj.driverOrderGateway = driverOrderGateway
        testObj.dspScheduleMatchValueConfig = dspScheduleMatchValueConfig

    }

    @Unroll
    def "test dispatch"() {
        given:
        delayDspTaskRepository.queryByTaskId(_) >> delayDspTask
        delayDspOrderRepository.queryByTaskId(_, _) >> delayDspOrders
        delayDspContextFactory.createDelayDspContextForMatch(_, _) >> delayDspContext
        DispatcherManager.register(DelayDspTaskType.DP, dispatchTaskService);
        dispatchTaskService.dispatch(_) >> DPDispatchResult

        when:
        def result = testObj.dispatch(requestType)

        then:
        result == expectedResult

        where:
        requestType                                | delayDspTask                                    | delayDspOrders                          | DPDispatchResult                                            | delayDspContext || expectedResult
        new DispatchDelayDspTaskRequest(taskId: 0) | null                                            | null                                    | null                                                        | null            || null
        new DispatchDelayDspTaskRequest(taskId: 0) | new DelayDspTask(taskStatus: 0)                 | null                                    | null                                                        | null            || null
        new DispatchDelayDspTaskRequest(taskId: 0) | new DelayDspTask(taskStatus: 4, taskVersion: 1) | null                                    | null                                                        | null            || null
        new DispatchDelayDspTaskRequest(taskId: 0) | new DelayDspTask(taskStatus: 4, taskVersion: 1) | Lists.newArrayList(new DelayDspOrder()) | new DispatchResult(Lists.newArrayList(new DelayDspOrder())) | null            || null
        new DispatchDelayDspTaskRequest(taskId: 0) | new DelayDspTask(taskStatus: 4)                 | Lists.newArrayList(new DelayDspOrder()) | buildDispatchResult1()                                      | null            || null
        new DispatchDelayDspTaskRequest(taskId: 0) | new DelayDspTask(taskStatus: 4)                 | Lists.newArrayList(new DelayDspOrder()) | buildDispatchResult2()                                      | null            || null

    }


    @Unroll
    def "test dealTaskMatchComplete"() {
        given:
        delayDspTaskRepository.queryByTaskId(_) >> delayDspTask
        delayDspMatchTaskRepository.query(_, _) >> delayDspMatchTaskRecords

        when:
        def result = testObj.dealTaskMatchComplete(1, 1)

        then:
        result == expectedResult

        where:
        delayDspTask                                    | delayDspMatchTaskRecords                          || expectedResult
        null                                            | null                                              || null
        new DelayDspTask(taskStatus: 2)                 | null                                              || null
        new DelayDspTask(taskStatus: 3, taskVersion: 1) | null                                              || null
        new DelayDspTask(taskStatus: 3, taskVersion: 1) | Lists.newArrayList(new DelayDspMatchTaskRecord()) || null

    }


    @Unroll
    def "test notMatchDriverOrderRedispatch"() {
        given:
        dspScheduleMatchValueConfig.getValue(_, _) >> dSPScheduleMatchValueVO
        delayDspOrderRepository.queryByOrderIdsWithTaskId(_, _, _) >> delayDspOrders

        when:
        def result = testObj.notMatchDriverOrderRedispatch(records, new DelayDspTask(taskStatus: 2, cityId: 1))

        then:
        result == expectedResult

        where:
        dSPScheduleMatchValueVO               | delayDspTask                    | delayDspOrders                                      | records                                                                        | delayDspContext || expectedResult
        null                                  | null                            | null                                                | null                                                                           | null            || null
        new DSPScheduleMatchValueVO(isOut: 0) | new DelayDspTask(taskStatus: 0) | null                                                | null                                                                           | null            || null
        new DSPScheduleMatchValueVO(isOut: 1) | new DelayDspTask(taskStatus: 0) | null                                                | Lists.newArrayList(new DelayDspMatchTaskRecord(matchSuccess: 1))               | null            || null
        new DSPScheduleMatchValueVO(isOut: 1) | new DelayDspTask(taskStatus: 0) | Lists.newArrayList()                                | Lists.newArrayList(new DelayDspMatchTaskRecord(matchSuccess: 0, orderId: "1")) | null            || null
        new DSPScheduleMatchValueVO(isOut: 1) | new DelayDspTask(taskStatus: 0) | Lists.newArrayList(new DelayDspOrder(orderId: "1")) | Lists.newArrayList(new DelayDspMatchTaskRecord(matchSuccess: 0, orderId: "1")) | null            || null


    }


    @Unroll
    def "test reachStandardDriverOutPool"() {
        given:
        delayDspContextFactory.queryPreOutPoolDrivers(_, _) >> outPoolDrivers
        when:
        def result = testObj.reachStandardDriverOutPool(records, new DelayDspTask(taskStatus: 2, cityId: 1))

        then:
        result == expectedResult

        where:
        dSPScheduleMatchValueVO               | delayDspTask                    | outPoolDrivers          | records                                                                                                                       | delayDspContext || expectedResult
        new DSPScheduleMatchValueVO(isOut: 1) | new DelayDspTask(taskStatus: 0) | null                    | Lists.newArrayList(new DelayDspMatchTaskRecord(matchSuccess: 0, isDelay: 0))                                                  | null            || null
        new DSPScheduleMatchValueVO(isOut: 1) | new DelayDspTask(taskStatus: 0) | null                    | Lists.newArrayList(new DelayDspMatchTaskRecord(matchSuccess: 1, isDelay: 0))                                                  | null            || null
        new DSPScheduleMatchValueVO(isOut: 1) | new DelayDspTask(taskStatus: 0) | null                    | Lists.newArrayList(new DelayDspMatchTaskRecord(matchSuccess: 1, isDelay: 1, driverId: 1, orderId: "1"))                       | null            || null
        new DSPScheduleMatchValueVO(isOut: 1) | new DelayDspTask(taskStatus: 0) | null                    | Lists.newArrayList(new DelayDspMatchTaskRecord(matchSuccess: 1, isDelay: 1, driverId: 1, orderId: "1"))                       | null            || null
        new DSPScheduleMatchValueVO(isOut: 1) | new DelayDspTask(taskStatus: 0) | Lists.newArrayList("1") | Lists.newArrayList(new DelayDspMatchTaskRecord(matchSuccess: 1, isDelay: 1, driverId: 1, orderId: "1"))                       | null            || null
        new DSPScheduleMatchValueVO(isOut: 1) | new DelayDspTask(taskStatus: 0) | Lists.newArrayList("1") | Lists.newArrayList(new DelayDspMatchTaskRecord(matchSuccess: 1, isDelay: 1, driverId: 1, orderId: "1", orderSource: "QUNAR")) | null            || null

    }

    @Unroll
    def "test occupancyInventory"() {
        given:
        delayDspOrderRepository.queryByTaskId(_, _) >> delayDspOrders

        when:
        def result = testObj.occupancyInventory(records, 1)

        then:
        result == expectedResult

        where:
        dSPScheduleMatchValueVO               | delayDspTask                    | delayDspOrders                                      | records                                                                                                                       | delayDspContext || expectedResult
        new DSPScheduleMatchValueVO(isOut: 1) | new DelayDspTask(taskStatus: 0) | null                                                | Lists.newArrayList(new DelayDspMatchTaskRecord(matchSuccess: 0, isDelay: 0))                                                  | null            || null
        new DSPScheduleMatchValueVO(isOut: 1) | new DelayDspTask(taskStatus: 0) | null                                                | Lists.newArrayList(new DelayDspMatchTaskRecord(matchSuccess: 1, isDelay: 0))                                                  | null            || null
        new DSPScheduleMatchValueVO(isOut: 1) | new DelayDspTask(taskStatus: 0) | Lists.newArrayList(new DelayDspOrder(orderId: "1")) | Lists.newArrayList(new DelayDspMatchTaskRecord(matchSuccess: 1, isDelay: 1, driverId: 1, orderId: "1", orderSource: "QUNAR")) | null            || null
        new DSPScheduleMatchValueVO(isOut: 1) | new DelayDspTask(taskStatus: 0) | Lists.newArrayList(new DelayDspOrder(orderId: "2")) | Lists.newArrayList(new DelayDspMatchTaskRecord(matchSuccess: 1, isDelay: 1, driverId: 1, orderId: "1", orderSource: "QUNAR")) | null            || null

    }


    DispatchResult buildDispatchResult1() {
        DispatchResult dispatchResult = new DispatchResult(Lists.newArrayList(new DelayDspOrder()))
        dispatchResult.getDetails().forEach {
            it.setMatchSuccess(YesOrNo.YES.code)
            it.setDriver(new Driver())
        }
        return dispatchResult
    }

    DispatchResult buildDispatchResult2() {
        DispatchResult dispatchResult = new DispatchResult(Lists.newArrayList(new DelayDspOrder()))
        dispatchResult.getDetails().forEach {
            it.setMatchSuccess(YesOrNo.YES.code)
            it.getOrder().setIsDelay(YesOrNo.YES.code)
            it.setDriver(new Driver())
        }
        return dispatchResult
    }
}
