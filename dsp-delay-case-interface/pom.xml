<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <!-- super pom -->
  <parent>
    <groupId>com.ctrip.dcs.dsp</groupId>
    <artifactId>dsp-delay-case</artifactId>
    <version>1.0.0</version>
  </parent>

  <!-- application module -->
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.ctrip.dcs.dsp</groupId>
  <artifactId>dsp-delay-case-interface</artifactId>
  <version>1.0.0</version>
  <name>dsp-delay-case-interface</name>
  <url>http://www.ctrip.com</url>

  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <maven.compiler.source>1.8</maven.compiler.source>
    <maven.compiler.target>1.8</maven.compiler.target>
  </properties>

  <!-- dependencies -->
  <dependencies>
    <dependency>
      <groupId>com.ctriposs.baiji</groupId>
      <artifactId>baiji-rpc-client</artifactId>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.ctrip.igt.framework</groupId>
      <artifactId>soa-common</artifactId>
    </dependency>
    <!-- app -->
    <dependency>
      <groupId>com.ctrip.dcs.dsp</groupId>
      <artifactId>dsp-delay-case-application</artifactId>
    </dependency>
    <!-- port -->
    <dependency>
      <groupId>com.ctrip.dcs.dsp</groupId>
      <artifactId>dsp-delay-case-infrastructure</artifactId>
    </dependency>
    <dependency>
      <groupId>com.ctrip.igt.framework</groupId>
      <artifactId>redis</artifactId>
    </dependency>
    <dependency>
      <groupId>com.ctrip.igt.framework</groupId>
      <artifactId>qconfig</artifactId>
    </dependency>
  </dependencies>
</project>
