package com.ctrip.dcs.dsp.delay.interfaces.consumer

import com.ctrip.arch.distlock.DLock
import com.ctrip.arch.distlock.redis.RedisDistributedLockService
import com.ctrip.dcs.dsp.application.service.DelayDspOrderApplicationService
import com.ctrip.dcs.dsp.delay.carconfig.value.DelayDspTaskValue
import com.ctrip.dcs.dsp.delay.infrastructure.geteway.InventoryGatewayImpl
import com.ctrip.dcs.dsp.delay.model.DelayDspTask
import com.ctrip.dcs.dsp.delay.repository.DelayDspTaskRepository
import com.ctrip.igt.framework.common.clogging.Logger
import com.ctrip.platform.dal.dao.helper.JsonUtils
import com.google.common.collect.Lists
import org.junit.Assert
import org.mockito.Mock
import org.springframework.beans.factory.annotation.Autowired
import qunar.tc.qmq.base.BaseMessage
import spock.lang.Specification
import spock.lang.Unroll


class DelayDspOutPoolTimeChangeConsumerTest extends Specification {
    def delayDspTaskRepository = Mock(DelayDspTaskRepository)
    def distributedLockService = Mock(RedisDistributedLockService)
    def lock = Mock(DLock)


    def delayDspOutPoolTimeChangeConsumer = new DelayDspOutPoolTimeChangeConsumer(delayDspTaskRepository: delayDspTaskRepository, distributedLockService: distributedLockService)

    @Unroll
    def "test onMessage"() {
        given:
        distributedLockService.getLock("DelayDspOutPoolTimeChangeConsumer") >> lock
        lock.tryLock() >> true
        when:
        def res = delayDspOutPoolTimeChangeConsumer.onMessage(message)

        then:
        res == expectedResult

        where:
        message           || expectedResult
        new BaseMessage() || null
        buildMessage1()   || null
        buildMessage2()   || null
    }


    @Unroll
    def "test changeTimeForIncompleteMatchTask"() {
        given:
        delayDspTaskRepository.batchQueryForChangeOutPoolTime(_, _, _, _, _) >> delayDspTasks
        lock.tryLock() >> true
        when:
        def res = delayDspOutPoolTimeChangeConsumer.changeTimeForIncompleteMatchTask(new Date(), new DelayDspTaskValue(hour: 12), "1")

        then:
        res == expectedResult

        where:
        delayDspTasks                                                                      || expectedResult
        null                                                                               || null
        Lists.newArrayList(new DelayDspTask(beginTime: new Date(), matchTime: new Date())) || null
    }


    @Unroll
    def "test changeTimeForCompleteMatchTask"() {
        given:
        delayDspTaskRepository.batchQueryForChangeOutPoolTime(_, _, _, _, _) >> delayDspTasks
        lock.tryLock() >> true
        when:
        def res = delayDspOutPoolTimeChangeConsumer.changeTimeForCompleteMatchTask(new Date(), new DelayDspTaskValue(hour: 12), "1")

        then:
        res == expectedResult

        where:
        delayDspTasks                                                                      || expectedResult
        null                                                                               || null
        Lists.newArrayList(new DelayDspTask(beginTime: new Date(), matchTime: new Date())) || null
    }


    BaseMessage buildMessage1() {
        BaseMessage message = new BaseMessage();
        Map<String, List<DelayDspTaskValue>> stringListMap = new HashMap<>();
        DelayDspTaskValue delayDspTaskValue = new DelayDspTaskValue();
        delayDspTaskValue.setType("KM")
        delayDspTaskValue.setTaskSeq(0)
        delayDspTaskValue.setHour(12)
        delayDspTaskValue.setDeadline(8)
        delayDspTaskValue.setEnd("23:59")
        delayDspTaskValue.setBegin("00:00")
        stringListMap.put("1111", Lists.newArrayList(delayDspTaskValue));
        message.setProperty("list", JsonUtils.toJson(stringListMap));
        return message;
    }

    BaseMessage buildMessage2() {
        BaseMessage message = new BaseMessage();
        message.setProperty("orderId", "1111");
        message.setProperty("driverId", "2222");
        return message;
    }
}
