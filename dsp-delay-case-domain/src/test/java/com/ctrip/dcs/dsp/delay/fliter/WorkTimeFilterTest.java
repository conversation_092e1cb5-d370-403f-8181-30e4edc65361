package com.ctrip.dcs.dsp.delay.fliter;

import com.ctrip.dcs.dsp.delay.carconfig.value.DelayDspTaskValue;
import com.ctrip.dcs.dsp.delay.factory.ModelFactory;
import com.ctrip.dcs.dsp.delay.filter.impl.WorkTimeFilter;
import com.ctrip.dcs.dsp.delay.model.DelayDspTask;
import com.ctrip.dcs.dsp.delay.model.Driver;
import com.ctrip.dcs.dsp.delay.util.DateUtil;
import com.google.common.collect.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.powermock.modules.junit4.PowerMockRunner;

/**
 * <AUTHOR>
 */
@RunWith(PowerMockRunner.class)
public class WorkTimeFilterTest {

    @InjectMocks
    private WorkTimeFilter filter;

    @Test
    public void testEmpty() {
        Driver driver = new Driver();
        DelayDspTask task = new DelayDspTask();
        boolean ok = this.filter.filter(driver, task);
        Assert.assertFalse(ok);
    }

    @Test
    public void test() {

        DelayDspTaskValue value = new DelayDspTaskValue();
        value.setBegin("08:00");
        value.setEnd("19:59");
        value.setTaskSeq(0);
        value.setHour(10);
        value.setDeadline(7);
        DelayDspTask task = ModelFactory.buildDelayDspTask("beijing_city", 1, 117, DateUtil.parseDate("2022-04-28 12:00:00"), value);

        Driver driver = new Driver();
        driver.setWorkTimes(Lists.newArrayList("08:00~19:59"));
        boolean ok = this.filter.filter(driver, task);
        Assert.assertTrue(ok);

        driver.setWorkTimes(Lists.newArrayList("07:10~20:59"));
        ok = this.filter.filter(driver, task);
        Assert.assertTrue(ok);

        driver.setWorkTimes(Lists.newArrayList("07:10~19:59"));
        ok = this.filter.filter(driver, task);
        Assert.assertTrue(ok);

        driver.setWorkTimes(Lists.newArrayList("08:00~20:59"));
        ok = this.filter.filter(driver, task);
        Assert.assertTrue(ok);

        driver.setWorkTimes(Lists.newArrayList("20:00~07:59"));
        ok = this.filter.filter(driver, task);
        Assert.assertFalse(ok);

        driver.setWorkTimes(Lists.newArrayList("08:00~18:59"));
        ok = this.filter.filter(driver, task);
        Assert.assertFalse(ok);

        driver.setWorkTimes(Lists.newArrayList("08:10~19:59"));
        ok = this.filter.filter(driver, task);
        Assert.assertFalse(ok);
    }
}
