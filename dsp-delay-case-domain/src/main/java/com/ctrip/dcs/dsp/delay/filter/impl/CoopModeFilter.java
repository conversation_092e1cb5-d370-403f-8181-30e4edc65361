package com.ctrip.dcs.dsp.delay.filter.impl;

import com.ctrip.dcs.dsp.delay.enums.CoopMode;
import com.ctrip.dcs.dsp.delay.model.DelayDspTask;
import com.ctrip.dcs.dsp.delay.model.Driver;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 */
@Component("coopModeFilter")
public class CoopModeFilter extends CommonFilter {

    @Override
    public boolean filter(Driver driver, DelayDspTask task) {
        return Objects.equals(driver.getCoopMode(), CoopMode.FULL_TIME_ASSIGN.getCode());
    }

    @Override
    public String reason() {
        return "not full time mode";
    }
}
