package com.ctrip.dcs.dsp.delay.model;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class DriverScore {

    private String drivId;

    private Double totalScore;

    private Double driverBaseScore;

    private Double carBaseScore;

    private Double activeScore;

    private Double orderServiceScore;

    private Double extraBonusScore;

    private Double studyScore;

    public DriverScore() {
    }

    public DriverScore(String drivId, Double totalScore, Double driverBaseScore, Double carBaseScore, Double activeScore, Double orderServiceScore, Double extraBonusScore, Double studyScore) {
        this.drivId = drivId;
        this.totalScore = totalScore;
        this.driverBaseScore = driverBaseScore;
        this.carBaseScore = carBaseScore;
        this.activeScore = activeScore;
        this.orderServiceScore = orderServiceScore;
        this.extraBonusScore = extraBonusScore;
        this.studyScore = studyScore;
    }

    public DriverScore(String drivId) {
        this.drivId = drivId;
        this.totalScore = 0.0;
        this.driverBaseScore = 0.0;
        this.carBaseScore = 0.0;
        this.activeScore = 0.0;
        this.orderServiceScore = 0.0;
        this.extraBonusScore = 0.0;
        this.studyScore = 0.0;
    }
}
