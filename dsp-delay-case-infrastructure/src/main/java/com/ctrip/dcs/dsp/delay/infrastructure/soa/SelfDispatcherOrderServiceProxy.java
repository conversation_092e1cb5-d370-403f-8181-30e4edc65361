package com.ctrip.dcs.dsp.delay.infrastructure.soa;

import com.ctrip.dcs.self.dispatchorder.client.SelfDispatchOrderServiceClient;
import com.ctrip.dcs.self.dispatchorder.interfaces.*;
import com.ctrip.igt.framework.soa.client.ServiceClient;

/**
 * <AUTHOR>
 */
@ServiceClient(value = SelfDispatchOrderServiceClient.class)
public interface SelfDispatcherOrderServiceProxy {

    ConfirmDelayDspOrderResponseType confirmDelayDspOrder(ConfirmDelayDspOrderRequestType request);

    ReDispatchSubmitResponseType reDispatchSubmit(ReDispatchSubmitRequestType request);

    QueryAvailableDriversResponseType queryAvailableDrivers(QueryAvailableDriversRequestType request);
}
