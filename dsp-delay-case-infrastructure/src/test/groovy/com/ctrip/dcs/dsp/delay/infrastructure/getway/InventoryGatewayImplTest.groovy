package com.ctrip.dcs.dsp.delay.infrastructure.getway

import com.ctrip.dcs.dsp.delay.infrastructure.geteway.InventoryGatewayImpl
import com.ctrip.dcs.dsp.delay.infrastructure.soa.TransportInventoryServiceProxy
import com.ctrip.dcs.dsp.delay.model.SupplyOrder
import com.ctrip.dcs.dsp.delay.util.MetricsUtil
import com.ctrip.dcs.dsp.delay.util.SpringApplicationContextUtil
import com.ctrip.dcs.self.transport.inventory.api.UseVirtualInventoryResponseType
import com.ctrip.igt.ResponseResult
import com.ctrip.igt.framework.common.metrics.MetricsFactory
import com.google.common.collect.Lists
import io.dropwizard.metrics5.MetricRegistry
import org.junit.runner.RunWith
import org.mockito.Mock
import org.powermock.api.mockito.PowerMockito
import org.powermock.core.classloader.annotations.PrepareForTest
import org.powermock.core.classloader.annotations.SuppressStaticInitializationFor
import org.powermock.modules.junit4.PowerMockRunner
import org.powermock.modules.junit4.PowerMockRunnerDelegate
import org.spockframework.runtime.Sputnik
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 */
@RunWith(PowerMockRunner.class)
@PowerMockRunnerDelegate(Sputnik.class)
@PrepareForTest([SpringApplicationContextUtil.class, MetricsUtil.class, MetricsFactory.class])
@SuppressStaticInitializationFor(["com.ctrip.dcs.dsp.delay.util.SpringApplicationContextUtil", "com.ctrip.dcs.dsp.delay.util.MetricsUtil", "com.ctrip.igt.framework.common.metrics.MetricsFactory"])
class InventoryGatewayImplTest extends Specification {

    def transportInventoryServiceProxy = Mock(TransportInventoryServiceProxy)

    def gateway = new InventoryGatewayImpl(transportInventoryServiceProxy: transportInventoryServiceProxy)

    @Mock
    private MetricRegistry metricRegistry;

    void setup() {
        PowerMockito.mockStatic(SpringApplicationContextUtil.class)
        PowerMockito.mockStatic(MetricsFactory.class)
        PowerMockito.mockStatic(MetricsUtil.class)
        PowerMockito.when(MetricsFactory.getMetricRegistry()).thenReturn(metricRegistry);
    }

    @Unroll
    def "checkInventoryConflict"() {

        given: "Mock数据"
        SupplyOrder order = new SupplyOrder()
        order.setSysExpectBookTime(new Date())
        order.setPredicServiceStopTime(new Date())
        order.setOrderId("1")

        transportInventoryServiceProxy.useVirtualInventory(_) >> buildUseVirtualInventoryResponseType()
        when: "执行校验方法"
        def finalRes = gateway.checkInventoryConflict(Lists.newArrayList("1"),order)

        then: "验证校验结果"
        finalRes == "true"
    }

    @Unroll
    def "checkInventoryConflict1"() {

        given: "Mock数据"
        SupplyOrder order = new SupplyOrder()
        order.setSysExpectBookTime(new Date())
        order.setPredicServiceStopTime(new Date())
        order.setOrderId("1")

        transportInventoryServiceProxy.useVirtualInventory(_) >> null
        when: "执行校验方法"
        def finalRes = gateway.checkInventoryConflict(Lists.newArrayList("1"),order)

        then: "验证校验结果"
        finalRes == null
    }

    UseVirtualInventoryResponseType buildUseVirtualInventoryResponseType(){
        UseVirtualInventoryResponseType useVirtualInventoryResponseType = new UseVirtualInventoryResponseType()
        useVirtualInventoryResponseType.setDriverId("true")
        ResponseResult responseResult = new ResponseResult()
        responseResult.setSuccess(true)
        useVirtualInventoryResponseType.setResponseResult(responseResult)
        return useVirtualInventoryResponseType
    }

}
